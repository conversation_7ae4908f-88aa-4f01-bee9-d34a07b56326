version: '3.4'

x-iam-variables:
  &iam-variables
  IAM_SECRET: "9yOq60-NIy7kqOYxJ4BC_IQMB6hiq4hp5Zs-QUjvppwxAJZEqZUJ"
  OWNER_EMAIL: "<EMAIL>"
  ROOT_ORG: "cairo-cloud-eg-local"
  IAM_HOST: "iam.cairo-cloud.eg.local"
  CALLBACK_URL: "http://cairo-cloud.eg.local/callback"
  MNJ_DYNAQUEUE_REDIS: "mnj-redis:6379"

services:
  mnj-app:
    build:
      context: ..
      dockerfile: Dockerfile.devenv
      args:
        VARIANT: "3.8"
        INSTALL_NODE: "false"
        NODE_VERSION: lts/*

    links:
      - "meneja-dev.gig.tech.local:iam.cairo-cloud.eg.local"
      - "meneja-dev.gig.tech.local:cairo-cloud.eg.local"

    environment:
      <<: *iam-variables
      PYTHONPATH: "/workspace/"
      MNJ_INSTANCE: "1"
      MNJ_DEBUG: "debug"
      MNJ_DATABASE: "meneja"
      IAM_DATABASE: "iam"
      MNJ_MONGODB: "mnj-db:27017"
      MNJ_DYNAQUEUE_REDIS: "mnj-redis:6379"
      MNJ_SECRET: "c30_IgyXRDph7hkhwTA-67PdX5hVb3hTvLcNFhEVAGpWHz-VsmgK"
      MNJ_TOKEN: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      SERVER_NAME: "meneja-dev.gig.tech.local"
      MINIO_URL: "http://mnj-minio:9000"
      EXTERN_MINIO_URL: "http://storage.meneja-dev.gig.tech.local"
      MINIO_ACCESS_KEY: "admin"
      MINIO_ACCESS_SECRET: "123456789"
      MINIO_READ_ONLY_KEY: "TASIAIJCCECVZDPRNLIJ"
      MINIO_READ_ONLY_SECRET: "DvcejFIc39Za5k/17JoPWzEfIwrAWo/iS7iQdfeV"
      MINIO_READ_ONLY_URL: "https://s3-qa-be-mac-1.meneja.gig-tech.cloudbuilders.be"
      GTLB_SERVER: "https://git.gig.tech"
      GTLB_G8S_PROJ: "gig-meneja/test-environments"
      GTLB_KNOWlEDGE_BASE_PROJ: "gig-meneja/knowledge_base"
      GTLB_TOKEN: "**************************"
      PIP_EXTRA_INDEX_URL: https://pypi.gig.tech
      TEST_IYO_URL: "https://staging.itsyou.online"
      TEST_IYO_JWT: "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      G8_DOCKER_REG_SERVER: "ghub.gig.tech"
      G8_DOCKER_REG_USER: "gigdeployer"
      G8_DOCKER_REG_PASSWD: "-Y9GqUXP9ucgch9UhZnJ"
      G8_MAILCLIENT_SERVER: "smtp.mandrillapp.com"
      G8_MAILCLIENT_PORT: "587"
      G8_MAILCLIENT_LOGIN: "<EMAIL>"
      G8_MAILCLIENT_PASSWD: "g5LzTokilBdLqT892-eR5Q"
      G8_MAILCLIENT_SENDER: "<EMAIL>"
      G8_JIRA_HOST: "https://gig-tech.atlassian.net"
      G8_JIRA_TOKEN: "************************"
      G8_JIRA_USERNAME: "<EMAIL>"
      ITSYOUONLINE_URL: "https://staging.itsyou.online"
      ENVIRONMENT_NAME: "meneja-dev"
      REQUESTS_CA_BUNDLE: "/etc/ssl/certs/ca-certificates.crt"
      MNJ_IAM_ORGANIZATION: meneja-test
      MNJ_CE_OPERATIONS_ORG: meneja-prd.cloud-enabler-operations
      OPERATIONS_ORG: greenitglobe.team.operations
      TESTING_G8: 'qt-gen-dc01-001'
      TEST_G8_NAME: "qt-gen-dc01-001"
      TEST_IMAGE_NAME:  "Ubuntu Server 20.04"
      IMAGE_TESTING_ACCOUNT_NAME: 'ImageTesting'
      GOOGLE_API_KEY: 'AIzaSyCzeXCByJTIHlRSsseqI0UnpVn8e2dXPoM'
      WG_CONFIG: "endpoints:

        - allowed-ips: [*************/32]

        \  host: ************:41194

        \  name: gw-mnj-prd-be:be-mac

        \  public-key: x8HiJJ8CTHP3WlJwBHM8Pgw/ZDSOhlab9WWTqLqQYGY=

        - allowed-ips: [*************/32]

        \  host: **************:41194

        \  name: gw-mnj-prd-at:nl-rmd

        \  public-key: +Gj5pHQ+Y3hXFzCFUZ08CAZv4l2LQ7bC8jtxysqpAR4=

        - allowed-ips: [*************/32]

        \  host: *************:41194

        \  name: gw-mnj-rd-ch:nl-rmd

        \  public-key: 2viXqaEj1jZErLb8FguN/Gx2UAXOsTbfWPevEqTjDAU=\n"
      OCTOPUS_API_BASE_URL: "https://octopus.gig.tech/api"
      OCTOPUS_TOKEN: "Hy3VYZtUx-zjal2DVd-gl0qnsHdFHWvpCyDpPrLqXnAdIBuMGo-FpFsUxHlbzDE2"
      PYDEVD_GDB_SCAN_SHARED_LIBRARIES: "libdl, libltdl, libc, libfreebl3"

    volumes:
    - ..:/workspace:cached
    - ../../vco_ui/admin_documentation:/workspace/mkdocs/admin_documentation:cached
    - ../../vco_ui/user_documentation:/workspace/mkdocs/user_documentation:cached
      # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    # network_mode: service:mnj-db

    # Uncomment the next line to use a non-root user for all processes.
    # user: node

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally. 
    # (Adding the "ports" property to this file will not forward from a Codespace.)

    networks:
      dev_net:
        ipv4_address: ***********

  mnj-node:
    build:
      context: ../../meneja_ui
      dockerfile: Dockerfile.node
    volumes:
      - ../../meneja_ui:/workspace:cached
    restart: unless-stopped
    networks:
      dev_net:
        ipv4_address: ***********

  vco-node:
    build:
      context: ../../vco_ui
      dockerfile: Dockerfile.node
    volumes:
      - ../../vco_ui:/workspace:cached
    restart: unless-stopped
    networks:
      dev_net:
        ipv4_address: ***********

  meneja-dev.gig.tech.local:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    networks:
      dev_net:
        ipv4_address: ***********

  iam-backend:
    environment:
      <<: *iam-variables
    image: ghub.gig.tech/gig-meneja/iam:master
    entrypoint:
      [
        "sh",
        "-c",
        "./iam -d -c mongodb://mnj-db:27017/iam --root-org $$ROOT_ORG --owner-email $$OWNER_EMAIL --api-key $$IAM_SECRET --iam-host $$IAM_HOST --callback-url $$CALLBACK_URL"
      ]
    # image: iam:debug
    # entrypoint: ["sh", "-c", "/go/bin/dlv debug /go/src/git.gig.tech/gig-meneja/iam --listen=:40000 --headless=true --api-version=2 --log -- -d -c mongodb://mnj-db:27017/iam --root-org $$ROOT_ORG --owner-email $$OWNER_EMAIL --api-key $$IAM_SECRET --iam-host $$IAM_HOST --callback-url $$CALLBACK_URL"]
    expose:
      - "8080"
      # - "40000"
      # - "1541"
    networks:
      dev_net:
        ipv4_address: ***********

  mnj-db:
    image: ghub.gig.tech/gig-meneja/meneja/mongo:3.7.2
    restart: unless-stopped
    volumes:
      - mnj-mongodb-data:/data/db
      - mnj-mongo-config:/data/configdb
    expose:
      - "27017"
    networks:
      dev_net:
        ipv4_address: ***********

  mnj-redis:
    image: redis:5.0.5-alpine
    restart: unless-stopped
    volumes:
      - mnj-redis-data:/data
    expose:
      - "6379"
    networks:
      dev_net:
        ipv4_address: ***********

  mnj-minio:
    image: minio/minio:latest
    command: "server /data"
    environment:
      MINIO_ACCESS_KEY: admin
      MINIO_SECRET_KEY: "123456789"
    restart: unless-stopped
    volumes:
      - mnj-minio-data:/data
      - mnj-minio-config:/root/.minio
    networks:
      dev_net:
        ipv4_address: ***********
  vdi-node:
    build:
      context: ../../vdi_ui
      dockerfile: Dockerfile.node
    volumes:
      - ../../vdi_ui:/workspace:cached
    restart: unless-stopped
    networks:
      dev_net:
        ipv4_address: ***********

volumes:
  mnj-mongodb-data:
  mnj-redis-data:
  mnj-mongo-config:
  mnj-minio-data:
  mnj-minio-config:

networks:
  dev_net:
    ipam:
      driver: 'default'
      config:
        - subnet: **********/24

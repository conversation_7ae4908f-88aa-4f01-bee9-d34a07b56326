ARG TAG=edge
ARG IMAGE=ghub.gig.tech/gig-meneja/meneja/meneja-base
ARG UI_IMAGE=ghub.gig.tech/gig-meneja/vco_ui/vco-ui-base
ARG DOCS_IMAGE=ghub.gig.tech/gig-meneja/vco_ui/vco-docs
ARG UI_TAG=latest
ARG VDI_IMAGE=ghub.gig.tech/gig-meneja/vdi_ui/vdi-ui-base
# ----

# hadolint ignore=DL3007
FROM ${UI_IMAGE}:${UI_TAG} as build
# hadolint ignore=DL3007
FROM ${DOCS_IMAGE}:${UI_TAG} as docs
FROM ${VDI_IMAGE}:${UI_TAG} as vdi-build

# ----

FROM ${IMAGE}:${TAG}

ENV PYTHONPATH "${PYTHONPATH}"
WORKDIR /usr/src/app

COPY . .
COPY --from=build /usr/src/app/dist meneja/server/static
COPY --from=vdi-build /usr/src/app/dist meneja/server/static/vdi/agent

COPY --from=docs /opt/admin_documentation/en/site meneja/server/templates/documentation/admin/en
COPY --from=docs /opt/admin_documentation/es/site meneja/server/templates/documentation/admin/es
COPY --from=docs /opt/admin_documentation/fr/site meneja/server/templates/documentation/admin/fr
COPY --from=docs /opt/admin_documentation/nl/site meneja/server/templates/documentation/admin/nl
COPY --from=docs /opt/admin_documentation/pt/site meneja/server/templates/documentation/admin/pt
COPY --from=docs /opt/admin_documentation/ug/site meneja/server/templates/documentation/admin/ug

COPY --from=docs /opt/admin_documentation/en/resources meneja/server/templates/documentation/resources_orig

COPY --from=docs /opt/admin_documentation/en/mkdocs.yml meneja/server/templates/documentation/admin/en
COPY --from=docs /opt/admin_documentation/es/mkdocs.yml meneja/server/templates/documentation/admin/es
COPY --from=docs /opt/admin_documentation/fr/mkdocs.yml meneja/server/templates/documentation/admin/fr
COPY --from=docs /opt/admin_documentation/nl/mkdocs.yml meneja/server/templates/documentation/admin/nl
COPY --from=docs /opt/admin_documentation/pt/mkdocs.yml meneja/server/templates/documentation/admin/pt
COPY --from=docs /opt/admin_documentation/ug/mkdocs.yml meneja/server/templates/documentation/admin/ug

COPY --from=docs /opt/admin_documentation meneja/server/templates/documentation/admin

COPY --from=docs /opt/user_documentation/en/site meneja/server/templates/documentation/user/en
COPY --from=docs /opt/user_documentation/es/site meneja/server/templates/documentation/user/es
COPY --from=docs /opt/user_documentation/fr/site meneja/server/templates/documentation/user/fr
COPY --from=docs /opt/user_documentation/nl/site meneja/server/templates/documentation/user/nl
COPY --from=docs /opt/user_documentation/pt/site meneja/server/templates/documentation/user/pt
COPY --from=docs /opt/user_documentation/ug/site meneja/server/templates/documentation/user/ug


RUN mv meneja/server/static/index.html meneja/server/templates/index_new.html
RUN mv meneja/server/static/vdi/agent/index.html meneja/server/templates/vdi_index.html

RUN pip install --upgrade pip==23.3.1
ENV PIP_INDEX_URL "https://pypi.gig.tech"
RUN python setup.py install
ENTRYPOINT ["menejas"]

# See here for image contents: https://github.com/microsoft/vscode-dev-containers/tree/v0.140.1/containers/python-3/.devcontainer/base.Dockerfile

# [Choice] Python version: 3, 3.8, 3.7, 3.6
ARG VARIANT="3.9"
FROM mcr.microsoft.com/devcontainers/python:3.9-bookworm

# [Option] Install Node.js
ARG INSTALL_NODE="true"
ARG NODE_VERSION="lts/*"
RUN if [ "${INSTALL_NODE}" = "true" ]; then su vscode -c "source /usr/local/share/nvm/nvm.sh && nvm install ${NODE_VERSION} 2>&1"; fi

# [Optional] If your pip requirements rarely change, uncomment this section to add them to the image.
COPY requirements.txt /tmp/pip-tmp/
COPY mkdocs-requirements.txt /tmp/pip-tmp/
RUN python3 -m venv mkdocs-env 
RUN . mkdocs-env/bin/activate && pip install --upgrade pip setuptools wheel Cython

RUN . mkdocs-env/bin/activate && pip install -r  /tmp/pip-tmp/mkdocs-requirements.txt
RUN pip3 --disable-pip-version-check --no-cache-dir install requests[security]==2.22.0 \
   && pip3 --disable-pip-version-check --no-cache-dir install --index-url https://pypi.gig.tech -r /tmp/pip-tmp/requirements.txt \
   && pip3 --disable-pip-version-check --no-cache-dir install py-spy==0.3.0 \
   && pip3 --disable-pip-version-check --no-cache-dir install ipython==7.13.0 \
   && pip3 --disable-pip-version-check --no-cache-dir install ipdb==0.13.4 \
   && pip3 --disable-pip-version-check --no-cache-dir install black==22.8.0 \
   && rm -rf /tmp/pip-tmp

# [Optional] Uncomment this section to install additional OS packages.
RUN curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add -
RUN apt-get update \
   && export DEBIAN_FRONTEND=noninteractive \
   && apt-get -y install --no-install-recommends redis-tools dirmngr gnupg apt-transport-https software-properties-common ca-certificates curl \
   && apt-get -y install --no-install-recommends ipython3 \
   && apt -y install libssl-dev \
   && apt -y install libffi-dev \
   && curl -fsSL https://pgp.mongodb.com/server-6.0.asc | gpg --dearmor -o /usr/share/keyrings/mongodb-archive-keyring.gpg \
   && echo "deb [ arch=amd64 signed-by=/usr/share/keyrings/mongodb-archive-keyring.gpg ] https://repo.mongodb.org/apt/debian bookworm/mongodb-org/6.0 main" \
      > /etc/apt/sources.list.d/mongodb-org-6.0.list \
   && apt-get update \
   && apt-get install -y --no-install-recommends mongodb-mongosh \
   && curl --silent https://github.com/wkhtmltopdf/packaging/releases/download/********-3/wkhtmltox_********-3.bookworm_amd64.deb -L -O \
   && apt-get install -y --no-install-recommends -f ./wkhtmltox_********-3.bookworm_amd64.deb \

   && apt -y install dnsutils \
   && apt -y install bind9 \
   && apt-get install -y --no-install-recommends libgl1-mesa-glx libglib2.0-dev
# RUN curl https://github.com/Percona-Lab/mongodb_consistent_backup/releases/download/1.4.1/mongodb-consistent-backup.debian9.x86_64 -L --output /usr/local/bin/mongodb-consistent-backup \
#    && chmod a+x /usr/local/bin/mongodb-consistent-backup

# [Optional] Uncomment this line to install global node packages.
# RUN su vscode -c "source /usr/local/share/nvm/nvm.sh && npm install -g <your-package-here>" 2>&1

EXPOSE 8080
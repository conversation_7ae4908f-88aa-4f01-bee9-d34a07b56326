# Meneja
G8 environment manager

* [Architecture](docs/Introduction.md#meneja-architecture)
* [Authentification](docs/Introduction.md#authentification)
* [Contribution guideline](docs/Introduction.md#best-practices)

## Setting up your development environment

### Setup

This project uses containers for creating the development. These are automatically setup by the "Remote - Containers" extension (`
ms-vscode-remote.remote-containers`) in vscode. This extension depends on the availability docker on your host. See https://docs.docker.com/engine/install/ for information on how to set it up on your computer. After installing the extension connect to the development environment by issueing `CMD+SHIFT+P` and then selecting `Remote-Containers: Reopen in container...`

### Containers

The following constainers will be created (see `.devcontainer/docker-compose.yml` for details):
- meneja_devcontainer_mnj-app_1: The container in which the developer environment will run.
- meneja_devcontainer_mnj-redis_1: Redis container for dynaqueue
- meneja_devcontainer_mnj-node_1: Container running the webpack development server
- meneja_devcontainer_mnj-db_1: Mongo container
- meneja_devcontainer_meneja-dev.gig.tech.local_1: Nginx reverse proxy container to combine the three webservers (app backend, minio & npm dev server) into one site
- meneja_devcontainer_mnj-minio_1: Minio container

> The meneja_devcontainer_mnj-db_1 container uses an image from the our own docker repository (`ghub.gig.tech/gig-meneja/meneja/mongo:3.7.2`) so you will have to login your docker to `ghub.gig.tech` to make that work. See https://docs.gitlab.com/ee/user/packages/container_registry/#authenticating-to-the-gitlab-container-registry for more information.

### meneja-dev.gig.tech.local

To run the whole stack locally, first start the development server in a terminal in vscode (make sure you are connected to the remote development container. Then add `meneja-dev.gig.tech.local` & `storage.meneja-dev.gig.tech.local` in your hosts file next to localhost (`127.0.0.1`). Finally open you browser and navigate to `http://meneja-dev.gig.tech.local/`.

The following debug server configurations are needed for testing most functionalities:
- `Meneja Server` will bring up meneja server configured in such a way that you will be able to debug it immediately.
- `Dynaqueue Server`
- `Dynaqueue Generic Worker`

For testing the monitoring and other scheduled tasks also start the following debug servers:
- `Dynaqueue Scheduler`
- `Dynaqueue Belgium Worker`
- `Dynaqueue Austria Worker`
- `Dynaqueue Swiss Worker`

For testing functionalities for building images also start the following debug servers:
- `Dynaqueue Imagebuilder Worker`

### cairo-cloud.eg.local

To run the whole stack locally, first start the development server in a terminal in vscode (make sure you are connected to the remote development container. Then add `cairo-cloud.eg.local` & `iam.cairo-cloud.eg.local` in your hosts file next to localhost (`127.0.0.1`). Finally open you browser and navigate to `http://cairo-cloud.eg.local/`.

To be able to login to the VCO portal:
1. In Meneja create a VCO with <NAME_EMAIL>
2. In `iam.cairo-cloud.eg.local`:
  a. Register a user with the <NAME_EMAIL>
  b. Log <NAME_EMAIL> user
  c. Accept invitation to the VCO organization that was created automatically

- `Cairo Cloud Server` will bring up vco server configured in such a way that you will be able to debug it immediately.

## Connecting to mongo from the devcontainer:

```bash
mongo --host mnj-db
```

## Connecting to minio 

The storage is served on http://storage.meneja-dev.gig.tech.local. You can login with access key `admin` and secret key `123456789`.

## Resetting your development environment.

Leave the development container and execute the following script from the root of the meneja repository.

```bash
bash scripts/reset-devenv.sh
```

## Notes

* When updating data model objects,
  1. Add a db migration script to  `meneja/model/db_migration` directory.
  2. Update `meneja/model/db_migration/__init__.py`:
    * Import your migration script
    * Increment `PRODUCTION_VERSION` value
    * Update `VERSIONS` dict with the new version and the imported method

  **Warning:** Never use `mongoengine` inside db migration scripts, only get and update collections with MongoDB client.

## Setting up Remote Meneja Environment

### Configuring Virtual machine

1. Create a virtual machine and when asking for configuration details add your local machine ssh public key in the SSH KEY 
2. Create port forward in your cloudspace.
3. Give it an external virtual firewall port for example 2222 and virtual machine port 22.
4. Create a user on the vm with the same username of your local machine.
5. Install docker add your user to docker group using `sudo usermod -aG docker <username>`.
6. Clone the meneja,meneja_ui,vco_ui directories on your VM in the same path as your local.
7. Setup your VPN on VM.

### Setting up SSH tunneling

1. Install the Remote-Conatiners extension in vscode.

2. On your local machine create a docker context using the created ssh in the first step
by executing

```
docker context create my-remote-docker-machine --docker "host=ssh://username@host:port"
```

 (replace "host" with your remote machine external virtual firewall ip, replace username with your VM username and replace port with the external virtual firewall port).
 then switch the new context
 
 ```
 docker context use my-remote-machine
 ```

 3. Open your meneja directory in vscode.

 4. Use the **Command Palette (Ctrl+Shift+P)** to issue the **Docker Context: Use command** then choose my-remote-machine context.

 5. Using the Remote-Containers extension use the `Reopen in container` command.

 6. After everything is built, type this command in your local machine's terminal `sudo ssh -p SSHPORT -L 80:localhost:80 -L 443:localhost:443  USERACCOUNT@CLOUDSPACEIP`(make sure to replace the SSHPORT to your external virtual firewall port, USERACCOUNT with your vm username, and CLOUDSPACEIP with your remote machine external virtual firewall ip).

 7. Then to be able to edit files inside the container execute this command in the remote container's terminal
 `sudo chown -R vscode /workspace`

 8. Before running make sure to add the  `meneja-dev.gig.tech.local` & `storage.meneja-dev.gig.tech.local` in your hosts file next to localhost (`127.0.0.1`).
  
 9. Finally, in your browser navigate to `http://meneja-dev.gig.tech.local/`.


### Integrate iam dev env:
1. Set `export TOKEN=<Your Gitlab CI token>` (used in Dockerfile.debug)
1. In IAM_UI dir `docker build . -t iam_ui`
2. In IAM dir go to file Dockerfile.debug and add `FROM iam_ui`
1. In IAM dir `docker build . -f Dockerfile.debug -t iam:debug`
1. In meneja dir: go to file docker-compose.yaml in meneja repo and uncomment lines of iam-backend service to set debug iam image
1. Delete your old iam container
1. Restart dev env
1. Make sure newly created iam container has image tag `iam:debug`
1. In iam container logs wait for `API server listening at: [::]:40000` to make sure the container is ready
1. Install `go` of required for IAM version on your local machine (Version 1.18)
1. Install DELVE debugger `go install github.com/go-delve/delve/cmd/dlv@v1.20.0`
1. Run debugger `Delve into Docker` from IAM repo. Terminal window should start showing debugger logs.

Now you can set breakpoints and start debugging.

Notes:
* If you make a change, you need to:
  * rebuild the iam image
  * restart iam pod
  * restart debugger
* If debugger crushes, you need to:
  * restart iam pod
  * restart debugger
* If debugger stuck after adding a breakpoint, you need to:
  * Pause debugger
  * Continue debugger

## Upgrade dynaqueue version

Meneja is using Dynaqueue client to communicate with [Dynaqueue component](https://git.gig.tech/openvcloud/dynaqueue).  Dynaqueue component can be upgraded by creating a new tag and upgrading the dynaqueue version with this tag name in the dynaqueue [charts](https://git.gig.tech/gig-meneja/charts) for all dynaqueue components: server, scheduler, worker.

Once a new tag is created and chart is updated, the Dynaqueue server will upgrade to the new version in the next meneja Upgrade (check this flow).

To upgrade Worker meneja and Scheduler components to use the new version of dynaqueue client, update the requirements in meneja repository.

## DEV env installation troubleshooting

### Error: Cannot resolve pypi.gig.tech

Can happen if you connected to vpn after starting docker. Solution: restart docker service

### Sqlalchemy Error when running servers

We use an old vertion of sqlalchemy==0.7.4, which doesn't install correctly with latest version of setuptools. What happens is that the python2 vertion of the package gets installed. For some reason when we build prd images it installs correctly.

If you don't need to use BI collection, you can just pump up the vertion of sqlalchemy to the latest (>2.0)

If you need to make changes for BI collection, you should enforce correct vertion installed. Steps:
1. install `setuptools` package instead the latest version you have. Multiple older versions might work, for example try `pip install setuptools==41.6.0`. Ignore incompatibility warnings as long as the packe is installed successfully.
2. remove and install again `sqlalchemy==0.7.4`

Now retry starting your servers, everything should work. Now you can again install the latest version of `setuptools`.

### IAM container is not accessible from dev container

Domain of iam container should point to nginx container IP address. Add to `/etc/hosts`:

`*********** iam.cairo-cloud.eg.local cairo-cloud.eg.local`

# CI/CD

By default, the deployment works for three clusters: `lab`, `qas`, and `prd`. 

## Deploying to a Different QAS Cluster

To deploy to a different QAS cluster, follow these steps:

1. **Change the CI Variable `QA_BRANCH`:**
   - Set the `QA_BRANCH` CI variable to a branch different than `master`.

2. **Add the New Kubeconfig:**
   - Add the new kubeconfig to the CI variable `qa_kubeconfig_name`.

This allows the deployment to target a different QAS cluster by using the specified branch and kubeconfig.

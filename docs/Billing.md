# Billing

Billing in Meneja/VCO portals is implemented through generic models. Each organization that represents either CE, VCO, Customer or Whitesky.cloud has a billing organization model.

Billing organization model contains information:
* billing details: company name, address, phone number, email, contact, vat number
* billing role: VCO, CE, GIG, Customer. If the same physical company owns multiple roles, multiple billing organization models will be created
* default selling pricing. Locations present in the default pricing are locations owned or being resoled by the billing organization.

When one billing organization is buying resources from another, a billing relation is created. Billing relation represents a one way relation between two billing organizations. If two companies purchase capacity from each other, these relationship will be represented with two billing organizations. Billing relation contains information:
* Selling billing organization
* Buying billing organization
* list of locations that are being perchaced. There are two types of consumption on g8s:
  * G8-based consumption. For G8s listed for this type, full G8 consumption will be included in the invoices. Typically used for CEs buying from whitesky.cloud
  * Account-based consumption. This will include list of accounts that the buyer is paying for. Typically used for customers, VCOs and CEs reselling capacity of locations that don't belong to them.
* Custom prices per G8 or per account depending of the consumption type
* payment terms configured between the parties

Creation of billing relations is a part of business logic and have to be handled with care. Some enteties have complex relations that are represented by multiple billing organizations and billing relations.When billing relations(RR) are created/updated:
* CE creation: creates BR whitesky.cloud->CE
* VCO creation: creates BR CE->VCO
* VCO customer creation: creates BR VCO->Customer
* Adding Customer location:
  * updates BR VCO->customer to set account used by this customer on g8
  * updates BR CE->VCO to set account used by the VCO's customer on g8
* New location creation: updates BR whitesky.cloud->CE with the new location

More complex relations are created for CE partners. When a location of CE1 is added to a VCO of CE2, CE1 and CE2 become partners. Location added to a CE partner is called a roaming location. Partner BRs scenarios:
* Adding roaming location. When location of CE1 is added to a VCO of CE2:
  * updates BR CE2->VCO: adds all customer accounts used by the VCO customers on the roaming location
  * updates BR CE1->CE2 with the same accounts that VCO's customers are using on this location. Each account is being resold from one CE to another
* Moving G8 from CE1 to CE2:
  * update billing organization CE1 to exclude G8 from it
  * update billing organization CE2 to include G8 to it
  * for each partner BR of CE1:
    * remove this G8 from BR CE1->Partner (if no other G8s are in the partnership, remove BR) 
    * create a new BR CE2->Partner (or if already exists, just add G8 to it)

Invoicing is performed by looping through all the active billing relations and calculating consumptions for the included g8s and accounts. Each BR results in a single invoice in a billing period.

# Meneja - Collector/cron - Logic

## cron

- User, cronjob is run under user: meneja
- Variables, refer to docs/VARIABLES.md
- Schedule, there is 2 min time shift between instances 1, 2 and 3.
-  Instance 1: 0,6,12,18,24,30,36,42,48,54 * * * * /opt/meneja/collector/cron.py > /var/tmp/meneja.log 2>&1
-  Instance 2: 2,8,14...
-  Instance 3: 4,10,16...

## Logic

- Every instance runs independent!
- State reported by RestMachine API: /system/health/getOverallStatus is the only factor determining system status.
  It will be represented by colours as follows:
-  EXPIRED: black
-  ERROR: red
-  WARNING: orange
-  OK: green
-  When any issue occured while query was executed: white

## Status reporting/notification
- Once instance pulled status from environments, it fetches System Status collected lately by other instances.
- Next it will calculate System Status per environment/schaduled time slot - 6min
- Hourly it will also calculate per hour stats.

## Pseudo code

```
- gigenvs = list_of_active_envs
- hours - list of hours: 0 - 23
- mins - list of timeslots
  (min being beginning of timeslot)
- dt = current date/time rounded to 6min slot
- gigenvds = list_of_active_envs plus dt
- run hcheck for all active envs.
```

### PI calculator 6min/Hour

```
- for hour in hours
-    for minx in mins
	calculate time_slot pi for gigenvs	# pi6min_calc
     calculate hourly pi for gigenvs		# pihour_calc
```

#### pi6min_calc

```
- from collection "sysstatus" fetch status of environments	# enstats
  reported within time slot >= "dt" amd < dt + 6min

- for stat in enstats
     for gigenv in gigenvs
	if envname in stat == reponame in gigenv
            set meneja instance id
            set time slot
            set timeslot/date
            set timeslot/time
	if state and realstate == EXPIRED
	    /* means G8 responded with state EXPIRED */
            set envname: reponame in gigenv
            set state: red
        if state in stat == EXPIRED
            set envname: reponame in gigenv
            set state: black
        else if state in stat == ERROR
            set envname: reponame in gigenv
            set state: red
        else if state in stat == WARNING
            set envname: reponame in gigenv
            set state: orange
        else if state in stat == OK
            set envname: reponame in gigenv
            set state: green
```

#### pihour_calc

```
- from collection "sysstatus" fetch status of environments	# enstats
  reported within time slot >= "dt" (round down 1 hour) abd < dt + 1 hour

- for stat in enstats
     for state in stat.data
        for gigenv in gigenvs
	   if envname in stat == reponame in gigenv and stat.data.state == "state"
                s6c[envname] += 1

- for state in s6c
     for stx in s6c.state
        if tstate == ""
           tstate = stx
           tcount = s6c[state][stx]
        if tcount <= s6c[state][stx]
           tstate = stx
           tcount = s6c[state][stx]
```

### Hcheck
```
- if env is active
-  hcheck
-   call system status check of env.		# sysstatus
    if call successful
       call to obtain list of env node ids	# idpull
       if call successful
          store node ids in collection "nodeids"
          for every env. node
              call to obtain detailed system status of node
              if call successful
                 store response in collection "nodestatus"
              else
                 store response/cause of error
       else
          store response/cause of error
    else
       store response/cause of error
-  verify list of registered issues		# gtlb_check
   if any issue registered/removed
      send notification				# notifier2
```

#### gtlb_check

```
- get current time slot				# "dt1"
- get previous time slot			# "dt2"

- if "gitlab" == "active"			# For current env
     stt0 = system status for dt1
     stt1 = system status for dt2
     if state was fetched for dt1 and dt2
         if stt1.state == EXPIRED
             close tickets with "OK" state
         else if stt1.state == ERROR
             close tickets with "OK" state
         else if stt1.state == WARNING
             close tickets with "ERROR" state
             close tickets with "OK" state
         else if stt1.state == "OK"
             close tickets with "EXPIRED" state
             close tickets with "ERROR" state
             close tickets with "WARNING" state

     	resp = get current state
     	if resp contains data:
            alertx = list of alerts to process	# gtlb_prcs
            if alertx >= 1				# were new issues detected/registered?
                send issues to GitLab/update list of issues	# gtlb_issue / "gtlbiids"
        else
	    resp = get current state "OK"
            if resp contains data
                generate "OK" notification
                if notification no registered	# gtlb_regchk
		    set/pass notification to notifier
	
```

#### notifier2

```
- old = check status of env. in previous time slot
- new = check status of env. in last time slot

- if old = cur
     if cur env state == "red"
         check if any new update/prepare telegram message
     if notify > 0 and telegram group set and updates are set on and there is an update
         send telegram message to group
- else						# of env. status has changed
    prepare status change info for group
    if cur env state == "red"
       add list of registered issues
     if notify > 0 and telegram group and notify are set
         send telegram message to group
    prepare status change info for channel
     if notify > 0 and telegram channel and notify are set
         send telegram message to channel

```

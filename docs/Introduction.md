# Introduction

## Meneja Architecture

Meneja project relies on four application layers (see the diagram below):

* User Interface (UI)
* API layer
* Business layer
* Data layer

Code base:

* [Meneja UI](https://git.gig.tech/gig-meneja/meneja_ui) - separate repository for the UI component
* [Meneja](https://git.gig.tech/gig-meneja/meneja) - API, Business, and Data layers.

![Application layers](application-layers.jpg "Meneja application layers")

### User Interface

A real-time graphical user interface in Meneja is implemented in `vuejs` and stored in a dedicated [repository](https://git.gig.tech/gig-meneja/meneja_ui).
Ways to update the UI view:

* Fetch data from the back end by consuming Meneja APIs
* Use WebSockets to interact directly to the Business layer when certain data gets updated.

API client is autogenerated from OpenAPI (swagger) Specification defined in path `swagger_api_definition.json`.

``` sh
node generate_api_client.js
```

### API Layer

* Responsible for Authentification and authorization
* Calls the methods of the Business Layer.

API client is defined with `flask_restx`.

### Business Layer

* Validates and process the input passed from API layer
* Updates UI view using WebSockets
* Calls methods of the Data Layer

### Data layer

* Provides Interfaces to the DB
* Defines Data models
* Defines queries

Methods of the Data layer should only be called from the Business layer.
Meneja uses MongoDB to store data.

## Authentification

To access meneja UI and meneja API you need an account in https://ityou.online. To make API calls you need to authenticate with a JWT token. You can copy your JWT from meneja UI on API page. Alternatively you can [generate a JWT yourself](JWT.md).

| Environment | Authenticate against | Admin Access |
|-------------|----------------------|-------------|
| QA | https://staging.itsyou.online/ | member of: meneja, greenitglobe.environments|
| Production | https://itsyou.online/ | member of: meneja-test, greenitglobe.environments|

## Jobs and Workflows

Meneja can either execute tasks in its own environment or schedule them in the queue of the [dynaqueue server](https://git.gig.tech/openvcloud/dynaqueue).

Executed in Meneja environment:

* Short tasks that do not make any external API calls

Schedule tasks in [dynaqueue server](https://git.gig.tech/openvcloud/dynaqueue):

* Tasks with long execution time or/and tasks that call external APIs. To schedule a method in dynaqueue, decorate the method

  ``` py
  from meneja.jobs import job
  @job(title="Title of the job")
  def foo():
    ...
  ```

* Sequence of tasks that should be executed in the specific order be queued with other tasks. Such sequence is called a workflow. Find examples of the workflows in [`meneja/jobs/`](https://git.gig.tech/gig-meneja/meneja/-/tree/master/meneja/jobs)
* Periodic jobs. To add a periodic job, decorate the method of this job and set periodicity in `cron` format

  ``` py
  from dynaqueue.scheduler import schedule
  ...
  @schedule(cron="0 10 * * *", description="Job description")
  def some_reocurring_task()
  ```

Dynaqueue component that triggers periodic jobs: Dynaqueue Scheduler.
Eventually, all tasks are executed with Dynaqueue workers.

## CI/CD

Deployment of Meneja is a result of running a pipeline of [Meneja](https://git.gig.tech/gig-meneja/meneja) defined in [.gitlab-ci.yml](https://git.gig.tech/gig-meneja/meneja/-/blob/master/.gitlab-ci.yml).

## Development flow

* Each commit to the Meneja repo triggers:
  * Build of the new images
  * Linting check
  * Unit tests
  * System tests
  Result: your code is verified
* Each merge to the master branch triggers an additional step: upgrade of QA meneja deployment in a QA kubernetes cluster .
* Upgrade of the Meneja production kubernetes cluster is possible only after the changes were successfully deployed to the QA kubernetes cluster. To perform the upgrade open the pipeline view and manually trigger the last step of the pipeline `prd_deploy`.

### Best practices

When contributing to the Meneja project:

* Ensure that each public method you add/edit contains an up-to-date docstring
* Use type-hinting
* Ensure that most of your code can be tested in the unit-tests, add unit tests when you add new logic in `/meneja/tests/`.
* Ensure if you add a new API call, that you also add a test for it in `/tests/api/`
* Add your changes on your branch and open a merge request
* Merge the merge requests only if you have at least one approval

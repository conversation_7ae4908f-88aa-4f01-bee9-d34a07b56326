# How to authenticate a request to meneja using a JWT

**See `scripts/generate_jwt.sh` for a script that helps you.**

## Step 1 gather your personal credentials
Log in on itsyou.online and create an api key in your personal settings page.
![New api key](iyo_apikey.png)

After pressing the CREATE button, copy the application id and secret into an environment variable

```bash
CLIENT_ID=F1_abc6fL19avj-aK28RAID87LGE # Application ID field
SECRET=6H6tpy_vFs0dzE82DrnlZ2W6xdLD    # Secret field
```

## Step 2 Generate a refreshable JWT to authenticate against meneja
Meneja authenticates people using an organization in itsyou.online. Only people that are at least member of the organization will be granted access. For the qa environment (https://meneja-qa.gig.tech) this is the **meneja-test** organization. When authenticating to meneja using a JWT, the JWT must contain proof that you are a member of this organization. Hence you need to add the **user:memberof:meneja-test** scope to the JWT creation request. <PERSON><PERSON><PERSON> also needs the offline_access scope to be present in the JWT, which makes men<PERSON>a able to extend the validity of the JWT by refreshing it. This results in the following curl request to create your refreshable JWT:

```bash
JWT=`curl -X POST "https://itsyou.online/v1/oauth/access_token?grant_type=client_credentials&client_id=${CLIENT_ID}&client_secret=${SECRET}&response_type=id_token&scope=user:memberof:meneja-test,offline_access"`
```

## Step 3 Using the JWT in a request to meneja
To authenticate a request using a JWT, you need to add an Authorization header as follows:

```bash
curl --header "Authorization: bearer ${JWT}" "https://meneja-qa.gig.tech"
```

## Step 4 Refreshing an expired JWT
Instead of recreating a JWT its better to refresh it. Refreshing a JWT has much less impact on the database of itsyou.online. Also it permits people to keep using a given grant without you needing to pass on your itsyou.online api key details, which is very dangerous. Refreshing happens as follows:

```bash
OLD_JWT_TOKEN=${JWT}
JWT=`curl -H "Authorization: bearer ${OLD_JWT_TOKEN}" https://itsyou.online/v1/oauth/jwt/refresh`
```
## How to connect to the minio server

The configmap ```system-config``` has two different urls to connect to the minio server:
- Internal url ```(example: http://meneja-minio:9000)```
- External url ```(example: https://storage-qa-meneja.gig.tech)```

The meneja server should connect to the minio using the external url to be able to generate the presigned url using the public domain.
and the internal url can be used inside the dynaqueue worker or anywhere else where there is no need to generate shareable links.

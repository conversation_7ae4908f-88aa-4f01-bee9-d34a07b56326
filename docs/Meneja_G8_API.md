## How to call APIs from G8 API
1. Create an entry in `meneja/business/g8/g8_api_mappings/mappings.yaml`
    * Should have `help`, `method_name`, `args`, `response_model_name` and `response` tags
    * `help` tag corresponds to method docstring
    * `method_name` name of the method to be generated on the G8Client class
    * `response_model_name` Used when generating output model for API. Use PasCal case
    * `args`
        * Tag (key) name is the name on the meneja side 
        * `g8_key` is the name on the G8 API
        * `help` is the information about the value, used in docstring and arg parser
        * `required` whether the field is required
        * `default` default value, ignored if `required` is `True`. 
        * `parser_skip` set to `True`, if choices needed for `API`, or this parameter is part of the API route and should not be included in the API parser, or you fo not want to expose parameter to the API at all
        * `type` type to be expected
    * `response`, this is recursive/nested
        * `name` name to be returned to meneja, must be provided if inside dict. if under list or under root level, not providing name would result in the scalar value not in a dictionary. 
            `name` is usually `null` on top-level `lists` or `dicts`
        * Not providing `name` on a field of type `dict` inside another `dict` will flatten that dictionary to its parent.
        * `type`
            * `int`, `str`, `bool` and `float` are the current atomic values
            * `list`, expects an `element` tag that contains any of the types under it
            * `dict`, expects `fields` tag, under which there can be any type
        * `hidden` if `True`, field will not be used when generating output model, and hence, will be hidden from API 
2. To generate the `G8Client` class at `meneja/business/g8/g8_api.py`
    
    In your development environment execute `Generate legacy G8 APIs` process in RUN&DEBUG configuration

3. Restart `Dynaqueue Server` and `Dynaqueue Generic Worker`
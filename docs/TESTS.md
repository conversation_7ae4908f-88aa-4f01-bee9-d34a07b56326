# Meneja - Automated Tests

- Tests are now a part of Meneja Pipeline and will be executed for each build
- Python `unittests` is used to develop tests and `nosetests` is used for execution 
- Test scripts location: `<project_root>/tests/`
- Tests are being executed automatically based on definition in `.gitlab-ci.yml` job: `meneja_unit_tests`


## Manual execution
 To start tests locally: 
 - Ensure nose is installed (`pip install nose`)
 - Ensure there is a running mongodb instance with environment info (e.g. the one located at `scripts/mongodb_bootstrap`)
 - Set environment variable `MNJ_MONGODB` to point to mongodb (e.g. `localhost:27017`)
 - In command line run the command:
 
    `nosetests -v <project_root>/tests/test_endpoints.py`
    
## Notifier test setup
Notifier testing requires test data to be prepared in a way so various scenarios can be covered with tests. 
For that purpose, mock data is inserted to MongoDB before test run. 
Test flow: 
1. test_notifier.py is started: 
e.g. `nosetests -v <project_root>/tests/test_notifier.py`
2. In `setUp` method, datetime is initialized the same way it's done in Meneja
3. `load_pi6min` method inserts 6 objects to pi_6min collection in mongoDB:
    first 3 objects are mocking environment state of T-12minutes for each of 3 meneja instances
    second 3 objects are mocking environment state of T-6minutes for each of 3 meneja instances
    
    Environment state in first and second set of data is configured to simulate environment state change. 
    E.g. Environment state was GREEN in T-12 and it changed to RED in T-6. In that case, we expect alert to be triggered. 
4. `gigenv` dictionary is initialized. Default fields are stored in `set_default_gigenv_fields` method in `test_lib.py`. This is done just to move repeated code away from test cases
5. Each method defined below and starts with `test_` is considered a test case and will be executed. 
6. In general, test case flow is as follows: 
 - Initialize gigenv with desired environment name. This reset all gigenv fields to default, in case previous tests modified it.
 - Environment name is used only to identify environment state in MongoDB. So, `non-existing environment names can be added only for testing purposes`.
 - How to choose which environment name to use in additional tests? - Check data that will be inserted to database and decide what status change suits your test case. 
 - Invoke notifier2 method which triggers alerts if needed
 - Verify if alert was triggered or not (i.e. if add_job method is called)

Note:  TearDown method is empty as tests are intended to be run during build stage, and not on live system. Hence, test data is stored in temporary mongodb and will be destroyed after stage is done.




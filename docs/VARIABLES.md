# Meneja Docker Container Variables

## Meneja Env variables

```bash
MNJ_VER="2.0.1"
VENV_ROOT="/home/<USER>/.venv"
PATH="${VENV_ROOT}/sbin:${VENV_ROOT}/bin:${PATH}"

MNJ_INSTANCE="1"					# or 2 or 3 - Meneja Instance identifier.
MNJ_DEBUG="none"					# none - normal use, debug - debuging purposes

MNJ_CBKURL="https://meneja.gig.tech/callback"		# IYO Callback
MNJ_IAM_ORGANIZATION="meneja-ops"					# IYO Organization
MNJ_SECRET="..................."			# IYO API Key

MNJ_TOKEN='...........................'			# Generated by JWT on the base of Settings API Keys
							# Application ID:	CLIENT_ID
							# Secret:		CLIENT_SECRET
							# Scope:		offline_access

MNJ_MONGODB=""          # Mongodb URI, default is localhost:27017
MNJ_MONGORS=""          # Mongodb replica set name

MINIO_URL="localhost:9000"     # URL to minio instance (without schema)
MINIO_ACCESS_KEY=""                   # accesskey to connect to minio instance
MINIO_ACCESS_SECRET=""                # accesssecret to connect to minio instance
```

# CRON / collector Variables

```bash
MNJ_TOKEN='..........................'			# As above

TLGM_TOKEN=..........................			# Telegram Bot Token
TLGM_GROUP=..........................			# Telegram Group ID
TLGM_CHANNEL=.........................			# Telegram MGNT Channel ID

TLGM_NOTIFY=1
TLGM_UPDATE=1

MNJ_INSTANCE=1	 					# or 2 or 3 - Instance identifier
MNJ_DEBUG=none						# As above
GTLB_SERVER=https://git.gig.tech 			# gitlab server
GTLB_G8S_PROJ=gig-meneja/environments       # gitlab g8s project
GTLB_REPO=https://git.gig.tech/gig-meneja/monit-test/issues/&222 # GitLab Repo URL & Project ID
GTLB_TOKEN=.........................			# GitLab token.
MNJ_MONGODB=""          # Mongodb URI, default is mongodb://localhost:27017/

```

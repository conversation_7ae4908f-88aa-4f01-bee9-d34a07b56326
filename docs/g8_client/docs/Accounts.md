# Accounts

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**acl** | [**list[AccountsACE]**](AccountsACE.md) | Access control list | [optional] 
**creation_timestamp** | **int** | Account creation time epoch | [optional] 
**id** | **int** | Account identifier | [optional] 
**name** | **str** | Account name | [optional] 
**resource_limits** | [**AccountsResourceLimits**](AccountsResourceLimits.md) |  | [optional] 
**status** | **str** | Account status | [optional] 
**update_timestamp** | **int** | Last account update time epoch | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



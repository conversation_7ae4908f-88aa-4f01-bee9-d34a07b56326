# AccountsAccountDetailsStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Account unique identifier | 
**name** | **str** | Name | 
**status** | **str** | Status | 
**creation_timestamp** | **int** | Create timestamp | 
**update_timestamp** | **int** | Update timestamp | 
**limits** | [**AccountsAccountDetailsStructLimits**](AccountsAccountDetailsStructLimits.md) |  | 
**group_access** | [**list[AccountsACEGroupStruct]**](AccountsACEGroupStruct.md) | List containing all group accesses | 
**user_access** | [**list[AccountsACEUserStruct]**](AccountsACEUserStruct.md) | List containing all user accesses | 
**cloudspaces** | [**list[AccountsCloudSpace]**](AccountsCloudSpace.md) | List of cloudspaces | 
**objectspaces** | [**list[AccountsObjectSpace]**](AccountsObjectSpace.md) | List of objectspaces | 
**images** | [**list[AccountsImage]**](AccountsImage.md) | List of images | 
**unattached_disks** | [**list[AccountsUnattachedDisk]**](AccountsUnattachedDisk.md) | List of unattached disks | 
**audits** | [**list[Audit]**](Audit.md) | list of audits by this account | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



# meneja.lib.clients.g8.lib.AccountsApi

All URIs are relative to *https://localhost/api/1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_g8_account_consumption**](AccountsApi.md#get_g8_account_consumption) | **GET** /accounts/{account_id}/consumption | Get account consumption
[**get_g8_admin_account_by_id**](AccountsApi.md#get_g8_admin_account_by_id) | **GET** /accounts/{account_id} | Get account by id
[**list_g8_admin_accounts**](AccountsApi.md#list_g8_admin_accounts) | **GET** /accounts | List Accounts


# **get_g8_account_consumption**
> AccountsAccountConsumption get_g8_account_consumption(account_id, x_fields=x_fields)

Get account consumption

Get consumption of a specific Account

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.AccountsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
account_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get account consumption
    api_response = api_instance.get_g8_account_consumption(account_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling AccountsApi->get_g8_account_consumption: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **account_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**AccountsAccountConsumption**](AccountsAccountConsumption.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_g8_admin_account_by_id**
> AccountsAccountDetailsStruct get_g8_admin_account_by_id(account_id, x_fields=x_fields)

Get account by id

Get details of a specific Account

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.AccountsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
account_id = 56 # int | 
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # Get account by id
    api_response = api_instance.get_g8_admin_account_by_id(account_id, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling AccountsApi->get_g8_admin_account_by_id: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **account_id** | **int**|  | 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**AccountsAccountDetailsStruct**](AccountsAccountDetailsStruct.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_g8_admin_accounts**
> AccountsPagination list_g8_admin_accounts(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, ids=ids, name=name, status=status, x_fields=x_fields)

List Accounts

Lists Accounts in the G8

### Example
```python
from __future__ import print_function
import time
import meneja.lib.clients.g8.lib
from meneja.lib.clients.g8.lib.rest import ApiException
from pprint import pprint

# Configure API key authorization: Bearer
configuration = meneja.lib.clients.g8.lib.Configuration()
configuration.api_key['Authorization'] = 'YOUR_API_KEY'
# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['Authorization'] = 'Bearer'

# create an instance of the API class
api_instance = meneja.lib.clients.g8.lib.AccountsApi(meneja.lib.clients.g8.lib.ApiClient(configuration))
limit = 25 # int | Flag to limit the amount of results. (optional) (default to 25)
start_after = 56 # int | Start returning records after index (optional)
sort_by = 'sort_by_example' # str | sorting field (optional)
sort_direction = 1 # int | sorting direction. 1 for asc and -1 for desc (optional) (default to 1)
ids = [56] # list[int] | search ids separated by a comma (optional)
name = 'name_example' # str | search status (optional)
status = 'status_example' # str | search status (optional)
x_fields = 'x_fields_example' # str | An optional fields mask (optional)

try:
    # List Accounts
    api_response = api_instance.list_g8_admin_accounts(limit=limit, start_after=start_after, sort_by=sort_by, sort_direction=sort_direction, ids=ids, name=name, status=status, x_fields=x_fields)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling AccountsApi->list_g8_admin_accounts: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| Flag to limit the amount of results. | [optional] [default to 25]
 **start_after** | **int**| Start returning records after index | [optional] 
 **sort_by** | **str**| sorting field | [optional] 
 **sort_direction** | **int**| sorting direction. 1 for asc and -1 for desc | [optional] [default to 1]
 **ids** | [**list[int]**](int.md)| search ids separated by a comma | [optional] 
 **name** | **str**| search status | [optional] 
 **status** | **str**| search status | [optional] 
 **x_fields** | **str**| An optional fields mask | [optional] 

### Return type

[**AccountsPagination**](AccountsPagination.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


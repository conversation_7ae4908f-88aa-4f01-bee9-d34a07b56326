# CloudspacesDNSRecordStruct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | Name of the record | 
**value** | **str** | Value of the record | 
**type** | **str** | type of the record | 
**ttl** | **int** | time to live for the record | 
**guid** | **str** | Identifier of record | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



apiVersion: v1
kind: ConfigMap
metadata:
  name: system-config
  namespace: default
data:
  environment_name: meneja-prd
  callbackurl: https://meneja.gig.tech/callback
  collector_debug: none
  domain: meneja.gig.tech
  isotemplate: /isotemplate
  meneja_debug: none
  meneja_db: meneja
  mongodb_host: mongo:27017
  # We are passing the hostname for service discovery and the port of the sentinal
  dynaqueue_redis: meneja-redis-redis-ha.default:26379
  dynaqueue_redis_sentinel_cluster_name: mymaster
  organization: meneja-ops
  mongodb_replcaset: rs0
  tlgm_notify: "1"
  tlgm_update: "1"
  gitlab_server: "https://git.gig.tech"
  gitlab_g8s_project: "gig-meneja/environments"
  gitlab_knowledge_base_project: "gig-meneja/knowledge_base"
  mnj_ce_operations_org: meneja-prd.cloud-enabler-operations
  operations_org: greenitglobe.team.operations
  gitlab_repo: "https://git.gig.tech/gig-meneja/monitoring/issues/&65"
  internal_minio_url: "https://s3-meneja-prd-begen.meneja.gig-tech.cloudbuilders.be"
  external_minio_url: "https://s3-meneja-prd-begen.meneja.gig-tech.cloudbuilders.be"
  backup_minio_url: "https://s3-meneja-prd-plwar-mirror.meneja.gig-tech.cloudbuilders.be"
  k8s_monitor_email: "<EMAIL>"
  # URL used by mongo-restic-backup helm chart
  restic_repository: "s3:https://s3-meneja-prd-begen.meneja.gig-tech.cloudbuilders.be/mongo-backup"
  letsencrypt_directory: "https://acme-v02.api.letsencrypt.org/directory"
  smtp_server: "smtp.sendgrid.net"
  smtp_port: "587"
  support_email_address: "<EMAIL>"
  iam_replicas: "1"
  meneja_replicas: "2"
  rancher_image_tag: "image:6246b6d40c5bc700014ebae0"
  mongo_hostname: "rs0/mongo"
  ubuntu_image_tag: "image:5fda5c1a1ed0bc000145b631"
  GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS: "<EMAIL>"
  email_server_url: email-server.meneja.gig-tech.cloudbuilders.be

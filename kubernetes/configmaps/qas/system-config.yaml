apiVersion: v1
kind: ConfigMap
metadata:
  name: system-config
  namespace: default
data:
  environment_name: meneja-qa
  callbackurl: https://meneja-qa.gig.tech/callback
  collector_debug: debug
  domain: meneja-qa.gig.tech
  isotemplate: /isotemplate
  meneja_debug: none
  meneja_db: meneja
  mongodb_host: mongo:27017
  dynaqueue_redis: meneja-redis-redis-ha.default:26379
  dynaqueue_redis_sentinel_cluster_name: mymaster
  organization: meneja-test
  mongodb_replcaset: rs0
  tlgm_notify: "1"
  tlgm_update: "1"
  gitlab_server: "https://git.gig.tech"
  gitlab_g8s_project: "gig-meneja/test-environments"
  gitlab_knowledge_base_project: "gig-meneja/knowledge_base"
  mnj_ce_operations_org: meneja-prd.cloud-enabler-operations
  operations_org: greenitglobe.team.operations
  gitlab_repo: "https://git.gig.tech/gig-meneja/monit-test/issues/&222"
  internal_minio_url: "https://s3-meneja-qa-begen.meneja.gig-tech.cloudbuilders.be"
  external_minio_url: "https://s3-meneja-qa-begen.meneja.gig-tech.cloudbuilders.be"
  backup_minio_url: "https://s3-meneja-qa-begen-mirror.meneja.gig-tech.cloudbuilders.be"
  # URL used by mongo-restic-backup helm chart
  restic_repository: "s3:https://s3-meneja-qa-begen.meneja.gig-tech.cloudbuilders.be/mongo-backup"
  k8s_monitor_email: "<EMAIL>"
  letsencrypt_directory: "https://acme-v02.api.letsencrypt.org/directory"
  smtp_server: "smtp.sendgrid.net"
  smtp_port: "587"
  support_email_address: "<EMAIL>"
  iam_replicas: "1"
  meneja_replicas: "2"
  mongo_hostname: "rs0/mongo"
  rancher_image_tag: "image:6246b675396dbe0001ed5b2f"
  ubuntu_image_tag: "image:602e5ee85212390001b5c765"
  GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS: <EMAIL>
  email_server_url: email-service.gig-11.deployments.cloudbuilders.be
  
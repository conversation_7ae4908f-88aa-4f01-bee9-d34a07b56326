apiVersion: v1
kind: ConfigMap
metadata:
  name: system-config
  namespace: {{ NAMESPACE }}
data:
  environment_name: meneja-test
  callbackurl: http://meneja-dev.gig.tech/callback
  collector_debug: debug
  domain: meneja:5000
  vco_domain: cairo-cloud-test-vco:5000
  isotemplate: /isotemplate
  meneja_debug: debug
  meneja_db: {{ DB_NAME }}
  iam_db: {{ DB_NAME }}-iam-test-vco
  mongodb_host: mongo:27017
  dynaqueue_redis: redis:6379
  organization: meneja-test
  mongodb_replcaset: rs0
  tlgm_notify: "0"
  tlgm_update: "0"
  gitlab_server: "https://git.gig.tech"
  gitlab_g8s_project: "gig-meneja/test-environments"
  gitlab_knowledge_base_project: "gig-meneja/knowledge_base"
  mnj_ce_operations_org: meneja-prd.cloud-enabler-operations
  operations_org: greenitglobe.team.operations

  gitlab_repo: 'https://git.gig.tech/gig-meneja/monit-test/issues/&222'

  internal_minio_url: 'http://storage-lab-meneja.gig.tech:9000'
  external_minio_url: 'http://storage-lab-meneja.gig.tech:9000'
  backup_minio_url: 'http://meneja-s3-service'
  # URL used by mongo-restic-backup helm chart
  restic_repository: 's3:http://meneja-minio-svc:9000/mongo-backup'
  letsencrypt_directory: "https://acme-staging-v02.api.letsencrypt.org/directory"
  smtp_server: 'smtp.sendgrid.net'
  smtp_port: '587'
  support_email_address: '<EMAIL>'
  iam_replicas: "1"
  meneja_replicas: "2"
  vco_iam_secret: "9yOq60-NIy7kqOYxJ4BC_IQMB6hiq4hp5Zs-QUjvppwxAJZEqZUJ"
  vco_callback: "http://cairo-cloud-test-vco:5000/callback"
  vco_organization: cairo-cloud-eg-local
  vco_id: cairo-cloud.eg.local
  iam_host: iam-cairo-cloud-test-vco:8080
  iam_domain: iam.cairo-cloud.eg.local
  image_name: "Ubuntu Server 20.04"
  email_server_url: invaliddomain.com
  
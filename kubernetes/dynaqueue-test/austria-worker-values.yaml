redisSentinalHosts: null
redisSentinalCluster: null
redisSentinalPort: null
redisConnection:
  host: "redis"
  port: 6379

name: austria-worker
namespace: {{ NAMESPACE }}
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
  - name: ENVIRONMENT_NAME
    valueFrom:
      configMapKeyRef:
        key: environment_name
        name: system-config
  - name: SERVER_NAME
    valueFrom:
      configMapKeyRef:
        key: domain
        name: system-config
  - name: MNJ_DATABASE
    valueFrom:
      configMapKeyRef:
        key: meneja_db
        name: system-config
  - name: MNJ_MONGORS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_replcaset
  - name: MNJ_MONGODB
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_host
  - name: MNJ_DEBUG
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: meneja_debug
  - name: MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: internal_minio_url
  - name: EXTERN_MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: external_minio_url
  - name: MINIO_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_KEY
  - name: MINIO_ACCESS_SECRET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_SECRET
  - name: MNJ_DYNAQUEUE_REDIS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: dynaqueue_redis
  - name: GTLB_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GTLB_TOKEN
  - name: GTLB_SERVER
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_server
  - name: GTLB_G8S_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_g8s_project
  - name: GTLB_KNOWlEDGE_BASE_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_knowledge_base_project
  - name: *********************
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mnj_ce_operations_org
  - name: OPERATIONS_ORG
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: operations_org
  - name: TLGM_GROUP
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_GROUP
  - name: TLGM_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_CHANNEL
  - name: TLGM_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_TOKEN
  - name: ROCKET_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_CHANNEL
  - name: ROCKET_BOT_NAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_NAME
  - name: ROCKET_BOT_PASSWORD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_PASSWORD
  - name: ROCKET_SERVER_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_SERVER_URL
  - name: ITSYOUONLINE_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ITSYOUONLINE_URL
  - name: TESTING_G8
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TESTING_G8
  - name: IMAGE_TESTING_ACCOUNT_NAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: IMAGE_TESTING_ACCOUNT_NAME
  - name: GOOGLE_API_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GOOGLE_API_KEY
  - name: terminationGracePeriodSeconds
    value: "129600"
queue: austria
replicaCount: 1
nodeSelector:
  location: at

redisSentinalHosts: null
redisSentinalCluster: null
redisSentinalPort: null
redisConnection:
  host: 'redis'
  port: 6379

name: imagebuilder
namespace: {{ NAMESPACE }}
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
- name: ENVIRONMENT_NAME
  valueFrom:
    configMapKeyRef:
      key: environment_name
      name: system-config 
- name: SERVER_NAME
  valueFrom:
    configMapKeyRef:
      key: domain
      name: system-config
- name: MNJ_DATABASE
  valueFrom:
    configMapKeyRef:
      key: meneja_db
      name: system-config
- name: MNJ_MONGORS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_replcaset
- name: MNJ_MONGODB
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_host
- name: MNJ_DEBUG
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: meneja_debug
- name: MINIO_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: internal_minio_url
- name: EXTERN_MINIO_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: external_minio_url
- name: MINIO_ACCESS_KEY
  valueFrom:
    secretKeyRef:
      name: meneja
      key: MINIO_ACCESS_KEY
- name: MINIO_ACCESS_SECRET
  valueFrom:
    secretKeyRef:
      name: meneja
      key: MINIO_ACCESS_SECRET
- name: MNJ_DYNAQUEUE_REDIS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: dynaqueue_redis
- name: GTLB_TOKEN
  valueFrom:
    secretKeyRef:
      name: meneja
      key: GTLB_TOKEN
- name: TESTING_G8
  valueFrom:
    secretKeyRef:
      name: meneja
      key: TESTING_G8
- name: IMAGE_TESTING_ACCOUNT_NAME
  valueFrom:
    secretKeyRef:
      name: meneja
      key: IMAGE_TESTING_ACCOUNT_NAME
- name: terminationGracePeriodSeconds
  value: "1260O"
queue: imagebuilder
replicaCount: 1
volumeMounts:
- name: dockersocket
  mountPath: /var/run/docker.sock
volumes:
- name: dockersocket
  hostPath:
    path: /var/run/docker.sock
    type: Socket

redisSentinalHosts: null
redisSentinalCluster: null
redisSentinalPort: null
redisConnection:
  host: 'redis'
  port: 6379

name: meneja-scheduler
namespace: {{ NAMESPACE }}
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
- name: ENVIRONMENT_NAME
  valueFrom:
    configMapKeyRef:
      key: environment_name
      name: system-config
- name: SERVER_NAME
  valueFrom:
    configMapKeyRef:
      key: domain
      name: system-config
- name: MNJ_DATABASE
  valueFrom:
    configMapKeyRef:
      key: meneja_db
      name: system-config
- name: MNJ_MONGORS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_replcaset
- name: MNJ_MONGODB
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_host
- name: MNJ_DYNAQUEUE_REDIS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: dynaqueue_redis
- name: EMAIL_SERVER_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: email_server_url
- name: terminationGracePeriodSeconds
  value: "180"
libraries:
  - meneja


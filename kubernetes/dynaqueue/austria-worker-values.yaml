redisSentinalHosts:
  - meneja-redis-redis-ha-announce-0
  - meneja-redis-redis-ha-announce-1
  - meneja-redis-redis-ha-announce-2

name: austria-worker
namespace: default
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
  - name: ENVIRONMENT_NAME
    valueFrom:
      configMapKeyRef:
        key: environment_name
        name: system-config
  - name: SERVER_NAME
    valueFrom:
      configMapKeyRef:
        key: domain
        name: system-config
  - name: LETSENCRYPT_DIRECTORY
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: letsencrypt_directory
  - name: MNJ_DATABASE
    valueFrom:
      configMapKeyRef:
        key: meneja_db
        name: system-config
  - name: K8s_MONITOR_EMAIL
    valueFrom:
      configMapKeyRef:
        key: k8s_monitor_email
        name: system-config
  - name: MNJ_MONGORS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_replcaset
  - name: MNJ_MONGODB
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_host
  - name: WORKER_DNS_RESOLVER
    valueFrom:
      secretKeyRef:
        name: meneja
        key: SWISS_DNS_RESOLVER
  - name: MNJ_DEBUG
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: meneja_debug
  - name: MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: internal_minio_url
  - name: EXTERN_MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: external_minio_url
  - name: MINIO_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_KEY
  - name: MINIO_ACCESS_SECRET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_SECRET
  - name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: dynaqueue_redis_sentinel_cluster_name
  - name: MNJ_DYNAQUEUE_REDIS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: dynaqueue_redis
  - name: GTLB_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GTLB_TOKEN
  - name: GTLB_SERVER
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_server
  - name: GTLB_G8S_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_g8s_project
  - name: GTLB_KNOWlEDGE_BASE_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_knowledge_base_project
  - name: *********************
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mnj_ce_operations_org
  - name: OPERATIONS_ORG
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: operations_org
  - name: TLGM_GROUP
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_GROUP
  - name: TLGM_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_CHANNEL
  - name: TLGM_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_TOKEN
  - name: ROCKET_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_CHANNEL
  - name: ROCKET_BOT_NAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_NAME
  - name: ROCKET_BOT_PASSWORD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_PASSWORD
  - name: ROCKET_SERVER_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_SERVER_URL
  - name: GIG_NS
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GIG_NS
  - name: DNS_BUCKET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: DNS_BUCKET
  - name: REDIS_DNS_QUEUES
    valueFrom:
      secretKeyRef:
        name: meneja
        key: REDIS_DNS_QUEUES
  - name: ITSYOUONLINE_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ITSYOUONLINE_URL
  - name: GOOGLE_API_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GOOGLE_API_KEY
  - name: EMAIL_SERVER_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: email_server_url

  - name: terminationGracePeriodSeconds
    value: "129600"
queue: austria
replicaCount: 1
nodeSelector:
  location: be
  deployment: enabled
resources:
  requests:
    memory: "1536Mi"  
    cpu: "400m"
  limits:
    memory: "1536Mi"
    cpu: "600m"
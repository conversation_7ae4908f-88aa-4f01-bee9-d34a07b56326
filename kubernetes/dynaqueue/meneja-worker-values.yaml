redisSentinalHosts:
  - meneja-redis-redis-ha-announce-0
  - meneja-redis-redis-ha-announce-1
  - meneja-redis-redis-ha-announce-2
replicaCount: 5
name: meneja
namespace: default
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
  - name: MC_UPLOAD_MULTIPART_THREADS
    value: "1"
  - name: GOMAXPROCS
    value: "1"
  - name: ENVIRONMENT_NAME
    valueFrom:
      configMapKeyRef:
        key: environment_name
        name: system-config
  - name: LETSENCRYPT_DIRECTORY
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: letsencrypt_directory
  - name: SERVER_NAME
    valueFrom:
      configMapKeyRef:
        key: domain
        name: system-config
  - name: K8s_MONITOR_EMAIL
    valueFrom:
      configMapKeyRef:
        key: k8s_monitor_email
        name: system-config
  - name: MNJ_DATABASE
    valueFrom:
      configMapKeyRef:
        key: meneja_db
        name: system-config
  - name: MNJ_MONGORS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_replcaset
  - name: MNJ_MONGODB
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongodb_host
  - name: MNJ_DEBUG
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: meneja_debug
  - name: MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: internal_minio_url
  - name: EXTERN_MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: external_minio_url
  - name: MINIO_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_KEY
  - name: MINIO_ACCESS_SECRET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_ACCESS_SECRET
  - name: BACKUP_MINIO_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: backup_minio_url 
  - name: BACKUP_MINIO_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: BACKUP_MINIO_ACCESS_KEY
  - name: BACKUP_MINIO_ACCESS_SECRET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: BACKUP_MINIO_ACCESS_SECRET  
  - name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: dynaqueue_redis_sentinel_cluster_name
  - name: MNJ_DYNAQUEUE_REDIS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: dynaqueue_redis
  - name: GTLB_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GTLB_TOKEN
  - name: GTLB_SERVER
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_server
  - name: GTLB_G8S_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_g8s_project
  - name: GTLB_KNOWlEDGE_BASE_PROJ
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: gitlab_knowledge_base_project
  - name: *********************
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mnj_ce_operations_org
  - name: OPERATIONS_ORG
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: operations_org
  - name: TLGM_GROUP
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_GROUP
  - name: TLGM_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_CHANNEL
  - name: TLGM_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TLGM_TOKEN
  - name: ROCKET_CHANNEL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_CHANNEL
  - name: ROCKET_BOT_NAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_NAME
  - name: ROCKET_BOT_PASSWORD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_BOT_PASSWORD
  - name: ROCKET_SERVER_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ROCKET_SERVER_URL
  - name: SENDGRID_API_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: SENDGRID_API_KEY
  - name: GIG_NS
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GIG_NS
  - name: DNS_BUCKET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: DNS_BUCKET
  - name: REDIS_DNS_QUEUES
    valueFrom:
      secretKeyRef:
        name: meneja
        key: REDIS_DNS_QUEUES
  - name: TESTING_G8
    valueFrom:
      secretKeyRef:
        name: meneja
        key: TESTING_G8
  - name: IMAGE_TESTING_ACCOUNT_NAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: IMAGE_TESTING_ACCOUNT_NAME
  - name: ITSYOUONLINE_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: ITSYOUONLINE_URL
  - name: GOOGLE_API_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: GOOGLE_API_KEY
  - name: OCTOPUS_API_BASE_URL
    valueFrom:
      secretKeyRef:
        name: meneja
        key: OCTOPUS_API_BASE_URL
  - name: OCTOPUS_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: OCTOPUS_TOKEN
  - name: G8_DOCKER_REG_SERVER
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_DOCKER_REG_SERVER
  - name: G8_DOCKER_REG_USER
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_DOCKER_REG_USER
  - name: G8_DOCKER_REG_PASSWD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_DOCKER_REG_PASSWD
  - name: SMTP_SERVER
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: smtp_server
  - name: SMTP_PORT
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: smtp_port
  - name: SUPPORT_EMAIL_ADDRESS
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: support_email_address
  - name: MENEJA_EMAIL_LOGIN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MENEJA_EMAIL_LOGIN
  - name: MENEJA_EMAIL_PASSWORD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MENEJA_EMAIL_PASSWORD
  - name: MONGO_HOSTNAME
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: mongo_hostname
  - name: MONGO_BACKUP_MINIO_ACCESS_KEY
    valueFrom:
      secretKeyRef:
        name: infrastructure
        key: MONGO_BACKUP_MINIO_ACCESS_KEY
  - name: MONGO_BACKUP_MINIO_ACCESS_SECRET
    valueFrom:
      secretKeyRef:
        name: infrastructure
        key: MONGO_BACKUP_MINIO_ACCESS_SECRET
  - name: RESTIC_REPO
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: restic_repository
  - name: RESTIC_PASSWORD
    valueFrom:
      secretKeyRef:
        name: infrastructure
        key: RESTIC_PASSWORD
  - name: G8_MAILCLIENT_SERVER
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_MAILCLIENT_SERVER  
  - name: G8_MAILCLIENT_PORT
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_MAILCLIENT_PORT
  - name: G8_MAILCLIENT_LOGIN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_MAILCLIENT_LOGIN
  - name: G8_MAILCLIENT_PASSWD
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_MAILCLIENT_PASSWD
  - name: G8_MAILCLIENT_SENDER
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_MAILCLIENT_SENDER              
  - name: G8_JIRA_HOST
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_JIRA_HOST
  - name: G8_JIRA_TOKEN
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_JIRA_TOKEN
  - name: G8_JIRA_USERNAME
    valueFrom:
      secretKeyRef:
        name: meneja
        key: G8_JIRA_USERNAME
  - name: MINIO_READ_ONLY_KEY
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_READ_ONLY_KEY
  - name: MINIO_READ_ONLY_SECRET
    valueFrom:
      secretKeyRef:
        name: meneja
        key: MINIO_READ_ONLY_SECRET
  - name: EMAIL_SERVER_URL
    valueFrom:
      configMapKeyRef:
        name: system-config
        key: email_server_url

nodeSelector:
  deployment: enabled
resources:
  requests:
    memory: "1536Mi"  
    cpu: "400m"
  limits:
    memory: "1536Mi"
    cpu: "600m"
redisSentinalHosts:
  - meneja-redis-redis-ha-announce-0
  - meneja-redis-redis-ha-announce-1
  - meneja-redis-redis-ha-announce-2

name: meneja-scheduler
namespace: default
image:
  repository: ghub.gig.tech/gig-meneja/meneja
extraEnv:
- name: ENVIRONMENT_NAME
  valueFrom:
    configMapKeyRef:
      key: environment_name
      name: system-config
- name: K8s_MONITOR_EMAIL
  valueFrom:
    configMapKeyRef:
      key: k8s_monitor_email
      name: system-config
- name: SERVER_NAME
  valueFrom:
    configMapKeyRef:
      key: domain
      name: system-config
- name: MNJ_DATABASE
  valueFrom:
    configMapKeyRef:
      key: meneja_db
      name: system-config
- name: MNJ_MONGORS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_replcaset
- name: MNJ_MONGODB
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: mongodb_host
- name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: dynaqueue_redis_sentinel_cluster_name
- name: LETSENCRYPT_DIRECTORY
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: letsencrypt_directory
- name: MNJ_DYNAQUEUE_REDIS
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: dynaqueue_redis
- name: terminationGracePeriodSeconds
  value: "180"
- name: MINIO_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: internal_minio_url
- name: MINIO_ACCESS_KEY
  valueFrom:
    secretKeyRef:
      name: meneja
      key: MINIO_ACCESS_KEY
- name: MINIO_ACCESS_SECRET
  valueFrom:
    secretKeyRef:
      name: meneja
      key: MINIO_ACCESS_SECRET
- name: BACKUP_MINIO_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: backup_minio_url 
- name: BACKUP_MINIO_ACCESS_KEY
  valueFrom:
    secretKeyRef:
      name: meneja
      key: BACKUP_MINIO_ACCESS_KEY
- name: BACKUP_MINIO_ACCESS_SECRET
  valueFrom:
    secretKeyRef:
      name: meneja
      key: BACKUP_MINIO_ACCESS_SECRET
- name: EMAIL_SERVER_URL
  valueFrom:
    configMapKeyRef:
      name: system-config
      key: email_server_url
libraries:
  - meneja
nodeSelector:
  deployment: enabled
resources:
  requests:
    memory: "1536Mi"  
    cpu: "400m"
  limits:
    memory: "1536Mi"
    cpu: "600m"
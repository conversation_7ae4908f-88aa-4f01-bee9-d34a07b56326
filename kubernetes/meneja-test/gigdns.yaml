apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: bind-deployment
  namespace: {{ NAMESPACE }}
  labels:
    app: bind
spec:
  serviceName: "bind-deployment"
  replicas: 3
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: bind
  template:
    metadata:
      labels:
        app: bind
    spec:
      shareProcessNamespace: true
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - bind
            topologyKey: "location"
      containers:
      - name: bind
        image: IMAGE
        volumeMounts:
        - name: workdir
          mountPath: "etc/"
        ports:
        - containerPort: 53
          protocol: TCP
        - containerPort: 53
          protocol: UDP
        readinessProbe:
          tcpSocket:
            port: 53
          initialDelaySeconds: 1
          periodSeconds: 1
        livenessProbe:
          tcpSocket:
            port: 53
          initialDelaySeconds: 15
          periodSeconds: 20
      - name: bind-sidecar
        image: IMAGE-sidecar-container
        command: ["watch-updates"]
        volumeMounts:
        - name: workdir
          mountPath: "/var/side-car/"
        env:
          - name: MNJ_DYNAQUEUE_REDIS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: dynaqueue_redis
          - name: NAME_SERVERS_IPS
            valueFrom:
              secretKeyRef:
                name: meneja
                key: NAME_SERVERS_IPS

      initContainers:
      - name: init-dns
        image: IMAGE-init-container
        command: ["init-dns"]
        volumeMounts:
        - name: workdir
          mountPath: "/tmp/dir"
        env:
          - name: NAME_SERVERS_IPS
            valueFrom:
              secretKeyRef:
                name: meneja
                key: NAME_SERVERS_IPS
          - name: MNJ_DATABASE
            valueFrom:
              configMapKeyRef:
                key: meneja_db
                name: system-config
          - name: MNJ_MONGORS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_replcaset
          - name: MNJ_MONGODB
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_host
      volumes:
      - name: workdir
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: bind-deployment
  namespace: {{ NAMESPACE }}
spec:
  type: ClusterIP
  selector:
    app: bind
  ports:
    - protocol: TCP
      port: 53
      name: tcp
      targetPort: 53
    - protocol: UDP
      port: 53
      name: udp
      targetPort: 53
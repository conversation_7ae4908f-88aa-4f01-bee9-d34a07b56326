---
apiVersion: v1
kind: Service
metadata:
  name: iam-cairo-cloud-test-vco
  namespace: {{ NAMESPACE }}
  labels:
    name: iam-cairo-cloud-test-vco
spec:
  ports:
    - name: iam-cairo-cloud-test-vco
      port: 8080
      targetPort: 8080
  selector:
    app: iam-test-vco

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-test-vco
  namespace: {{ NAMESPACE }}
spec:
  selector:
    matchLabels:
      app: iam-test-vco
  replicas: 1
  template:
    metadata:
      labels:
        app: iam-test-vco
    spec:
      initContainers:
        - name: iam-init-db
          image: ghub.gig.tech/gig-meneja/meneja/test-iam-init-db:{{ SHA_IMAGE }}
          env:
            - name: MNJ_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: meneja_db
                  name: system-config
            - name: MNJ_MONGORS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_replcaset
            - name: MNJ_MONGODB
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_host
            - name: DB_NAME
              value: $(MNJ_DATABASE)-iam-test-vco
            - name: ROOT_ORG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_organization
          command: ["python"]
          args:
          - "scripts/init_iam_db.py"
      
      containers:
        - name: iam-test-vco
          image: ghub.gig.tech/gig-meneja/iam:master
          imagePullPolicy: Always
          env:
            - name: MNJ_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: meneja_db
                  name: system-config
            - name: MNJ_MONGORS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_replcaset
            - name: MNJ_MONGODB
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_host
            - name: IAM_SECRET
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_iam_secret
            - name: IAM_HOST
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: iam_host
            - name: ROOT_ORG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_organization
            - name: VCO_CALLBACK
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_callback              
          args:
            - -c=mongodb://$(MNJ_MONGODB)/$(MNJ_DATABASE)-iam-test-vco?replicaSet=$(MNJ_MONGORS)
            - --iam-host=iam.cairo-cloud.eg.local
            - --root-org=$(ROOT_ORG)
            - --callback-url=$(VCO_CALLBACK)
            - --owner-email=<EMAIL>
            - --api-key=$(IAM_SECRET)
          ports:
            - containerPort: 5000
              name: http

apiVersion: v1
kind: ConfigMap
metadata:
  name: meneja-health-probes
  namespace: {{ NAMESPACE }}
data:
  liveness.sh: |
    #!/bin/sh
    set -eu

    # Check availability of mongo
    #test `lsof -a -i4 -i6 -itcp -nn | grep ':27017 (CLOSE_WAIT)' | wc -l` -eq 0 || echo "Lost connection to MongoDB..." && exit 1

    # Check availability of redis
    CHECK_SERVER="$(redis-cli -h redis -p 6379 ping)"

    if [ "$CHECK_SERVER" != "PONG" ] && [ "$CHECK_SERVER" != "LOADING Redis is loading the dataset in memory" ]; then
        echo "Server check failed with: $CHECK_SERVER"
        exit 1
    fi

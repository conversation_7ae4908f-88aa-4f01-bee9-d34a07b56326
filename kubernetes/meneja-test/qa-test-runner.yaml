  apiVersion: v1
  kind: Pod
  metadata:
    name: {{ POD_NAME }}
    namespace: default
    labels:
      app: qa-test-runner
  spec:
    restartPolicy: Never
    containers:
    - name: test-runner
      image: ghub.gig.tech/gig-meneja/meneja:{{ SHA_IMAGE }}
      ports:
        - containerPort: 8007
          name: http
      env:
        - name: CI_PIPELINE_ID
          value: "{{ CI_PIPELINE_ID }}"
        - name: QAS_NIGHTLY_BUILD
          value: "{{ QAS_NIGHTLY_BUILD }}"
        - name: GODADDY_API_KEY
          valueFrom:
            secretKeyRef:
              name: test-config
              key: GODADDY_API_KEY
        - name: STAGING_MINIO_ACCESS_SECRET
          valueFrom:
            secretKeyRef:
              name: test-config
              key: STAGING_MINIO_ACCESS_SECRET
        - name: STAGING_MINIO_URL
          valueFrom:
            secretKeyRef:
              name: test-config
              key: STAGING_MINIO_URL
        - name: STAGING_MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: test-config
              key: STAGING_MINIO_ACCESS_KEY
        - name: VCO_DOMAIN
          valueFrom:
            secretKeyRef:
              name: test-config
              key: VCO_DOMAIN
        - name: DOMAIN_OWNERSHIP
          valueFrom:
            secretKeyRef:
              name: test-config
              key: DOMAIN_OWNERSHIP
        - name: GODADDY_API_SECRET
          valueFrom:
            secretKeyRef:
              name: test-config
              key: GODADDY_API_SECRET
        - name: JWT
          valueFrom:
            secretKeyRef:
              name: test-config
              key: JWT
        - name: NIGHTLY_BUILD
          value: "true"
        - name: ENVIRONMENT_NAME
          valueFrom:
            configMapKeyRef:
              key: environment_name
              name: system-config
        - name: MNJ_DATABASE
          valueFrom:
            configMapKeyRef:
              key: meneja_db
              name: system-config
        - name: MNJ_MONGORS
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: mongodb_replcaset
        - name: MNJ_MONGODB
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: mongodb_host
        - name: MNJ_DYNAQUEUE_REDIS
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: dynaqueue_redis
        - name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: dynaqueue_redis_sentinel_cluster_name
        - name: MNJ_DEBUG
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: meneja_debug
        - name: LETSENCRYPT_DIRECTORY
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: letsencrypt_directory
        - name: MNJ_TOKEN
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MNJ_TOKEN
        - name: MINIO_URL
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: internal_minio_url
        - name: EXTERN_MINIO_URL
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: external_minio_url
        - name: GTLB_SERVER
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: gitlab_server
        - name: GTLB_G8S_PROJ
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: gitlab_g8s_project
        - name: SMTP_SERVER
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: smtp_server
        - name: K8s_MONITOR_EMAIL
          valueFrom:
            configMapKeyRef:
              key: k8s_monitor_email
              name: system-config
        - name: SMTP_PORT
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: smtp_port
        - name: MENEJA_EMAIL_LOGIN
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MENEJA_EMAIL_LOGIN
        - name: MENEJA_EMAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MENEJA_EMAIL_PASSWORD
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_KEY
        - name: MINIO_ACCESS_SECRET
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_SECRET
        - name: GTLB_TOKEN
          valueFrom:
            secretKeyRef:
              name: meneja
              key: GTLB_TOKEN
        - name: TLGM_TOKEN
          valueFrom:
            secretKeyRef:
              name: meneja
              key: TLGM_TOKEN
        - name: TLGM_GROUP
          valueFrom:
            secretKeyRef:
              name: meneja
              key: TLGM_GROUP
        - name: TLGM_CHANNEL
          valueFrom:
            secretKeyRef:
              name: meneja
              key: TLGM_CHANNEL
        - name: GIG_NS
          valueFrom:
            secretKeyRef:
              name: meneja
              key: GIG_NS
        - name: DNS_BUCKET
          valueFrom:
            secretKeyRef:
              name: meneja
              key: DNS_BUCKET
        - name: REDIS_DNS_QUEUES
          valueFrom:
            secretKeyRef:
              name: meneja
              key: REDIS_DNS_QUEUES
        - name: OCTOPUS_API_BASE_URL
          valueFrom:
            secretKeyRef:
              name: meneja
              key: OCTOPUS_API_BASE_URL
        - name: OCTOPUS_TOKEN
          valueFrom:
            secretKeyRef:
              name: meneja
              key: OCTOPUS_TOKEN
        - name: EXCHANGE_API_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: EXCHANGE_API_KEY                  
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: GOOGLE_API_KEY
        - name: RANCHER_IMAGE_TAG
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: rancher_image_tag
        - name: UBUNTU_IMAGE_TAG
          valueFrom:
            configMapKeyRef:
              name: system-config
              key: ubuntu_image_tag
        - name: ACRONIS_BACKUP_CDROM_ID
          valueFrom:
            secretKeyRef:
              name: meneja
              key: ACRONIS_BACKUP_CDROM_ID
        - name: ROCKET_CHANNEL
          valueFrom:
            secretKeyRef:
              name: meneja
              key: ROCKET_CHANNEL
        - name: ROCKET_BOT_NAME
          valueFrom:
            secretKeyRef:
              name: meneja
              key: ROCKET_BOT_NAME
        - name: ROCKET_BOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: meneja
              key: ROCKET_BOT_PASSWORD
        - name: ROCKET_SERVER_URL
          valueFrom:
            secretKeyRef:
              name: meneja
              key: ROCKET_SERVER_URL
        - name: VEEAM_BACKUP_CDROM_ID
          valueFrom:
            secretKeyRef:
              name: meneja
              key: VEEAM_BACKUP_CDROM_ID
        - name: QA_TESTING_LOCATION
          valueFrom:
            secretKeyRef:
              name: test-config
              key: QA_TESTING_LOCATION
        - name: OCTOPUS_CUSTOMER_ID
          valueFrom:
            secretKeyRef:
              name: test-config
              key: OCTOPUS_CUSTOMER_ID
        - name: OCTOPUS_CLOUDSPACE_ID
          valueFrom:
            secretKeyRef:
              name: test-config
              key: OCTOPUS_CLOUDSPACE_ID
        - name: OCTOPUS_VM_ID
          valueFrom:
            secretKeyRef:
              name: test-config
              key: OCTOPUS_VM_ID
        - name: OCTOPUS_JWT
          valueFrom:
            secretKeyRef:
              name: test-config
              key: OCTOPUS_JWT
      command: ["sleep"]
      args:
      - "infinity"

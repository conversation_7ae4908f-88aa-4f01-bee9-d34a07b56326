---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: {{ NAMESPACE }}
  labels:
    app: redis
spec:
  selector:
    matchLabels:
      app: redis
  replicas: 1
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis
        ports:
        - containerPort: 6379

---        
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: {{ NAMESPACE }}
  labels:
    app: redis
spec:
  ports:
  - port: 6379
    targetPort: 6379
  selector:
    app: redis

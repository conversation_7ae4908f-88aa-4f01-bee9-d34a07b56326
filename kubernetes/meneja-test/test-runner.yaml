  apiVersion: batch/v1
  kind: Job
  metadata:
    name: {{ POD_NAME }}
    namespace: {{ NAMESPACE }}
  spec:
    backoffLimit: 0
    template:
      metadata:
        labels:
          app: test-runner
      spec:
        restartPolicy: Never
        containers:
        - name: test-runner
          image: ghub.gig.tech/gig-meneja/meneja:{{ SHA_IMAGE }}
          ports:
            - containerPort: 8007
              name: http
          env:
          - name: ENVIRONMENT_NAME
            value: meneja-test
          - name: NIGHTLY_BUILD
            valueFrom:
              secretKeyRef:
                name: test-config
                key: NIGHTLY_BUILD            
          - name: TEST_RESULT_FILE
            valueFrom:
              secretKeyRef:
                name: test-config
                key: TEST_RESULT_FILE
          - name: BUCKET
            valueFrom:
              secretKeyRef:
                name: test-config
                key: BUCKET
          - name: S3_URL
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_URL
          - name: S3_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_ACCESS_KEY
          - name: S3_ACCESS_SECRET
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_ACCESS_SECRET                                      
          - name: JWT
            valueFrom:
              secretKeyRef:
                name: test-config
                key: JWT
          - name: ITSYOUONLINE_URL
            valueFrom:
              secretKeyRef:
                name: meneja
                key: ITSYOUONLINE_URL
          - name: TEST_G8_NAME
            valueFrom:
              secretKeyRef:
                name: test-config
                key: TEST_G8_NAME
          - name: TEST_IMAGE_NAME
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: image_name
          - name: TESTING_G8
            valueFrom:
              secretKeyRef:
                name: meneja
                key: TESTING_G8
          - name: MNJ_DATABASE
            valueFrom:
              configMapKeyRef:
                key: meneja_db
                name: system-config
          - name: IAM_DATABASE
            valueFrom:
              configMapKeyRef:
                key: iam_db
                name: system-config                
          - name: MNJ_MONGODB
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_host
          - name: MNJ_MONGORS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_replcaset
          - name: MNJ_DYNAQUEUE_REDIS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: dynaqueue_redis
          - name: IAM_HOST
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: iam_host
          - name: VCO_DOMAIN
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: vco_domain
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          command: ["/bin/bash"]
          args:
          - "scripts/test_and_upload_results.sh"

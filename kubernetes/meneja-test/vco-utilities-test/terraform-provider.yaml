apiVersion: batch/v1
kind: Job
metadata:
  name: terraform-provider-builder
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        app: terraform-provider-builder
    spec:
      restartPolicy: Never
      containers:
      - args:
        - build
        env:
        - name: GIG_APP_NAME
          value: test-terraform-provider
        - name: SWAGGER_DESCRIPTION
          value: https://console.cloudbuilders.be/api/1/swagger.json
        - name: S3_UPLOAD_LOCATION
          value: https://storage-qa-meneja.gig.tech
        - name: S3_BUCKET
          value: vco.terraform.provider
        - name: S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_KEY
        - name: S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_SECRET
        - name: JWT_REFRESH_URL
          value: https://iam.cloudbuilders.be/v1/oauth/jwt/refresh
        image: ghub.gig.tech/gig-meneja/terraform-provider-vco/terraform-provider-vco-builder
        imagePullPolicy: Always
        name: terraform-provider-builder
        resources:
          limits:
            cpu: "1"
            memory: 1Gi
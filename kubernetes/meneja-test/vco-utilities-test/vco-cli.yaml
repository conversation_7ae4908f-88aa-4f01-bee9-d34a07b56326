apiVersion: batch/v1
kind: Job
metadata:
  name: vco-cli-builder
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        app: vco-cli-builder
    spec:
      restartPolicy: Never
      containers:
      - args:
        - build
        env:
        - name: GIG_APP_NAME
          value: vco-cli
        - name: SWAGGER_DESCRIPTION
          value: https://console.cloudbuilders.be/api/1/swagger.json
        - name: S3_UPLOAD_LOCATION
          value: https://storage-qa-meneja.gig.tech
        - name: S3_BUCKET
          value: vco.terraform.provider
        - name: S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_KEY
        - name: S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: meneja
              key: MINIO_ACCESS_SECRET
        - name: JWT_REFRESH_URL
          value: https://iam.cloudbuilders.be/v1/oauth/jwt/refresh
        image: ghub.gig.tech/gig-meneja/vco-cli/cli-builder
        imagePullPolicy: Always
        name: vco-cli-builder
        resources:
          limits:
            cpu: "1"
            memory: 2Gi
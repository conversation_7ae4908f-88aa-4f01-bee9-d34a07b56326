---
apiVersion: v1
kind: Service
metadata:
  name: cairo-cloud-test-vco
  namespace: {{ NAMESPACE }}
  labels:
    name: cairo-cloud-test-vco
spec:
  ports:
    - name: cairo-cloud-test-vco
      port: 5000
      targetPort: 5000
  selector:
    app: cairo-cloud-test-vco

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cairo-cloud-test-vco
  namespace: {{ NAMESPACE }}
spec:
  selector:
    matchLabels:
      app: cairo-cloud-test-vco
  replicas: 1
  template:
    metadata:
      labels:
        app: cairo-cloud-test-vco
    spec:
      initContainers:
        - name: pre-test
          image: ghub.gig.tech/gig-meneja/meneja:{{ SHA_IMAGE }}
          command: ["/bin/bash"]
          args:
          - "-c"
          - "pypy scripts/pre_tests.py"
          env:
          - name: ENVIRONMENT_NAME
            value: meneja-test
          - name: NIGHTLY_BUILD
            valueFrom:
              secretKeyRef:
                name: test-config
                key: NIGHTLY_BUILD            
          - name: TEST_RESULT_FILE
            valueFrom:
              secretKeyRef:
                name: test-config
                key: TEST_RESULT_FILE
          - name: BUCKET
            valueFrom:
              secretKeyRef:
                name: test-config
                key: BUCKET
          - name: S3_URL
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_URL
          - name: S3_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_ACCESS_KEY
          - name: S3_ACCESS_SECRET
            valueFrom:
              secretKeyRef:
                name: test-config
                key: S3_ACCESS_SECRET                                      
          - name: JWT
            valueFrom:
              secretKeyRef:
                name: test-config
                key: JWT
          - name: ITSYOUONLINE_URL
            valueFrom:
              secretKeyRef:
                name: meneja
                key: ITSYOUONLINE_URL
          - name: TEST_G8_NAME
            valueFrom:
              secretKeyRef:
                name: test-config
                key: TEST_G8_NAME
          - name: TEST_IMAGE_NAME
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: image_name
          - name: TESTING_G8
            valueFrom:
              secretKeyRef:
                name: meneja
                key: TESTING_G8
          - name: MNJ_DATABASE
            valueFrom:
              configMapKeyRef:
                key: meneja_db
                name: system-config
          - name: IAM_DATABASE
            valueFrom:
              configMapKeyRef:
                key: iam_db
                name: system-config                
          - name: MNJ_MONGODB
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_host
          - name: MNJ_MONGORS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: mongodb_replcaset
          - name: MNJ_DYNAQUEUE_REDIS
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: dynaqueue_redis
          - name: IAM_HOST
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: iam_host
          - name: IAM_DOMAIN
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: iam_domain                
          - name: VCO_ORGANIZATION
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: vco_organization
          - name: VCO_DOMAIN
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: vco_domain
          - name: VCO_ID
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: vco_id
          - name: IAM_SECRET
            valueFrom:
              configMapKeyRef:
                name: system-config
                key: vco_iam_secret
          - name: MENEJA_DOMAIN
            valueFrom:
              configMapKeyRef:
                key: domain
                name: system-config
      containers:
        - name: cairo-cloud-test-vco
          image: ghub.gig.tech/gig-meneja/meneja/vco:{{ SHA_IMAGE }}
          imagePullPolicy: Always
          env:
            - name: IAM_HOST
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: iam_host
            - name: ROOT_ORG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_organization
            - name: IAM_SECRET
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_iam_secret
            - name: ENVIRONMENT_NAME
              valueFrom:
                configMapKeyRef:
                  key: environment_name
                  name: system-config
            - name: MNJ_INSTANCE
              value: "1"
            - name: VCO
              value: "yes"
            - name: MNJ_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: meneja_db
                  name: system-config
            - name: IAM_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: iam_db
                  name: system-config                  
            - name: MNJ_MONGORS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_replcaset
            - name: MNJ_MONGODB
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_host
            - name: MNJ_DYNAQUEUE_REDIS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: dynaqueue_redis
            - name: MNJ_DEBUG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: meneja_debug
            - name: MNJ_ISOTMP
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: isotemplate
            - name: MNJ_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_TOKEN
            - name: MNJ_FLASK_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_FLASK_SECRET
            - name: MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: internal_minio_url
            - name: EXTERN_MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: external_minio_url
            - name: GTLB_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_server
            - name: GTLB_G8S_PROJ
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_g8s_project
            - name: SMTP_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_server
            - name: SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_port
            - name: MENEJA_EMAIL_LOGIN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_LOGIN
            - name: MENEJA_EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_PASSWORD
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_KEY
            - name: MINIO_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_SECRET
            - name: GTLB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GTLB_TOKEN
            - name: TLGM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_TOKEN
            - name: TLGM_GROUP
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_GROUP
            - name: TLGM_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_CHANNEL
            - name: ROCKET_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_CHANNEL
            - name: ROCKET_BOT_NAME
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_NAME
            - name: ROCKET_BOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_PASSWORD
            - name: ROCKET_SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_SERVER_URL
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GOOGLE_API_KEY
            - name: MNJ_CBKURL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: vco_callback
            - name: ACRONIS_BACKUP_CDROM_ID
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ACRONIS_BACKUP_CDROM_ID
            - name: VEEAM_BACKUP_CDROM_ID
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: VEEAM_BACKUP_CDROM_ID
            - name: ROCKET_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_CHANNEL
            - name: ROCKET_BOT_NAME
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_NAME
            - name: ROCKET_BOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_PASSWORD
            - name: ROCKET_SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_SERVER_URL

          command: ["menejas"]
          args:
            - "0.0.0.0"
            - "5000"
            - "$(GTLB_SERVER)"
            - "$(MNJ_ISOTMP)"
          ports:
            - containerPort: 5000
              name: http

apiVersion: apps/v1
kind: Deployment
metadata:
  name: meneja
  namespace: default
spec:
  selector:
    matchLabels:
      app: meneja
  replicas: 4
  template:
    metadata:
      labels:
        app: meneja
    spec:
      serviceAccountName: meneja-account
      nodeSelector:
        deployment: enabled
      containers:
        - name: meneja
          image: ghub.gig.tech/gig-meneja/meneja:{{ SHA_IMAGE }}
          imagePullPolicy: Always
          env:
            - name: MENEJA
              value: "yes"
            - name: G8_DOCKER_REG_SERVER
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_DOCKER_REG_SERVER
            - name: G8_DOCKER_REG_USER
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_DOCKER_REG_USER
            - name: G8_DOCKER_REG_PASSWD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_DOCKER_REG_PASSWD
            - name: G8_JIRA_HOST
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_JIRA_HOST
            - name: K8s_MONITOR_EMAIL
              valueFrom:
                configMapKeyRef:
                  key: k8s_monitor_email
                  name: system-config
            - name: G8_JIRA_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_JIRA_TOKEN
            - name: G8_JIRA_USERNAME
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_JIRA_USERNAME
            - name: G8_MAILCLIENT_SERVER
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_MAILCLIENT_SERVER
            - name: G8_MAILCLIENT_PORT
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_MAILCLIENT_PORT
            - name: G8_MAILCLIENT_LOGIN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_MAILCLIENT_LOGIN
            - name: G8_MAILCLIENT_PASSWD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_MAILCLIENT_PASSWD
            - name: G8_MAILCLIENT_SENDER
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: G8_MAILCLIENT_SENDER
            - name: ENVIRONMENT_NAME
              valueFrom:
                configMapKeyRef:
                  key: environment_name
                  name: system-config
            - name: LETSENCRYPT_DIRECTORY
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: letsencrypt_directory
            - name: MNJ_INSTANCE
              value: "1"
            - name: SERVER_NAME
              valueFrom:
                configMapKeyRef:
                  key: domain
                  name: system-config
            - name: MNJ_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: meneja_db
                  name: system-config
            - name: MNJ_MONGORS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_replcaset
            - name: MNJ_MONGODB
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_host
            - name: MNJ_DYNAQUEUE_REDIS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: dynaqueue_redis
            - name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: dynaqueue_redis_sentinel_cluster_name
            - name: MNJ_DEBUG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: meneja_debug
            - name: MNJ_IAM_ORGANIZATION
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: organization
            - name: MNJ_CBKURL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: callbackurl
            - name: MNJ_ISOTMP
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: isotemplate
            - name: MNJ_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_TOKEN
            - name: MNJ_FLASK_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_FLASK_SECRET
            - name: MNJ_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_SECRET
            - name: ITSYOUONLINE_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ITSYOUONLINE_URL
            - name: MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: internal_minio_url
            - name: EXTERN_MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: external_minio_url
            - name: GTLB_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_server
            - name: GTLB_G8S_PROJ
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_g8s_project
            - name: GTLB_KNOWlEDGE_BASE_PROJ
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_knowledge_base_project
            - name: MNJ_CE_OPERATIONS_ORG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mnj_ce_operations_org
            - name: OPERATIONS_ORG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: operations_org
            - name: SMTP_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_server
            - name: SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_port
            - name: SUPPORT_EMAIL_ADDRESS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: support_email_address
            - name: MENEJA_EMAIL_LOGIN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_LOGIN
            - name: MENEJA_EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_PASSWORD
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_KEY
            - name: MINIO_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_SECRET
            - name: GTLB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GTLB_TOKEN
            - name: TLGM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_TOKEN
            - name: TLGM_GROUP
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_GROUP
            - name: TLGM_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_CHANNEL
            - name: ROCKET_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_CHANNEL
            - name: ROCKET_BOT_NAME
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_NAME
            - name: ROCKET_BOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_BOT_PASSWORD
            - name: ROCKET_SERVER_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: ROCKET_SERVER_URL
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: SENDGRID_API_KEY
            - name: GIG_NS
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GIG_NS
            - name: DNS_BUCKET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: DNS_BUCKET
            - name: REDIS_DNS_QUEUES
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: REDIS_DNS_QUEUES
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GOOGLE_API_KEY
            - name: TESTING_G8
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TESTING_G8
            - name: IMAGE_TESTING_ACCOUNT_NAME
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: IMAGE_TESTING_ACCOUNT_NAME
            - name: WG_CONFIG
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: WG_CONFIG
                  optional: true
            - name: OCTOPUS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_TOKEN
            - name: OCTOPUS_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_API_BASE_URL
            - name: VCO_MENEJA_REPLICAS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: meneja_replicas
            - name: VCO_IAM_REPLICAS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: iam_replicas
            - name: RESTIC_REPO
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: restic_repository
            - name: RESTIC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: infrastructure
                  key: RESTIC_PASSWORD
            - name: OCTOPUS_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_API_BASE_URL
            - name: OCTOPUS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_TOKEN
            - name: RANCHER_IMAGE_TAG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: rancher_image_tag
            - name: EXCHANGE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: EXCHANGE_API_KEY
            - name: MONGO_BACKUP_MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: infrastructure
                  key: MONGO_BACKUP_MINIO_ACCESS_KEY
            - name: MONGO_BACKUP_MINIO_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: infrastructure
                  key: MONGO_BACKUP_MINIO_ACCESS_SECRET
            - name: UBUNTU_IMAGE_TAG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: ubuntu_image_tag
            - name: TWILIO_ACCOUNT_SID  
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TWILIO_ACCOUNT_SID
            - name: TWILIO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TWILIO_AUTH_TOKEN
            - name: TWILIO_PHONE_NUMBER
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TWILIO_PHONE_NUMBER
            - name: GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: GIG_NOTIFICATION_SENDER_EMAIL_ADDRESS
            - name: EMAIL_SERVER_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: email_server_url
          command: ["menejas"]
          args:
            - "0.0.0.0"
            - "5000"
            - "$(MNJ_CBKURL)"
            - "$(MNJ_IAM_ORGANIZATION)"
            - "$(MNJ_SECRET)"
            - "$(GTLB_SERVER)"
            - "$(MNJ_ISOTMP)"
          ports:
            - containerPort: 5000
              name: http
          livenessProbe:
            httpGet:
              path: /liveness_probe
              port: 5000
              httpHeaders:
                - name: Host
                  value: {{ SERVER_NAME }}
            initialDelaySeconds: 15
            periodSeconds: 30
            timeoutSeconds: 15
          readinessProbe:
            httpGet:
              path: /api/1/swagger.json
              port: 5000
              httpHeaders:
                - name: Host
                  value: {{ SERVER_NAME }}
          resources:
            requests:
              memory: "1536Mi"  
              cpu: "400m"
            limits:
              memory: "1536Mi"
              cpu: "600m"
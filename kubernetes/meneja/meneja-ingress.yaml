apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-meneja
  namespace: default
  annotations:
    # Use the shared ingress-nginx
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "route"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "86400"
spec:
  rules:
    - host: "meneja-qa.gig.tech"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: meneja
                port:
                  number: 5000
    - host: "meneja.gig.tech"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: meneja
                port:
                  number: 5000
    - host: "meneja-lab.gig.tech"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: meneja
                port:
                  number: 5000
    - host: "meneja-edge.gig.tech"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: meneja
                port:
                  number: 5000
  tls:
    - hosts:
        - "*.gig.tech"
      secretName: gig-tls-cert

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: meneja-account
  namespace: default
imagePullSecrets:
  - name: meneja-registry-credentials
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: meneja-role-cluster
rules:
  # Application: full access cluster-wide.
  - apiGroups: [gig.tech]
    resources: [iams]
    verbs: [list, watch, create, delete, get, patch]

  - apiGroups: [gig.tech]
    resources: [g8-vco-accesses]
    verbs: [list, watch, create, delete, get, patch]

  - apiGroups: [batch]
    resources: [jobs, jobs/status]
    verbs: [delete, list, watch, get, create]

  - apiGroups: [""]
    resources: [pods, configmaps]
    verbs: [delete, list, watch, get, patch, create]

  - apiGroups: ["cert-manager.io"]
    resources: ["certificates"]
    verbs: [list]

  - apiGroups: ["apps"]
    resources: [deployments]
    verbs: [delete, list, watch, get, patch]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: meneja-rolebinding-cluster
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: meneja-role-cluster
subjects:
  - kind: ServiceAccount
    name: meneja-account
    namespace: default
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vco-account
  namespace: default
imagePullSecrets:
  - name: meneja-registry-credentials
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vco-role-cluster
rules:
  - apiGroups: [gig.tech]
    resources: [iams]
    verbs: [get, patch]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vco-rolebinding-cluster
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: vco-role-cluster
subjects:
  - kind: ServiceAccount
    name: vco-account
    namespace: default
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vco-docs
  namespace: default
  labels:
    gig.tech/type: VCO
spec:
  selector:
    matchLabels:
      app: vco-docs
  replicas: 1
  template:
    metadata:
      labels:
        app: vco-docs
        gig.tech/type: VCO
    spec:
      serviceAccountName: vco-account
      nodeSelector:
        deployment: enabled
      containers:
        - name: vco-docs
          image: ghub.gig.tech/gig-meneja/meneja/vco:{{ SHA_IMAGE }}
          imagePullPolicy: Always
          env:
            - name: ENVIRONMENT_NAME
              valueFrom:
                configMapKeyRef:
                  key: environment_name
                  name: system-config
            - name: MNJ_INSTANCE
              value: "1"
            - name: VCO
              value: "yes"
            - name: VCO_DOCS
              value: "yes"              
            - name: MNJ_DATABASE
              valueFrom:
                configMapKeyRef:
                  key: meneja_db
                  name: system-config
            - name: MNJ_MONGORS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_replcaset
            - name: MNJ_MONGODB
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: mongodb_host
            - name: MNJ_DYNAQUEUE_REDIS
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: dynaqueue_redis
            - name: MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: dynaqueue_redis_sentinel_cluster_name
            - name: MNJ_DEBUG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: meneja_debug
            - name: MNJ_ISOTMP
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: isotemplate
            - name: MNJ_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_TOKEN
            - name: MNJ_FLASK_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MNJ_FLASK_SECRET
            - name: MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: internal_minio_url
            - name: EXTERN_MINIO_URL
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: external_minio_url
            - name: GTLB_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_server
            - name: GTLB_G8S_PROJ
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: gitlab_g8s_project
            - name: SMTP_SERVER
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_server
            - name: SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: smtp_port
            - name: MENEJA_EMAIL_LOGIN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_LOGIN
            - name: MENEJA_EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MENEJA_EMAIL_PASSWORD
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_KEY
            - name: MINIO_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: MINIO_ACCESS_SECRET
            - name: GTLB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GTLB_TOKEN
            - name: TLGM_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_TOKEN
            - name: TLGM_GROUP
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_GROUP
            - name: TLGM_CHANNEL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: TLGM_CHANNEL
            - name: GIG_NS
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GIG_NS
            - name: OCTOPUS_API_BASE_URL
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_API_BASE_URL
            - name: OCTOPUS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: OCTOPUS_TOKEN
            - name: EXCHANGE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: EXCHANGE_API_KEY                  
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: meneja
                  key: GOOGLE_API_KEY
            - name: RANCHER_IMAGE_TAG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: rancher_image_tag
            - name: UBUNTU_IMAGE_TAG
              valueFrom:
                configMapKeyRef:
                  name: system-config
                  key: ubuntu_image_tag
            - name: DEBUGPY_PORT
              value: "5678"
            - name: GEVENT_SUPPORT
              value: "True"
          command: ["menejas"]
          args:
            - "0.0.0.0"
            - "5000"
            - "$(GTLB_SERVER)"
            - "$(MNJ_ISOTMP)"
          ports:
            - containerPort: 5000
              name: http
          livenessProbe:
            httpGet:
              path: /liveness_probe
              port: 5000
            initialDelaySeconds: 300
            periodSeconds: 120
            timeoutSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /liveness_probe
              port: 5000
            initialDelaySeconds: 360
            periodSeconds: 15
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3


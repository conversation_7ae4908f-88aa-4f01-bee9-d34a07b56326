# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
from dataclasses import dataclass
from typing import Optional

from flask_restx import fields

from meneja.api import api
from meneja.structs.vco.dataclasses.notes import ResourceNoteStruct

os_names_model = api.model(
    "OSNamesMap",
    {
        "result": fields.List(
            fields.Nested(
                api.model(
                    "OSTypeName",
                    {
                        "os_type": fields.String(readOnly=True, help_text="OS type"),
                        "os_names": fields.List(fields.String(readOnly=True, help_text="possible os-name")),
                    },
                )
            )
        )
    },
)

resource_notes_model = api.model("ResourceNotes", {"result": fields.List(fields.Nested(ResourceNoteStruct.model(api)))})


class BinaryStringField(fields.String):
    """
    Custom flask_restx field to be used in marshalling APIs returning binary
    """

    def schema(self):
        """Return the schema"""
        enum = self._v("enum")
        schema = super(BinaryStringField, self).schema()
        if enum:
            schema.update(enum=enum)
        if enum and schema["example"] is None:
            schema["example"] = enum[0]
        schema["format"] = "binary"
        return schema


@dataclass
class ResourceNote:
    """Resource Note details"""

    creation_time: float
    last_update_time: Optional[float]
    title: str
    content: str
    id: str
    created_by: str
    modified_by: Optional[str]

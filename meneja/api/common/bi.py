# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
"""BI API utils"""

import json
import os
from dataclasses import dataclass
from time import time
from typing import Dict, List, OrderedDict, Tuple

import gevent
from cubes import __version__
from cubes.browser import SPLIT_DIMENSION_NAME
from cubes.calendar import Calendar<PERSON>emberConverter
from cubes.cells import Cell, cut_from_dict, cuts_from_string
from cubes.formatters import SlicerJSONEncoder, csv_generator_p3
from cubes.model import Cube
from cubes.server.errors import NotFoundError
from cubes.server.utils import formatted_response, validated_parameter
from cubes.workspace import Workspace
from flask.globals import request
from gevent.lock import Semaphore
from werkzeug import Response

from meneja.business.bi_utils import _S3ObjectGetterBase
from meneja.business.vco.bi.customer.model import Session

WORKSPACE_IDLE_TIME = 300
API_VERSION = 2
JSON_RECORD_LIMIT = 1000


class WorkspaceManager:  # pylint: disable=too-few-public-methods
    """
    Manages access to workspaces and fans out workspaces
    after being idle for more than 5 minutes
    """

    _S3Getter: _S3ObjectGetterBase = _S3ObjectGetterBase

    @dataclass
    class WorkspaceItem:
        """Workspace details"""

        object_id: str
        last_access: int
        workspace: Workspace
        db_filename: str
        db_session: Session

    def __init__(self, base_file_dir: str):
        self._workspaces: Dict[str, self.WorkspaceItem] = {}
        self._lock = Semaphore()
        self.base_file_dir = base_file_dir
        gevent.spawn_later(60, self._vacuumer)

    def _vacuumer(self):
        try:
            workspace_bin: List[self.WorkspaceItem] = list()
            for item in self._workspaces.values():
                if _now() - item.last_access > WORKSPACE_IDLE_TIME:
                    workspace_bin.append(item)
            with self._lock:
                for item in workspace_bin:
                    del self._workspaces[item.object_id]
                    item.workspace.flush_lookup_cache()
                    os.remove(item.db_filename)
        finally:
            gevent.spawn_later(60, self._vacuumer)

    def __getitem__(self, object_id: str) -> Workspace:
        """Gets the workspace for the specified customer

        Args:
            object_id (str): Workspace object id

        Returns:
            Workspace: [description]
        """
        return self._get_workspace_item(object_id).workspace

    def get_db_session(self, object_id: str) -> Session:
        """Get a session the customers sqlite db

        Args:
            object_id (str): VCO customer id

        Returns:
            Session
        """
        return self._get_workspace_item(object_id).db_session

    def _get_workspace_item(self, object_id: str) -> "WorkspaceItem":
        # Check if a workspace exists and return if it does
        if object_id in self._workspaces:
            workspace_item = self._workspaces[object_id]
            workspace_item.last_access = _now()
            return workspace_item
        with self._lock:
            # Re-check if a workspace exists as another greenlet might have concurrently set it up
            if object_id in self._workspaces:
                workspace_item = self._workspaces[object_id]
                workspace_item.last_access = _now()
                return workspace_item
            # Create a new workspace
            s3_object_getter = self._S3Getter(object_id=object_id)
            self._update_pricing(s3_object_getter, object_id)
            workspace = Workspace()
            workspace.register_default_store("sql", url=f"sqlite:///{s3_object_getter.db_filename}")
            workspace.import_model(os.path.join(self.base_file_dir, "model.json"))
            workspace_item = self.WorkspaceItem(
                object_id, _now(), workspace, s3_object_getter.db_filename, s3_object_getter.session
            )
            self._workspaces[object_id] = workspace_item
            return workspace_item

    def _update_pricing(self, s3_object_getter, object_id):
        """Placeholder for update pricing method"""
        raise NotImplementedError("Update pricing method is not implemented")


def jsonify(obj):
    """ " Crete json response based on a slices object"""
    encoder = SlicerJSONEncoder()
    encoder.iterator_limit = JSON_RECORD_LIMIT
    data = encoder.iterencode(obj)
    return Response(data, mimetype="application/json")


def _monkey_patch_jsonify():
    from cubes.server import utils  # pylint: disable=import-outside-toplevel

    utils.jsonify = jsonify


_monkey_patch_jsonify()
del _monkey_patch_jsonify


def _now() -> int:
    return int(time())


def get_cubes_version():
    """Get cubes version"""
    info = {
        "version": __version__,
        "server_version": __version__,  # Backward compatibility key
        "api_version": API_VERSION,
    }
    return jsonify(info)


def get_workspace_info(workspace: Workspace) -> Dict:
    """Get Workspace info

    Args:
        workspace (Workspace): Workspace object

    Returns:
        Dict: Workspace info
    """
    if workspace.info:
        info = OrderedDict(workspace.info)
    else:
        info = OrderedDict()

    info["json_record_limit"] = JSON_RECORD_LIMIT
    info["cubes_version"] = __version__
    info["timezone"] = workspace.calendar.timezone_name
    info["first_weekday"] = workspace.calendar.first_weekday
    info["api_version"] = API_VERSION

    # authentication
    authinfo = {}
    authinfo["type"] = "none"
    info["authentication"] = authinfo

    return info


def prepare_cell(workspace: Workspace, cube: Cube, argname: str = "cut") -> Cell:
    """Prepare cess

    Args:
        workspace (Workspace): Workspace object
        cube (Cube): _description_
        argname (str, optional): arg name. Defaults to "cut".

    Returns:
        Cell: _description_
    """
    converters = {"time": CalendarMemberConverter(workspace.calendar)}

    cuts = []
    for cut_string in request.args.getlist(argname):
        cuts += cuts_from_string(cube, cut_string, role_member_converters=converters)

    if cuts:
        cell = Cell(cube, cuts)
    else:
        cell = None

    return cell


def get_common_parameters() -> Tuple[int, int, List[str]]:
    """Get params

    Returns:
        Tuple[int, int, List[str]]: Page, page size, order
    """
    if "page" in request.args:
        try:
            page = int(request.args.get("page"))
        except ValueError as error:
            raise ValueError("'page' should be a number") from error
    else:
        page = None

    if "pagesize" in request.args:
        try:
            page_size = int(request.args.get("pagesize"))
        except ValueError as error:
            raise ValueError("'pagesize' should be a number") from error
    else:
        page_size = None

    order = []
    for orders in request.args.getlist("order"):
        for order in orders.split(","):
            split = order.split(":")
            if len(split) == 1:
                order.append((order, None))
            else:
                order.append((split[0], split[1]))
    return page, page_size, order


def generate_cube_report(workspace: Workspace, cube_name: str):
    """Generate cube report"""
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)

    report_request = json.loads(request.data)

    try:
        queries = report_request["queries"]
    except KeyError as error:
        raise KeyError("Report request does not contain 'queries' key") from error

    cell_cuts = report_request.get("cell")

    if cell_cuts:
        # Override URL cut with the one in report
        cuts = [cut_from_dict(cut) for cut in cell_cuts]
        cell = Cell(cube, cuts)
    else:
        cell = prepare_cell(workspace, cube)
        if not cell:
            cell = Cell(cube)

    result = browser.report(cell, queries)

    return jsonify(result)


def get_cube_cell(workspace: Workspace, cube_name: str):
    """Get cube cell

    Args:
        workspace (Workspace): workspace object
        cube_name (str): Cube name

    Returns:
        JSON: Cube cell details
    """
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)

    cell = prepare_cell(workspace, cube)

    details = browser.cell_details(cell)

    if not cell:
        cell = Cell(cube)

    cell_dict = cell.to_dict()
    for cut, detail in zip(cell_dict["cuts"], details):
        cut["details"] = detail

    return jsonify(cell_dict)


def get_cube_members(workspace: Workspace, cube_name: str, dimension_name: str):
    """Get Cube members

    Args:
        workspace (Workspace): workspace object
        cube_name (str): Cube name
        dimension_name (str): Dimension name
    """
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)
    depth = request.args.get("depth")
    level = request.args.get("level")

    if depth and level:
        raise ValueError("Both depth and level provided, use only one (preferably level)")

    if depth:
        try:
            depth = int(depth)
        except ValueError as error:
            raise ValueError("depth should be an integer") from error

    try:
        dimension = cube.dimension(dimension_name)
    except KeyError as error:
        raise NotFoundError(
            dimension_name, "dimension", message=f"Dimension '{dimension_name}' was not found"
        ) from error

    hierarchy_name = request.args.get("hierarchy")
    hierarchy = dimension.hierarchy(hierarchy_name)

    if not depth and not level:
        depth = len(hierarchy)
    elif level:
        depth = hierarchy.level_index(level) + 1

    cell = prepare_cell(workspace, cube)
    page, page_size, _ = get_common_parameters()

    values = browser.members(cell, dimension, depth=depth, hierarchy=hierarchy, page=page, page_size=page_size)

    result = {
        "dimension": dimension.name,
        "hierarchy": hierarchy.name,
        "depth": len(hierarchy) if depth is None else depth,
        "data": values,
    }

    # Collect fields and labels
    attributes = []
    for level in hierarchy.levels_for_depth(depth):
        attributes += level.attributes

    fields = [attr.ref for attr in attributes]
    labels = [attr.label or attr.name for attr in attributes]

    return formatted_response(result, fields, labels, iterable=values)


def get_kube_fact(workspace: Workspace, cube_name: str, fact_id: str):
    """Get kube fact details

    Args:
        workspace (Workspace): workspace
        cube_name (str): Cube name
        fact_id (str): Fact ID
    """
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)

    fact = browser.fact(fact_id)

    if fact:
        return jsonify(fact)
    else:
        raise NotFoundError(fact_id, "fact", message=f"No fact with id '{fact_id}'")


def get_kube_facts(workspace: Workspace, cube_name: str):
    """Get Kube facts

    Args:
        workspace (Workspace): workspace object
        cube_name (str): Cube name
    """
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)

    # Request parameters
    fields_str = request.args.get("fields")
    if fields_str:
        fields = fields_str.split(",")
    else:
        fields = None

    # fields contain attribute names
    if fields:
        attributes = cube.get_attributes(fields)
    else:
        attributes = cube.all_fact_attributes

    # Construct the field list
    fields = [attr.ref for attr in attributes]

    cell = prepare_cell(workspace, cube)
    page, page_size, order = get_common_parameters()

    # Get the result
    facts = browser.facts(cell, fields=fields, order=order, page=page, page_size=page_size)

    # Add cube key to the fields (it is returned in the result)
    fields.insert(0, cube.key or "__fact_key__")

    # Construct the header
    labels = [attr.label or attr.name for attr in attributes]
    labels.insert(0, cube.key or "__fact_key__")

    return formatted_response(facts, fields, labels)


def aggregate_cube(workspace: Workspace, cube_name: str):
    """Aggregate Cube

    Args:
        workspace (Workspace): workspace
        cube_name (str): Cube name
    """
    cube = workspace.cube(cube_name)
    browser = workspace.browser(cube)

    output_format = validated_parameter(request.args, "format", values=["json", "csv"], default="json")

    header_type = validated_parameter(request.args, "header", values=["names", "labels", "none"], default="labels")

    fields_str = request.args.get("fields")
    if fields_str:
        fields = fields_str.lower().split(",")
    else:
        fields = None

    # Aggregates
    # ----------

    aggregates = []
    for agg in request.args.getlist("aggregates") or []:
        aggregates += agg.split("|")

    drilldown = []

    drill_down_list = request.args.getlist("drilldown")
    if drill_down_list:
        for drill_down_string in drill_down_list:
            drilldown += drill_down_string.split("|")

    cell = prepare_cell(workspace, cube)

    page, page_size, order = get_common_parameters()

    # split=None as I could not find where it is calculated in the original blueprint that comes with cubes
    result = browser.aggregate(
        cell, aggregates=aggregates, drilldown=drilldown, split=cell, page=page, page_size=page_size, order=order
    )

    # Hide cuts that were generated internally (default: don't)
    # if current_app.slicer.hide_private_cuts:
    #     result.cell = result.cell.public_cell()

    if output_format == "json":
        return jsonify(result)
    elif output_format != "csv":
        raise ValueError(f"unknown response format '{output_format}'")

    # csv
    if header_type == "names":
        header = result.labels
    elif header_type == "labels":
        header = []
        for label in result.labels:
            if label == SPLIT_DIMENSION_NAME:
                header.append("Matches Filters")
            else:
                header += [attr.label or attr.name for attr in cube.get_attributes([label], aggregated=True)]
    else:
        header = None

    fields = result.labels
    generator = csv_generator_p3(result, fields, include_header=bool(header), header=header)

    headers = {"Content-Disposition": 'attachment; filename="aggregate.csv"'}
    return Response(generator, mimetype="text/csv", headers=headers)

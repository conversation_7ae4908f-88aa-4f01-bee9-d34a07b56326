# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON>ISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
"""Blueprint for G8 Owner BI"""

import json
import os

from flask import Blueprint
from flask.globals import request
from flask_itsyouonline import authenticated
from werkzeug import Response

from meneja.api.common.bi import (
    WorkspaceManager,
    aggregate_cube,
    generate_cube_report,
    get_cube_cell,
    get_cube_members,
    get_cubes_version,
    get_kube_fact,
    get_kube_facts,
    get_workspace_info,
    jsonify,
)
from meneja.business.auth import requires_g8_owner
from meneja.business.bi_utils import S3CeObjectGetter
from meneja.business.meneja.bi.ce import model as G8OwnerModel
from meneja.business.vco.bi.customer.collection import update_ce_pricing
from meneja.business.vco.bi.vco.model import Settings

VIEW_INIT = (
    b'{"views":[{"charttype":"lines-stacked","chartoptions":{"showLegend":true},"mode":"chart",'
    + b'"drilldown":["unit"],"cuts":[],"datefilters":[],"columnHide":{},"columnWidths":{},"columnSort":{},'
    + b'"cubename":"spending","name":"Monthly revenue per unit","xaxis":"date@daily:month",'
    + b'"yaxis":"revenue_sum"}'
    + b',{"charttype":"bars-vertical","chartoptions":{"showLegend":true},"mode":"chart",'
    + b'"drilldown":["vco"],'
    + b'"cuts":[],"datefilters":[],"columnHide":{},"columnWidths":{},"columnSort":{},"cubename":"spending",'
    + b'"name":"Monthly revenue per vco","xaxis":"date@daily:month","yaxis":"revenue_sum"}]}'
)


class CEWorkspaceManager(WorkspaceManager):
    """CE workspace manager"""

    _S3Getter = S3CeObjectGetter

    def _update_pricing(self, s3_object_getter, object_id):
        update_ce_pricing(s3_object_getter, object_id)


workspace_manager = CEWorkspaceManager(base_file_dir=os.path.dirname(__file__))
del CEWorkspaceManager

g8_owner_slicer = Blueprint("g8_owner_slicer", __name__)


@g8_owner_slicer.route("/<g8_owner_id>/view", methods=["GET", "PUT"])
@authenticated
@requires_g8_owner
def view(g8_owner_id: str):
    """Returns the version information of this BI environment

    Args:
        g8_owner_id (str): G8 Owner ID
    """

    db_session: G8OwnerModel.Session = workspace_manager.get_db_session(g8_owner_id)
    # Create response
    settings = Settings.get_settings(db_session)
    if request.method == "GET":
        return Response(settings.view, mimetype="application/json")
    else:
        json.loads(request.data)
        settings.view = request.data
        if not json.loads(request.data)["views"]:
            settings.view = VIEW_INIT
        db_session.commit()
        return Response(settings.view, mimetype="application/json")


@g8_owner_slicer.route("/<g8_owner_id>/version")
@authenticated
@requires_g8_owner
def show_version(g8_owner_id: str):
    """Returns the version information of this BI environment

    Args:
        g8_owner_id (str): G8 Owner id
    """

    _ = workspace_manager[g8_owner_id]
    get_cubes_version()


@g8_owner_slicer.route("/<g8_owner_id>/info")
@authenticated
@requires_g8_owner
def show_info(g8_owner_id: str):
    """Return specific BI environment information

    Args:
        g8_owner_id (str): G8 Owner id
    """

    workspace = workspace_manager[g8_owner_id]

    return jsonify(get_workspace_info(workspace))


@g8_owner_slicer.route("/<g8_owner_id>/cubes")
@authenticated
@requires_g8_owner
def list_cubes(g8_owner_id: str):
    """Lists available cubes

    Args:
        g8_owner_id (str): G8 OWNER id
    """

    workspace = workspace_manager[g8_owner_id]

    return jsonify(workspace.list_cubes())


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/model")
@authenticated
@requires_g8_owner
def cube_model(g8_owner_id: str, cube_name: str):
    """Gets the cube model

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
    """

    workspace = workspace_manager[g8_owner_id]

    cube = workspace.cube(cube_name)
    response = cube.to_dict(
        expand_dimensions=True, with_mappings=False, full_attribute_names=True, create_label=True, hierarchy_limits=None
    )
    response["features"] = workspace.cube_features(cube)
    return jsonify(response)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/aggregate")
@authenticated
@requires_g8_owner
def aggregate(g8_owner_id: str, cube_name: str):
    """Aggregates cells

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
    """
    workspace = workspace_manager[g8_owner_id]
    return aggregate_cube(workspace, cube_name)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/facts")
@authenticated
@requires_g8_owner
def cube_facts(g8_owner_id: str, cube_name: str):
    """Gets cube facts

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
    """
    workspace = workspace_manager[g8_owner_id]
    return get_kube_facts(workspace, cube_name)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/fact/<fact_id>")
@authenticated
@requires_g8_owner
def cube_fact(g8_owner_id: str, cube_name, fact_id):
    """Gets cube facts

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
        fact_id (str): Fact
    """
    workspace = workspace_manager[g8_owner_id]
    return get_kube_fact(workspace, cube_name, fact_id)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/members/<dimension_name>")
@authenticated
@requires_g8_owner
def cube_members(g8_owner_id: str, cube_name: str, dimension_name: str):
    """Gets the cube members

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
        dimension_name (str): Dimension name
    """

    workspace = workspace_manager[g8_owner_id]
    return get_cube_members(workspace, cube_name, dimension_name)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/cell")
@authenticated
@requires_g8_owner
def cube_cell(g8_owner_id: str, cube_name: str):
    """Gets a cube cell

    Args:
        g8_owner_id (str): G8 OWNER id
        cube_name (str): Cube name
    """
    workspace = workspace_manager[g8_owner_id]
    return get_cube_cell(workspace, cube_name)


@g8_owner_slicer.route("/<g8_owner_id>/cube/<cube_name>/report", methods=["GET", "POST"])
@authenticated
@requires_g8_owner
def cube_report(g8_owner_id: str, cube_name: str):
    """Gets a cube report

    Args:
        cube_name (str): Cube name
    """
    workspace = workspace_manager[g8_owner_id]
    return generate_cube_report(workspace, cube_name)

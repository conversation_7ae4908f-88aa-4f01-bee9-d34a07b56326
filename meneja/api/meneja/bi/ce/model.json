{"cubes": [{"name": "spending", "fact": "spending", "key": "id", "label": "Ft Spending", "dimensions": ["unit", "date", "space", "vco"], "measures": ["amount", "cost", "revenue", "profit"], "aggregates": [{"name": "amount_sum", "function": "sum", "measure": "amount"}, {"name": "cost_sum", "function": "sum", "measure": "cost"}, {"name": "revenue_sum", "function": "sum", "measure": "revenue"}, {"name": "profit_sum", "function": "sum", "measure": "profit"}], "joins": [{"master": "spending.unit_id", "detail": "dim_unit.id"}, {"master": "spending.vco_id", "detail": "dim_vco.id"}, {"alias": "space_id", "detail": "dim_space.id", "master": "spending.space_id"}, {"alias": "space_id_cluster_id", "detail": "dim_cluster.id", "master": "space_id.cluster_id"}, {"alias": "space_id_cluster_id_datacenter_id", "detail": "dim_datacenter.id", "master": "space_id_cluster_id.datacenter_id"}, {"alias": "space_id_cluster_id_datacenter_id_city_id", "detail": "dim_city.id", "master": "space_id_cluster_id_datacenter_id.city_id"}, {"alias": "space_id_cluster_id_datacenter_id_city_id_country_id", "detail": "dim_country.id", "master": "space_id_cluster_id_datacenter_id_city_id.country_id"}, {"alias": "space_id_type_id", "detail": "dim_spacetype.id", "master": "space_id.type_id"}], "mappings": {"amount": "spending.amount", "cost": "spending.cost", "revenue": "spending.revenue", "profit": "spending.profit", "date.day": {"column": "date", "extract": "day", "table": "spending"}, "date.month": {"column": "date", "extract": "month", "table": "spending"}, "date.week": {"column": "date", "extract": "week", "table": "spending"}, "date.year": {"column": "date", "extract": "year", "table": "spending"}, "unit": "dim_unit.name", "vco": "dim_vco.name", "id": "spending.id", "space.country": "space_id_cluster_id_datacenter_id_city_id_country_id.name", "space.country_code": "space_id_cluster_id_datacenter_id_city_id_country_id.code", "space.city": "space_id_cluster_id_datacenter_id_city_id.name", "space.city_code": "space_id_cluster_id_datacenter_id_city_id.code", "space.datacenter": "space_id_cluster_id_datacenter_id.name", "space.datacenter_code": "space_id_cluster_id_datacenter_id.code", "space.cluster": "space_id_cluster_id.code", "space.space_type": "space_id_type_id.name", "space.name": "space_id.name", "space.code": "space_id.code"}}], "dimensions": [{"name": "unit", "label": "Unit", "attributes": ["name"]}, {"name": "vco", "label": "VCO", "attributes": ["name"]}, {"name": "space", "label": "Space", "levels": [{"name": "country", "label": "Country", "attributes": ["country", "country_code"], "label_attribute": "country"}, {"name": "city", "label": "City", "attributes": ["city", "city_code"], "label_attribute": "city"}, {"name": "datacenter", "label": "Datacenter", "attributes": ["datacenter", "datacenter_code"], "label_attribute": "datacenter"}, {"name": "cluster", "label": "Cluster", "attributes": ["cluster"], "label_attribute": "cluster"}, {"name": "space_type", "label": "Space type", "attributes": ["space_type"], "label_attribute": "space_type"}]}, {"name": "date", "role": "time", "hierarchies": [{"label": "Daily", "levels": ["year", "month", "day"], "name": "daily"}, {"label": "Weekly", "levels": ["year", "week"], "name": "weekly"}], "info": {"cv-datefilter": true, "cv-datefilter-hierarchy": "daily"}, "label": "Date", "levels": [{"attributes": ["year"], "label": "Year", "label_attribute": "year", "name": "year", "order_attribute": "year", "role": "year"}, {"attributes": ["month"], "label": "Month", "label_attribute": "month", "name": "month", "order_attribute": "month", "role": "month"}, {"attributes": ["day"], "label": "Day", "label_attribute": "day", "name": "day", "order_attribute": "day", "role": "day"}, {"attributes": ["week"], "label": "Week", "label_attribute": "week", "name": "week", "order_attribute": "week", "role": "week"}]}]}
# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
"""Blueprint for Gig BI"""

import json
import os

from flask import Blueprint
from flask.globals import request
from flask_itsyouonline import authenticated
from werkzeug import Response

from meneja.api.common.bi import (
    WorkspaceManager,
    aggregate_cube,
    generate_cube_report,
    get_cube_cell,
    get_cube_members,
    get_cubes_version,
    get_kube_fact,
    get_kube_facts,
    get_workspace_info,
    jsonify,
)
from meneja.business.auth import requires_admin
from meneja.business.bi_utils import S3GigObjectGetter
from meneja.business.meneja.bi.gig import model as GigModel
from meneja.business.meneja.bi.gig.collection import update_gig_pricing
from meneja.business.meneja.bi.gig.model import Settings

GIG = "gig"

VIEW_INIT = bytes(
    """{"views":[{"charttype":"lines-stacked","chartoptions":{"showLegend":true},"mode":"chart",
            "drilldown":["unit"],"cuts":[],"datefilters":[],"columnHide":{},"columnWidths":{},"columnSort":{},
            "cubename":"spending","name":"Monthly revenue per unit","xaxis":"date@daily:month",
            "yaxis":"revenue_sum"}
            ,{"charttype":"bars-vertical","chartoptions":{"showLegend":true},"mode":"chart",
            "drilldown":["cloudenabler"],
            "cuts":[],"datefilters":[],"columnHide":{},"columnWidths":{},"columnSort":{},"cubename":"spending",
            "name":"Monthly revenue per cloudenabler","xaxis":"date@daily:month","yaxis":"revenue_sum"}]}""",
    "utf-8",
)


class GIGWorkspaceManager(WorkspaceManager):
    """GIG workspace manager"""

    _S3Getter = S3GigObjectGetter

    def _update_pricing(self, s3_object_getter, object_id):
        _ = object_id
        update_gig_pricing(s3_object_getter)


workspace_manager = GIGWorkspaceManager(base_file_dir=os.path.dirname(__file__))
del GIGWorkspaceManager

gig_slicer = Blueprint("gig_slicer", __name__)


@gig_slicer.route("/view", methods=["GET", "PUT"])
@authenticated
@requires_admin
def view():
    """Returns the version information of this BI environment

    Args:
        gig (str): Gig
    """
    # Authorize and get bi workspace
    db_session: GigModel.Session = workspace_manager.get_db_session(GIG)
    # Create response
    settings = Settings.get_settings(db_session)
    if request.method == "GET":
        return Response(settings.view, mimetype="application/json")
    else:
        if not json.loads(request.data)["views"]:
            settings.view = VIEW_INIT
        else:
            settings.view = request.data
        db_session.commit()
        return Response(settings.view, mimetype="application/json")


@gig_slicer.route("/version")
@authenticated
@requires_admin
def show_version():
    """Returns the version information of this BI environment"""
    # Authorize and get bi workspace
    _ = workspace_manager[GIG]
    get_cubes_version()


@gig_slicer.route("/info")
@authenticated
@requires_admin
def show_info():
    """Return specific BI environment information

    Args:
        gig (str): Gig
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    # Calculate result
    return jsonify(get_workspace_info(workspace))


@gig_slicer.route("/cubes")
@authenticated
@requires_admin
def list_cubes():
    """Lists available cubes

    Args:
        gig (str): Gig
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    # Calculate result
    return jsonify(workspace.list_cubes())


@gig_slicer.route("/cube/<cube_name>/model")
@authenticated
@requires_admin
def cube_model(cube_name: str):
    """Gets the cube model

    Args:
        gig (str): Gig
        cube_name (str): Cube name
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    # Calculate result
    cube = workspace.cube(cube_name)
    response = cube.to_dict(
        expand_dimensions=True, with_mappings=False, full_attribute_names=True, create_label=True, hierarchy_limits=None
    )
    response["features"] = workspace.cube_features(cube)
    return jsonify(response)


@gig_slicer.route("/cube/<cube_name>/aggregate")
@authenticated
@requires_admin
def aggregate(cube_name: str):
    """Aggregates cells

    Args:
        gig (str): Gig
        cube_name (str): Cube name
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return aggregate_cube(workspace, cube_name)


@gig_slicer.route("/cube/<cube_name>/facts")
@authenticated
@requires_admin
def cube_facts(cube_name: str):
    """Gets cube facts

    Args:
        gig (str): Gig
        cube_name (str): Cube name
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return get_kube_facts(workspace, cube_name)


@gig_slicer.route("/cube/<cube_name>/fact/<fact_id>")
@authenticated
@requires_admin
def cube_fact(cube_name, fact_id):
    """Gets cube facts

    Args:
        gig (str): Gig
        cube_name (str): Cube name
        fact_id (str): Fact
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return get_kube_fact(workspace, cube_name, fact_id)


@gig_slicer.route("/cube/<cube_name>/members/<dimension_name>")
@authenticated
@requires_admin
def cube_members(cube_name: str, dimension_name: str):
    """Gets the cube members

    Args:
        gig (str): Gig
        cube_name (str): Cube name
        dimension_name (str): Dimension name
    """

    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return get_cube_members(workspace, cube_name, dimension_name)


@gig_slicer.route("/cube/<cube_name>/cell")
@authenticated
@requires_admin
def cube_cell(cube_name: str):
    """Gets a cube cell

    Args:
        gig (str): Gig
        cube_name (str): Cube name
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return get_cube_cell(workspace, cube_name)


@gig_slicer.route("/cube/<cube_name>/report", methods=["GET", "POST"])
@authenticated
@requires_admin
def cube_report(cube_name: str):
    """Gets a cube report

    Args:
        cube_name (str): Cube name
    """
    # Authorize and get bi workspace
    workspace = workspace_manager[GIG]
    return generate_cube_report(workspace, cube_name)

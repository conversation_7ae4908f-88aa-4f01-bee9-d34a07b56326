{"cubes": [{"name": "spending", "fact": "spending", "key": "id", "label": "Ft Spending", "dimensions": ["unit", "date", "cluster", "cloud<PERSON><PERSON>"], "measures": ["amount", "revenue"], "aggregates": [{"name": "amount_sum", "function": "sum", "measure": "amount"}, {"name": "revenue_sum", "function": "sum", "measure": "revenue"}], "joins": [{"master": "spending.unit_id", "detail": "dim_unit.id"}, {"master": "spending.cloud_enabler_id", "detail": "dim_cloud_enabler.id"}, {"alias": "cluster_id", "detail": "dim_cluster.id", "master": "spending.cluster_id"}, {"alias": "cluster_id_datacenter_id", "detail": "dim_datacenter.id", "master": "cluster_id.datacenter_id"}, {"alias": "cluster_id_datacenter_id_city_id", "detail": "dim_city.id", "master": "cluster_id_datacenter_id.city_id"}, {"alias": "cluster_id_datacenter_id_city_id_country_id", "detail": "dim_country.id", "master": "cluster_id_datacenter_id_city_id.country_id"}], "mappings": {"amount": "spending.amount", "revenue": "spending.revenue", "date.day": {"column": "date", "extract": "day", "table": "spending"}, "date.month": {"column": "date", "extract": "month", "table": "spending"}, "date.week": {"column": "date", "extract": "week", "table": "spending"}, "date.year": {"column": "date", "extract": "year", "table": "spending"}, "unit": "dim_unit.name", "cloudenabler": "dim_cloud_enabler.name", "id": "spending.id", "cluster.country": "cluster_id_datacenter_id_city_id_country_id.name", "cluster.country_code": "cluster_id_datacenter_id_city_id_country_id.code", "cluster.city": "cluster_id_datacenter_id_city_id.name", "cluster.city_code": "cluster_id_datacenter_id_city_id.code", "cluster.datacenter": "cluster_id_datacenter_id.name", "cluster.datacenter_code": "cluster_id_datacenter_id.code", "cluster.code": "cluster_id.code"}}], "dimensions": [{"name": "unit", "label": "Unit", "attributes": ["name"]}, {"name": "cloud<PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "attributes": ["name"]}, {"name": "cluster", "label": "Cluster", "levels": [{"name": "country", "label": "Country", "attributes": ["country", "country_code"], "label_attribute": "country"}, {"name": "city", "label": "City", "attributes": ["city", "city_code"], "label_attribute": "city"}, {"name": "datacenter", "label": "Datacenter", "attributes": ["datacenter", "datacenter_code"], "label_attribute": "datacenter"}, {"name": "cluster", "label": "G8", "attributes": ["code"]}]}, {"name": "date", "role": "time", "hierarchies": [{"label": "Daily", "levels": ["year", "month", "day"], "name": "daily"}, {"label": "Weekly", "levels": ["year", "week"], "name": "weekly"}], "info": {"cv-datefilter": true, "cv-datefilter-hierarchy": "daily"}, "label": "Date", "levels": [{"attributes": ["year"], "label": "Year", "label_attribute": "year", "name": "year", "order_attribute": "year", "role": "year"}, {"attributes": ["month"], "label": "Month", "label_attribute": "month", "name": "month", "order_attribute": "month", "role": "month"}, {"attributes": ["day"], "label": "Day", "label_attribute": "day", "name": "day", "order_attribute": "day", "role": "day"}, {"attributes": ["week"], "label": "Week", "label_attribute": "week", "name": "week", "order_attribute": "week", "role": "week"}]}]}
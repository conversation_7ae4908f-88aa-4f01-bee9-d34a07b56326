# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring
import logging

import yaml
from flask import Response, current_app, request, send_file
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from mongoengine import DoesNotExist
from requests.exceptions import HTTPError

from meneja.api import api
from meneja.api.meneja import SUCCESS
from meneja.business import backups as backup_business
from meneja.business import g8 as G8_bizz
from meneja.business import g8_config, g8_installer_image, g8_version, security
from meneja.business.auth import check_g8_accessibility, get_accessible_g8s, requires_admin
from meneja.business.db_model_2_rest import generate_request_parser, generate_restfull_model_spec
from meneja.business.g8 import g8_network
from meneja.business.g8 import hardware as G8_hardware
from meneja.business.g8.capacity import get_g8_capacity_series
from meneja.business.g8.g8 import get_g8_external_network_details
from meneja.business.g8.g8_api import G8Client
from meneja.business.g8.g8_node_status_archive import get_g8_power_status_history
from meneja.business.g8.hardware import list_g8_nodes
from meneja.business.g8.performance import get_g8_stat_series
from meneja.business.g8_owners import set_g8_owner_id
from meneja.business.g8_status import g8_update_notify
from meneja.business.g8_vgpus import list_active_vgpus, update_gpu_config
from meneja.lib.clients.g8.client import G8ClientApiV1
from meneja.lib.enumeration import (
    AccessRight,
    ArakoonLocation,
    G8ExternalNetworkStats,
    G8HealthCheck,
    G8StatsEnum,
    Notify,
)
from meneja.lib.fixes import FixedArgument
from meneja.lib.meneja_g8_mapping import gen_arg_parser, generate_api_response_model
from meneja.lib.utils import from_dict_with_enum
from meneja.model.g8 import BackupConfig as BackupConfigModel
from meneja.model.g8 import DefaultBackupConfig as DefaultBackupConfigModel
from meneja.model.g8 import (
    G8CapacityConsumption,
    G8CDROMImage,
    G8HardwareSpec,
    G8Info,
    G8OSInstallerImage,
    G8VMOSImage,
    Partition,
    VMAgentRequirements,
)
from meneja.structs.dataclasses.node_disk import NodeDiskStruct
from meneja.structs.meneja.dataclasses.backups_settings import BackupSettingsStruct
from meneja.structs.meneja.dataclasses.externalnetwork import ExternalNetworkStruct
from meneja.structs.meneja.dataclasses.g8 import G8DocumentationStruct, SwitchManufacturer, SwitchModelName
from meneja.structs.meneja.dataclasses.g8_node_block import G8PowerStatusStruct
from meneja.structs.meneja.dataclasses.vgpuStruct import GPUStruct, VGPUStruct
from meneja.structs.meneja.g8.node import NodeStruct
from meneja.structs.vco.dataclasses.success import SuccessModel

logger = logging.getLogger()
ns = api.namespace("g8s", description="G8 deployments")

# models
g8_model = api.model("G8", generate_restfull_model_spec(G8Info, api=api))
g8s_model = api.model("G8s", generate_restfull_model_spec(G8Info, exported_only=True, api=api))
installer_image_model = api.model("G8InstallerImage", generate_restfull_model_spec(G8OSInstallerImage))
installer_images_model_dict = generate_restfull_model_spec(G8OSInstallerImage, exported_only=True)
additional_args = {
    "id": fields.String(readOnly=True, description="Image ID"),
    "download_url": fields.String(readOnly=True, description="Download URL"),
}
installer_images_model_dict.update(additional_args)
installer_images_model = api.model("G8InstallerImage", installer_images_model_dict)
g8_config_model = api.model(
    "G8Configuration",
    {
        "content": fields.String("Configuration to be validated", required=True),
    },
)
update_g8_config_model = api.model(
    "UpdateG8Configuration",
    {
        "content": fields.String("New configuration", required=True),
        "commit_message": fields.String("gitlab commit message", required=True),
    },
)
upgrade_g8_model = api.model(
    "UpgradeG8",
    {
        "version": fields.String("Software version", required=True),
        "download_only": fields.Boolean(default=False, description="only download new release", required=False),
    },
)
set_backup_config_model = api.model(
    "SetBackupConfig",
    {
        "url": fields.String("S3 url", required=True),
        "bucket_name": fields.String("S3 Bucket Name", required=True),
        "access_key": fields.String("S3 access key", required=True),
        "secret": fields.String("S3 secret", required=True),
    },
)
backup_config_model = api.model("BackupConfig", generate_restfull_model_spec(BackupConfigModel, api=api))
update_gpu_config_api_model = api.model("gpuID", {"gpu_id": fields.String(description="GPU ID")})

# parsers
node_argparser = reqparse.RequestParser(argument_class=FixedArgument)
node_argparser.add_argument("g8_block", type=str, default=None, help="filter by block name")
node_argparser.add_argument("deleted", type=bool, default=False, help="filter deleted nodes")
update_g8_healthcheck_parser = reqparse.RequestParser(argument_class=FixedArgument)
update_g8_healthcheck_parser.add_argument(
    "healthcheck",
    type=str,
    location="args",
    default=G8HealthCheck.ACTIVE.value,
    help="Health check",
    choices=G8HealthCheck.values(),
)

update_g8_resource_profile_parser = reqparse.RequestParser(argument_class=FixedArgument)
update_g8_resource_profile_parser.add_argument(
    "resource-profile",
    type=str,
    location="args",
    help="Resource Profile check",
)
update_g8_upgrade_stage_parser = reqparse.RequestParser(argument_class=FixedArgument)
update_g8_upgrade_stage_parser.add_argument(
    "upgrade_stage", type=str, location="args", required=True, help="G8 upgrade label (e.g. ALPHA, BETA, GAMMA)"
)
success_model = api.model("SuccessModel", {"success": fields.Boolean()})
update_g8_jwt_parser = reqparse.RequestParser(argument_class=FixedArgument)
update_g8_jwt_parser.add_argument("jwt", type=str, location="args", required=True, help="Refreshable jwt of the G8")

list_g8s_argparser = reqparse.RequestParser(argument_class=FixedArgument)
list_g8s_argparser.add_argument(
    "datacenter", type=str, location="args", default=None, help="Filter G8s by datacenter id"
)
list_g8s_argparser.add_argument(
    "include_deleted", type=boolean, location="args", default=False, help="Flag to include deleted g8s"
)
g8_config_argparser = reqparse.RequestParser(argument_class=FixedArgument)
g8_config_argparser.add_argument("Accept", type=str, location="headers", default="text/plain")
generate_g8_config_parser = reqparse.RequestParser(argument_class=FixedArgument)
generate_g8_config_parser.add_argument(
    "force",
    type=boolean,
    default=False,
    location="args",
    help="If True, will be Created using G8HardwareSpec and G8Info and pushed to the gitlab repository. If"
    " false, will only generate and push it if it's out of date",
)
get_list_installer_image_parser = reqparse.RequestParser(argument_class=FixedArgument)
get_list_installer_image_parser.add_argument(
    "with_download_url", type=boolean, location="args", default=False, help="Flag to include download url in response"
)

network_parser = reqparse.RequestParser(argument_class=FixedArgument)
network_parser.add_argument("network", type=str, location="args")
network_parser.add_argument("gateway", type=str, location="args")
network_parser.add_argument("vlan", type=int, location="args")

post_serialnr_switch_parser = reqparse.RequestParser(argument_class=FixedArgument)
post_serialnr_switch_parser.add_argument("serial_number", required=True, type=str, location="args")

postr_node_macaddr_parser = reqparse.RequestParser(argument_class=FixedArgument)
postr_node_macaddr_parser.add_argument("mac_address", required=True, type=str, location="args")

postr_node_password_parser = reqparse.RequestParser(argument_class=FixedArgument)
postr_node_password_parser.add_argument("password", required=True, type=str, location="args")

postr_node_username_parser = reqparse.RequestParser(argument_class=FixedArgument)
postr_node_username_parser.add_argument("username", required=True, type=str, location="args")

meneja_ipaddr_parser = reqparse.RequestParser(argument_class=FixedArgument)
meneja_ipaddr_parser.add_argument("ip_address", required=True, type=str, location="args")

ipaddr_parser = reqparse.RequestParser(argument_class=FixedArgument)
ipaddr_parser.add_argument("ip_address", required=True, type=str, location="args")
ipaddr_parser.add_argument("network", required=True, type=str, location="args")
ipaddr_parser.add_argument("gateway", required=True, type=str, location="args")

github_support_parser = reqparse.RequestParser(argument_class=FixedArgument)
github_support_parser.add_argument("client_id", required=True, type=str, location="args")
github_support_parser.add_argument("client_secret", required=True, type=str, location="args")
github_support_parser.add_argument("org", required=True, type=str, location="args")
github_support_parser.add_argument("team", required=True, type=str, location="args")

g8_image_argparser = generate_request_parser(G8VMOSImage, editable_only=True)
g8_cdrom_argparser = generate_request_parser(G8CDROMImage, editable_only=True)
g8_os_image_model = api.model("G8VMOSImage", generate_restfull_model_spec(G8VMOSImage, api=api))
g8_os_image_model["name"] = fields.String(readOnly=True, description="Image name")
g8_os_image_model["availability"] = fields.String(readOnly=True, description="Image availability")

g8_cdrom_model = api.model("G8CDROMImage", generate_restfull_model_spec(G8CDROMImage, api=api))
g8_cdrom_model["name"] = fields.String(readOnly=True, description="Image name")
g8_cdrom_model["availability"] = fields.String(readOnly=True, description="Image availability")

domain_parser = reqparse.RequestParser(argument_class=FixedArgument)
domain_parser.add_argument("domain", required=True, type=str, location="args")

g8_consumption_model = api.model("G8Consumption", generate_restfull_model_spec(G8CapacityConsumption, api=api))

capacity_series_model = api.model(
    "CapacitySeries",
    {
        "total": fields.List(fields.Float(description="Total capacity series")),
        "used": fields.List(fields.Float(description="Used capacity series")),
    },
)

backend_series_model = api.model(
    "BackendCapacity",
    {
        "name": fields.String(description="Backend name"),
        "capacity": fields.Nested(
            api.model(
                "BackendCapacitySeries",
                {
                    "total": fields.List(fields.Float(description="Total backend capacity series")),
                    "used": fields.List(fields.Float(description="Used backend capacity series")),
                },
            )
        ),
    },
)

g8_consumption_archive_model = api.model(
    "G8ConsumptionArchive",
    {
        "timeseries": fields.List(fields.Integer(description="Timeseries")),
        "cu": fields.Nested(capacity_series_model),
        "vcu": fields.Nested(capacity_series_model),
        "su": fields.Nested(capacity_series_model),
        "ou": fields.Nested(capacity_series_model),
        "piu": fields.Nested(capacity_series_model),
        "mu": fields.Nested(capacity_series_model),
        "backends": fields.List(fields.Nested(backend_series_model)),
    },
)

objectstorage_asd_parser = reqparse.RequestParser(argument_class=FixedArgument)
objectstorage_asd_parser.add_argument(
    "objectstorage_asd",
    required=True,
    type=float,
    location="args",
    help="Share of the total G8 storage that will be used by Object storage",
)

disk_partitions = api.model("partitions", generate_restfull_model_spec(Partition, api=api))

g8_hardware_specs = api.model("G8Hardware specs", generate_restfull_model_spec(G8HardwareSpec, api=api))

g8_account_create = gen_arg_parser("cloudapi/accounts/create")

g8_account_delete = gen_arg_parser("cloudapi/accounts/delete")

g8_account_restore = gen_arg_parser("cloudapi/accounts/restore")

g8_account_rename = gen_arg_parser("cloudapi/accounts/update$rename")

g8_account_quota = gen_arg_parser("cloudapi/accounts/update$quota")

g8_add_user_parser = gen_arg_parser("cloudapi/accounts/addUser")
# Add access right here to add choices
g8_add_user_parser.add_argument(
    "access_right",
    type=str,
    default=AccessRight.ADMIN.name,
    location="args",
    help="User access rights on the account",
    choices=list(AccessRight.__members__.keys()),
)

g8_update_user_parser = gen_arg_parser("cloudapi/accounts/updateUser")
# Add access right here to add choices
g8_update_user_parser.add_argument(
    "access_right",
    type=str,
    default=AccessRight.ADMIN.name,
    location="args",
    help="User access rights on the account",
    choices=list(AccessRight.__members__.keys()),
)

g8_del_user_parser = gen_arg_parser("cloudapi/accounts/deleteUser")

g8_enable_parser = gen_arg_parser("cloudapi/accounts/enable")
g8_disable_parser = gen_arg_parser("cloudapi/accounts/disable")

g8_add_vco_parser = reqparse.RequestParser(argument_class=FixedArgument)
g8_add_vco_parser.add_argument("vco_id", type=str, location="args", help="Add VCO on the G8")

add_vco_access_argparser = reqparse.RequestParser(argument_class=FixedArgument)
add_vco_access_argparser.add_argument("vco_id", type=str, location="args", required=True, help="VCO ID")

set_g8_owner_argparser = reqparse.RequestParser(argument_class=FixedArgument)
set_g8_owner_argparser.add_argument("g8owner_id", type=str, location="args", required=True, help="G8 Owner ID")

get_g8_series_parser = reqparse.RequestParser(argument_class=FixedArgument)
get_g8_series_parser.add_argument("start", type=int, location="args", help="Start timestamp")
get_g8_series_parser.add_argument("end", type=int, location="args", help="End timestamp")
get_g8_series_parser.add_argument(
    "sample_count", type=int, location="args", help="Number of samples of the moving average"
)
vm_agent_requirements = generate_request_parser(VMAgentRequirements)


post_user_name_access_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_user_name_access_switch.add_argument("user_name", required=True, type=str, location="args")
post_password_access_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_password_access_switch.add_argument("password", required=True, type=str, location="args")
post_user_name_management_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_user_name_management_switch.add_argument("user_name", required=True, type=str, location="args")
post_password_management_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_password_management_switch.add_argument("password", required=True, type=str, location="args")
post_ip_management_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_ip_management_switch.add_argument("ip_address", required=True, type=str, location="args")
post_ip_access_switch = reqparse.RequestParser(argument_class=FixedArgument)
post_ip_access_switch.add_argument("ip_address", required=True, type=str, location="args")

get_node_status_history_parser = reqparse.RequestParser(argument_class=FixedArgument)
get_node_status_history_parser.add_argument("start", type=int, location="args", help="Start timestamp")
get_node_status_history_parser.add_argument("end", type=int, location="args", help="End timestamp")


@ns.route("/")
class G8s(Resource):
    @authenticated
    @ns.marshal_list_with(g8s_model)
    @ns.expect(list_g8s_argparser)
    @ns.doc(shortcut="ListG8s", description="Lists the G8's which the user is authorized to manage")
    def get(self):
        args = list_g8s_argparser.parse_args()
        datacenter = args.get("datacenter")
        include_deleted = args.get("include_deleted")
        accessible_g8s = get_accessible_g8s()
        return G8_bizz.list_g8s(accessible_g8s, include_deleted=include_deleted, datacenter=datacenter)


@ns.route("/<g8_name>")
class G8(Resource):
    @authenticated
    @ns.marshal_with(g8_model)
    @ns.doc(shortcut="getG8Info", description="Get G8 information")
    @check_g8_accessibility
    def get(self, g8_name):
        return G8_bizz.get_by_name(g8_name)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deleteG8Info", description="Delete G8")
    @check_g8_accessibility
    def delete(self, g8_name):
        G8_bizz.delete(g8_name)
        return True, 200


@ns.route("/<g8_name>/healthcheck")
class G8Healthcheck(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8Healthcheck", description="Update G8 healthcheck")
    @check_g8_accessibility
    @ns.expect(update_g8_healthcheck_parser)
    def put(self, g8_name):
        args = update_g8_healthcheck_parser.parse_args()
        healthcheck = args.get("healthcheck")
        G8_bizz.update_g8_healthcheck(g8_name, healthcheck=healthcheck)


@ns.route("/<g8_name>/upgrade-stage")
class G8UpgradeStage(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8UpgradeStage", description="Update G8 upgrade stage")
    @check_g8_accessibility
    @ns.expect(update_g8_upgrade_stage_parser)
    def put(self, g8_name):
        args = update_g8_upgrade_stage_parser.parse_args()
        upgrade_stage = args.get("upgrade_stage")
        G8_bizz.update_g8_upgrade_stage(g8_name, upgrade_stage=upgrade_stage)


@ns.route("/<g8_name>/jwt")
class G8JWT(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8JWT", description="Update G8 JWT token")
    @check_g8_accessibility
    @ns.expect(update_g8_jwt_parser)
    def put(self, g8_name):
        args = update_g8_jwt_parser.parse_args()
        jwt = args.get("jwt")
        G8_bizz.update_g8_jwt(g8_name, jwt=jwt)


@ns.route("/<g8_name>/installer-images")
class G8InstallerImages(Resource):
    @authenticated
    @ns.doc(shortcut="listG8InstallerImages", description="List G8 installer images")
    @ns.marshal_list_with(installer_images_model)
    @ns.expect(get_list_installer_image_parser)
    @check_g8_accessibility
    def get(self, g8_name):
        args = get_list_installer_image_parser.parse_args()
        with_download_url = args.get("with_download_url")
        return g8_installer_image.list_g8_installer_images(g8_name, with_download_url)

    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="generateG8InstallerImage",
        description="Generate G8 installer image",
        params={"password": "password", "version": "version"},
    )
    @ns.marshal_with(installer_image_model)
    @check_g8_accessibility
    def post(self, g8_name):
        iso_template = current_app.config["ISO_TEMPLATE"]
        password = request.args.get("password")
        version = request.args.get("version")
        user_info = get_current_user_info()
        return g8_installer_image.create(g8_name, version, iso_template, password, user_info.username, user_info.jwt)


@ns.route("/<g8_name>/installer-images/<image_id>")
class G8InstallerImage(Resource):
    @authenticated
    @ns.doc(shortcut="getG8InstallerImage", description="Get G8 installer image")
    @ns.expect(get_list_installer_image_parser)
    @ns.marshal_with(installer_image_model)
    @check_g8_accessibility
    def get(self, g8_name, image_id):  # pylint: disable=unused-argument
        args = get_list_installer_image_parser.parse_args()
        with_download_url = args.get("with_download_url")
        try:
            image = g8_installer_image.get(image_id, with_download_url)
        except DoesNotExist:
            logger.exception("Image not found")
            return "Image not found", 404
        return image

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deleteG8InstallerImage", description="Delete G8 installer image")
    @check_g8_accessibility
    def delete(self, g8_name, image_id):
        g8_installer_image.delete(g8_name, image_id)
        return True, 200


@ns.route("/<g8_name>/installer-images/<image_id>/download")
class DownloadG8InstallerImage(Resource):
    @authenticated
    @ns.doc(shortcut="downloadG8InstallerImage", description="Get download url of specific g8 installer image")
    @check_g8_accessibility
    def get(self, g8_name, image_id):  # pylint: disable=unused-argument
        try:
            url = g8_installer_image.download(image_id)
        except DoesNotExist:
            logger.exception("Image not found")
            return "Image not found", 404
        return url


@ns.route("/<g8_name>/config")
class G8Config(Resource):
    @authenticated
    @ns.doc(shortcut="getG8Config", description="Get the G8's configuration")
    @ns.expect(g8_config_argparser)
    @check_g8_accessibility
    def get(self, g8_name):
        args = g8_config_argparser.parse_args()
        accept = args.get("Accept")
        as_text = True if "text/plain" in accept else False
        config, _, _ = g8_config.get(g8_name, as_text=as_text)
        return config

    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8Config", description="Update G8's configuration")
    @check_g8_accessibility
    @ns.expect(update_g8_config_model)
    def put(self, g8_name):
        data = ns.payload
        user_info = get_current_user_info()
        author_name = f"{user_info.firstname} {user_info.lastname}"
        try:
            config = yaml.safe_load(data["content"])
            g8_config.update(g8_name, config, author_name, user_info.email, data["commit_message"])
        except (g8_config.SchemaValidationError, yaml.parser.ParserError, security.SecurityValidationError) as err:
            logger.exception("Failed to update configuration")
            return str(err), 400
        return True, 201

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deployG8Config", description="deploy last saved G8's configuration to the environment")
    @check_g8_accessibility
    def post(self, g8_name):
        config, _, commit_id = g8_config.get(g8_name)
        g8_config.push_to_env(g8_name, config, commit_id)


@ns.route("/<g8_name>/config/regenerate")
class GenerateSystemConfig(Resource):
    @authenticated
    @ns.doc(shortcut="regenerateG8Config", description="Regenerate the G8's configuration")
    @ns.expect(generate_g8_config_parser)
    @requires_admin
    def post(self, g8_name):
        kwargs = generate_g8_config_parser.parse_args()
        return g8_config.create(g8_name, **kwargs)


@ns.route("/<g8_name>/config/download-config-files")
class DownloadG8Config(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadG8ConfigFiles", description="Download G8 configration files", params={"password": "password"}
    )
    @check_g8_accessibility
    def get(self, g8_name):
        password = request.args.get("password")
        jwt = get_current_user_info().jwt
        try:
            buff = g8_config.download(g8_name, password, jwt)
        except security.SecurityValidationError as err:
            logger.exception("Security validation error")
            return str(err), 400
        return send_file(buff, as_attachment=True, attachment_filename="config.tar")


@ns.route("/<g8_name>/version/")
class Version(Resource):
    @authenticated
    @ns.doc(
        shortcut="listVersions",
        description="Get available versions",
    )
    def get(self, g8_name):
        return g8_version.list_new_versions(g8_name)

    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="upgrade",
        description="Upgrade the G8 to the specified version",
    )
    @ns.expect(upgrade_g8_model)
    def post(self, g8_name):
        version = ns.payload["version"]
        download_only = ns.payload.get("download_only", False)
        return g8_version.upgrade(g8_name, version, download_only)


@ns.route("/versions/")
class Versions(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="listAllVersions",
        description="Get all available versions",
    )
    def get(self):
        return g8_version.list_all_new_versions()


@ns.route("/config/validate")
class Config(Resource):
    @authenticated
    @ns.doc(shortcut="validateConfig", description="Validate configuration")
    @ns.expect(g8_config_model)
    def post(self):
        data = ns.payload
        try:
            config = yaml.safe_load(data["content"])
            g8_config.validate(config, previous_config=config)
        except (g8_config.SchemaValidationError, yaml.parser.ParserError, security.SecurityValidationError) as err:
            logger.exception("Security validation error.")
            return str(err), 400
        return True


@ns.route("/<g8_name>/notification")
class G8Notifications(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="enableG8Notification", description="Enable G8 notification")
    @check_g8_accessibility
    def post(self, g8_name):
        g8_update_notify(g8_name, Notify.YES)
        return True

    @authenticated
    @requires_admin
    @ns.doc(shortcut="disableG8Notification", description="Disable G8 notification")
    @check_g8_accessibility
    def delete(self, g8_name):
        g8_update_notify(g8_name, Notify.NO)
        return True


@ns.route("/<g8_name>/images")
class G8OSImages(Resource):
    """
    G8 virtual machine os image apis
    """

    @authenticated
    @ns.doc(shortcut="getAttachedImages", description="Get attached images to the G8")
    @ns.marshal_list_with(g8_os_image_model)
    @check_g8_accessibility
    def get(self, g8_name):
        return G8Info.get_by_name(g8_name).images

    @authenticated
    @ns.doc(shortcut="attachImageToG8", description="Attach os image to the G8")
    @ns.expect(g8_image_argparser)
    @check_g8_accessibility
    def post(self, g8_name):
        args = g8_image_argparser.parse_args()
        image_id = args.get("image_id")
        username = get_current_user_info().username
        return G8_bizz.attach_image(g8_name, image_id, username)

    @authenticated
    @ns.doc(shortcut="detachImageFromG8", description="Detach os image from the G8")
    @ns.expect(g8_image_argparser)
    @check_g8_accessibility
    def delete(self, g8_name):
        args = g8_image_argparser.parse_args()
        image_id = args.get("image_id")
        return G8_bizz.detach_image(g8_name, image_id)


@ns.route("/<g8_name>/cdroms")
class G8CDROMs(Resource):
    """
    G8 CD_ROM apis
    """

    @authenticated
    @ns.doc(shortcut="getAttachedCDROMs", description="Get attached CD-ROMs to the G8")
    @ns.marshal_list_with(g8_cdrom_model)
    @check_g8_accessibility
    def get(self, g8_name):
        return G8Info.get_by_name(g8_name).cdroms

    @authenticated
    @ns.doc(shortcut="attachCDROMToG8", description="Attach CD-ROM to the G8")
    @ns.expect(g8_cdrom_argparser)
    @check_g8_accessibility
    def post(self, g8_name):
        args = g8_cdrom_argparser.parse_args()
        cdrom_id = args.get("cdrom_id")
        username = get_current_user_info().username
        return G8_bizz.attach_cdrom(g8_name, cdrom_id, username)

    @authenticated
    @ns.doc(shortcut="detachCDROMFromG8", description="Detach CD-ROM from the G8")
    @ns.expect(g8_cdrom_argparser)
    @check_g8_accessibility
    def delete(self, g8_name):
        args = g8_cdrom_argparser.parse_args()
        cdrom_id = args.get("cdrom_id")
        return G8_bizz.detach_cdrom(g8_name, cdrom_id)


@ns.route("/<g8_name>/hardware")
class HardwareSpec(Resource):
    @authenticated
    @ns.doc(
        shortcut="getHardwarespec",
        description="Get the hardware specifications",
    )
    @ns.marshal_with(g8_hardware_specs)
    @check_g8_accessibility
    def get(self, g8_name):
        return G8_hardware.get_by_name(g8_name)


@ns.route("/<g8_name>/hardware/objectstorage-asd")
class ObjectstorageAsd(Resource):
    @authenticated
    @ns.doc(
        shortcut="setObjectstorageAsd",
        description="Set the objectstorage_asd_percentile in the G8's system-config.",
    )
    @ns.expect(objectstorage_asd_parser)
    @check_g8_accessibility
    def post(self, g8_name):
        args = objectstorage_asd_parser.parse_args()
        return G8_hardware.set_objectstorage_asd(g8_name, args["objectstorage_asd"])


@ns.route("/<g8_name>/networks/<network_name>")
class NetworkSettings(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(network_parser)
    @ns.doc(
        shortcut="setNetworkSettings",
        description="Set the networks settings",
    )
    def post(self, g8_name, network_name):
        args = network_parser.parse_args()
        network = args.get("network")
        vlan = args.get("vlan")
        gateway = args.get("gateway")
        g8_network.set_network_settings(g8_name, network_name, network=network, vlan=vlan, gateway=gateway)


@ns.route("/<g8_name>/hardware/switches/management/<switch_name>/serial-number")
class SerialNumberMgmtSwitch(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(post_serialnr_switch_parser)
    @ns.doc(
        shortcut="setSerialnumberManagementSwitch",
        description="Set the serial number of a management switch",
    )
    def post(self, g8_name, switch_name):
        args = post_serialnr_switch_parser.parse_args()
        serialnr = args.get("serial_number")
        G8_hardware.set_serial_number_management_switch(g8_name, switch_name, serialnr)


@ns.route("/<g8_name>/hardware/switches/<switch_name>/model")
class SwitchModelSetter(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(SwitchModelName.model(api))
    @ns.doc(
        shortcut="setSwitchModelName",
        description="Set the model of the switch",
    )
    def post(self, g8_name, switch_name):
        G8_hardware.set_switch_model_name(g8_name, switch_name, **ns.payload)


@ns.route("/<g8_name>/hardware/switches/<switch_name>/manufacturer")
class SwitchManufacturerSetter(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(SwitchManufacturer.model(api))
    @ns.doc(
        shortcut="setSwitchManufacturer",
        description="Set the manufacturer of the switch",
    )
    def post(self, g8_name, switch_name):
        G8_hardware.set_switch_manufacturer(g8_name, switch_name, **ns.payload)


@ns.route("/<g8_name>/hardware/switches/access/<switch_name>/serial-number")
class SerialNumberAccSwitch(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(post_serialnr_switch_parser)
    @ns.doc(
        shortcut="setSerialnumberAccessSwitch",
        description="Set the serial number of an access switch",
    )
    def post(self, g8_name, switch_name):
        args = post_serialnr_switch_parser.parse_args()
        serialnr = args.get("serial_number")
        G8_hardware.set_serial_number_access_switch(g8_name, switch_name, serialnr)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/access/<switch_name>/username")
class AccessSwitchUserName(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_user_name_access_switch)
    @ns.doc(shortcut="setAccessSwitchUsername", description="Set the user name of an access switch")
    def post(self, g8_name, switch_name):
        args = post_user_name_access_switch.parse_args()
        user_name = args["user_name"]
        G8_hardware.set_access_switch_username(g8_name, switch_name, user_name)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/access/<switch_name>/password")
class AccessSwitchPassword(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_password_access_switch)
    @ns.doc(shortcut="setAccessSwitchPassword", description="Set the password of an access switch")
    def post(self, g8_name, switch_name):
        args = post_password_access_switch.parse_args()
        password = args["password"]
        G8_hardware.set_access_switch_password(g8_name, switch_name, password)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/management/<switch_name>/username")
class ManagementSwitchUserName(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_user_name_management_switch)
    @ns.doc(shortcut="setManagementSwitchUserName", description="Set the user name of a management switch")
    def post(self, g8_name, switch_name):
        args = post_user_name_management_switch.parse_args()
        user_name = args["user_name"]
        G8_hardware.set_management_switch_username(g8_name, switch_name, user_name)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/management/<switch_name>/password")
class ManagementSwitchPassword(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_password_management_switch)
    @ns.doc(shortcut="setManagementSwitchPassword", description="Set the password of a management switch")
    def post(self, g8_name, switch_name):
        args = post_password_management_switch.parse_args()
        password = args["password"]
        G8_hardware.set_management_switch_password(g8_name, switch_name, password)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/management/<switch_name>/ip-address")
class ManagementSwitchIP(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_ip_management_switch)
    @ns.doc(shortcut="setManagementSwitchIP", description="Set the IP Address of a management switch")
    def post(self, g8_name, switch_name):
        args = post_ip_management_switch.parse_args()
        ip_address = args["ip_address"]
        G8_hardware.set_management_switch_ip(g8_name, switch_name, ip_address)
        return SUCCESS


@ns.route("/<g8_name>/hardware/switches/access/<switch_name>/ip-address")
class AccessSwitchIP(Resource):
    @authenticated
    @requires_admin
    @ns.expect(post_ip_access_switch)
    @ns.doc(shortcut="setAccessSwitchIP", description="Set the IP Address of an access switch")
    def post(self, g8_name, switch_name):
        args = post_ip_access_switch.parse_args()
        ip_address = args["ip_address"]
        G8_hardware.set_access_switch_ip(g8_name, switch_name, ip_address)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/<string:node_name>/interfaces/<string:interface_name>/mac-address")
class NodeMacAddr(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(postr_node_macaddr_parser)
    @ns.doc(
        shortcut="setMacAddressInterfaceNode",
        description="Set the mac address of a node's interface for switches",
    )
    def post(self, g8_name, node_name, interface_name):
        args = postr_node_macaddr_parser.parse_args()
        mac_addr = args.get("mac_address")
        G8_hardware.set_mac_addr_node(g8_name, node_name, interface_name, mac_addr)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/<string:node_name>/password")
class NodePassword(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(postr_node_password_parser)
    @ns.doc(
        shortcut="setNodePassword",
        description="Set the IPMI password of the node",
    )
    def post(self, g8_name, node_name):
        args = postr_node_password_parser.parse_args()
        password = args.get("password")
        G8_hardware.set_ipmi_password_node(g8_name, node_name, password)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/<string:node_name>/username")
class NodeUSername(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(postr_node_username_parser)
    @ns.doc(
        shortcut="setNodeUsername",
        description="Set the IPMI username of the node",
    )
    def post(self, g8_name, node_name):
        args = postr_node_username_parser.parse_args()
        username = args.get("username")
        G8_hardware.set_ipmi_username_node(g8_name, node_name, username)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/controller/<string:node_name>/public")
class ControllerPublicIPAddr(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(ipaddr_parser)
    @ns.doc(
        shortcut="setPublicIPCtrl",
        description="Set a public IP address on a controller node",
    )
    def post(self, g8_name, node_name):
        args = ipaddr_parser.parse_args()
        public_ip = args.get("ip_address")
        network = args.get("network")
        gateway = args.get("gateway")
        g8_network.set_public_network_ctrl_node(g8_name, node_name, public_ip, network, gateway)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/controller/<string:node_name>/meneja-ip")
class ControllerMenejaIPAddr(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(meneja_ipaddr_parser)
    @requires_admin
    @ns.doc(shortcut="setMenejaIPCtrl", description="Set an IP address of the controller node in Meneja Network")
    def post(self, g8_name, node_name):
        args = meneja_ipaddr_parser.parse_args()
        meneja_ip = args.get("ip_address")
        g8_network.set_meneja_ip_ctrl_node(g8_name, node_name, meneja_ip)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/nodes/<string:node_name>/disks/<string:disk_uuid>/partitions")
class DiskManager(Resource):
    @authenticated
    @ns.doc(shortcut="setDiskPartitions", description="define disk partitions")
    @ns.expect(disk_partitions, validate=True)
    @ns.marshal_with(SuccessModel.model(api))
    @check_g8_accessibility
    def post(self, g8_name, node_name, disk_uuid):
        partition = ns.payload
        G8_hardware.define_disk_partition(g8_name, node_name, disk_uuid, Partition(**partition))
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/nodes/<node_name>/disks/<string:disk_uuid>/partitions/<int:partition_number>")
class DiskPartitions(Resource):
    @authenticated
    @ns.doc(shortcut="deleteDiskPartition", description="delete disk partitions")
    @check_g8_accessibility
    @ns.marshal_with(SuccessModel.model(api))
    def delete(self, g8_name, node_name, disk_uuid, partition_number):
        G8_hardware.delete_disk_partition(g8_name, node_name, disk_uuid, partition_number)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/nodes/<string:node_name>/disks")
class NodeDisks(Resource):
    @authenticated
    @ns.doc(shortcut="addNodeDisk", description="Add node disk")
    @ns.expect(NodeDiskStruct.model(api))
    @check_g8_accessibility
    def post(self, g8_name, node_name):
        node_disk = from_dict_with_enum(data_class=NodeDiskStruct, data=ns.payload)
        G8_hardware.add_node_disk(g8_name, node_name, node_disk=node_disk)
        return SUCCESS


@ns.route("/<g8_name>/hardware/nodes/<string:node_name>/disks/<string:disk_uuid>")
class NodeDisk(Resource):
    @authenticated
    @ns.doc(shortcut="deleteNodeDisk", description="Delete node disk")
    @check_g8_accessibility
    def delete(self, g8_name, node_name, disk_uuid):
        G8_hardware.delete_node_disk(g8_name, node_name, disk_uuid=disk_uuid)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/nodes/<string:node_name>/resource-profile")
class G8NodeResourcePorifle(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8NodeResourceProfile", description="Update G8 nodes resource profile")
    @check_g8_accessibility
    @ns.expect(update_g8_resource_profile_parser)
    def put(self, g8_name, node_name):
        args = update_g8_resource_profile_parser.parse_args()
        resource_profile = args.get("resource-profile")
        G8_hardware.set_node_resource_profile(g8_name, node_name, resource_profile)


@ns.route("/<g8_name>/licensing/vm_agent")
class LicenseVMAgentSettings(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="setVMAgentRequirements", description="Configure the OS types and OS names that require VM Agent")
    @ns.expect(vm_agent_requirements)
    def post(self, g8_name):
        args = vm_agent_requirements.parse_args()
        os_list = args.get("os_list")
        categories = args.get("categories")
        return G8_bizz.set_vm_agent_requirements(g8_name, os_list, categories)


@ns.route("/<g8_name>/support/teleport")
class SupportGithub(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="setSupportAccess", description="Configure teleport github support access")
    @ns.expect(github_support_parser)
    def post(self, g8_name):
        args = github_support_parser.parse_args()
        client_id = args["client_id"]
        client_secret = args["client_secret"]
        org = args["org"]
        team = args["team"]
        return G8_bizz.set_teleport_support(g8_name, client_id, client_secret, org=org, team=team)


@ns.route("/<g8_name>/bom.pdf")
class DownloadBomPdf(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadBomPdf",
        description="Download bill of material PDF file",
    )
    def get(self, g8_name):
        bom = G8_bizz.get_bom_pdf(g8_name)
        return Response(bom, mimetype="application/pdf")


@ns.route("/<g8_name>/bom.html")
class DownloadBomHtml(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadBomHtml",
        description="Download bill of material HTML file",
    )
    def get(self, g8_name):
        bom = G8_bizz.get_bom_html(g8_name)
        return Response(bom, mimetype="text/html")


@ns.route("/<g8_name>/install-spec.pdf")
class DownloadInstallSpecPdf(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadInstallSpecPdf",
        description="Download installation specifications PDF file",
    )
    def get(self, g8_name):
        install_spec = G8_bizz.get_install_spec_pdf(g8_name)
        return Response(install_spec, mimetype="application/pdf")


@ns.route("/<g8_name>/install-spec.html")
class DownloadInstallSpecHtml(Resource):
    @authenticated
    @ns.doc(
        shortcut="downloadInstallSpecHtml",
        description="Download installation specifications HTML file",
    )
    def get(self, g8_name):
        install_spec = G8_bizz.get_install_spec_html(g8_name)
        return Response(install_spec, mimetype="text/html")


@ns.route("/<g8_name>/certificates/<purpose>")
class SetG8CertID(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.expect(domain_parser)
    @ns.doc(
        shortcut="setG8Cert",
        description="Set new certificate domain for the SSL settings of a G8",
    )
    def post(self, g8_name, purpose):
        args = domain_parser.parse_args()
        G8_bizz.set_certificate_domain_by_purpose(g8_name, purpose=purpose, domain=args.get("domain"))


@ns.route("/<g8_name>/capacity")
class G8Capacity(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.marshal_with(generate_api_response_model("cloudapi/billing/getCapacity", api))
    @ns.doc(shortcut="getG8Capacity", description="Get G8 Capacity")
    def get(self, g8_name):
        return G8Client(g8_name, jwt=get_current_user_info().jwt).get_capacity()


@ns.route("/<g8_name>/g8owner")
class G8G8Owner(Resource):
    @authenticated
    @check_g8_accessibility
    @requires_admin
    @ns.expect(set_g8_owner_argparser)
    @ns.doc(shortcut="setG8Owner", description="Set G8 Owner ID for a specific G8")
    def post(self, g8_name):
        set_g8_owner_id(g8_name, ce_id=set_g8_owner_argparser.parse_args()["g8owner_id"])


@ns.route("/<g8_name>/accounts")
class G8Account(Resource):
    @authenticated
    @ns.doc(shortcut="listG8Accounts", description="List all accounts on the G8")
    @check_g8_accessibility
    def get(self, g8_name):
        jwt = get_current_user_info().jwt
        return G8Client(g8_name, jwt=jwt).list_all_accounts()

    @authenticated
    @ns.doc(shortcut="createG8Account", description="Create account on the G8")
    @ns.expect(g8_account_create)
    @check_g8_accessibility
    def post(self, g8_name):
        jwt = get_current_user_info().jwt
        kwargs = g8_account_create.parse_args()
        if kwargs.get("email_address"):
            email = kwargs.pop("email_address")
            try:
                # Try creating an account without passing email; will fail if does not account exists
                return G8Client(g8_name, jwt=jwt).create_account(**kwargs)
            except HTTPError:
                # Then try passing the args with the email, will succeed for now accounts
                kwargs["email_address"] = email
        return G8Client(g8_name, jwt=jwt).create_account(**kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>")
class G8AccountInfo(Resource):
    @authenticated
    @ns.doc(shortcut="getG8AccountInfo", description="Get account info")
    @check_g8_accessibility
    def get(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        return G8Client(g8_name, jwt=jwt).get_account_info(account_id=account_id)

    @authenticated
    @ns.doc(shortcut="deleteG8Account", description="Delete account")
    @ns.expect(g8_account_delete)
    @check_g8_accessibility
    def delete(self, g8_name, account_id):
        kwargs = g8_account_delete.parse_args()
        jwt = get_current_user_info().jwt
        return G8Client(g8_name, jwt=jwt).delete_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/name")
class G8AccountInfoName(Resource):
    @authenticated
    @ns.doc(shortcut="updateG8AccountName", description="Rename Account")
    @ns.expect(g8_account_rename)
    @check_g8_accessibility
    def put(self, g8_name, account_id):
        kwargs = g8_account_rename.parse_args()
        jwt = get_current_user_info().jwt
        return G8Client(g8_name, jwt=jwt).rename_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/quota")
class G8AccountInfoQuota(Resource):
    @authenticated
    @ns.doc(shortcut="updateG8AccountQuota", description="Update account Quotas")
    @ns.expect(g8_account_quota)
    @check_g8_accessibility
    def put(self, g8_name, account_id):
        kwargs = g8_account_quota.parse_args()
        jwt = get_current_user_info().jwt
        return G8Client(g8_name, jwt=jwt).update_account_quota(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/enable")
class G8AccountEnable(Resource):
    @authenticated
    @ns.doc(shortcut="enableG8Account", description="Enable G8 account")
    @ns.expect(g8_enable_parser)
    @check_g8_accessibility
    def post(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        kwargs = g8_enable_parser.parse_args()
        return G8Client(g8_name, jwt=jwt).enable_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/disable")
class G8AccountDisable(Resource):
    @authenticated
    @ns.doc(shortcut="disableG8Account", description="Disable G8 account")
    @ns.expect(g8_disable_parser)
    @check_g8_accessibility
    def post(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        kwargs = g8_disable_parser.parse_args()
        return G8Client(g8_name, jwt=jwt).disable_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/restore")
class G8AccountRestore(Resource):
    @authenticated
    @ns.doc(shortcut="restoreG8Account", description="Disable G8 account")
    @ns.expect(g8_account_restore)
    @check_g8_accessibility
    def post(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        kwargs = g8_account_restore.parse_args()
        return G8Client(g8_name, jwt=jwt).restore_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/users")
class G8Users(Resource):
    @authenticated
    @ns.doc(shortcut="getG8Users", description="Get all users with access to the account")
    @check_g8_accessibility
    def get(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        return G8_bizz.get_g8_account_users(g8_name, account_id, jwt)

    @authenticated
    @ns.doc(shortcut="addG8UserAccess", description="Add user access to the G8 account")
    @ns.expect(g8_add_user_parser)
    @check_g8_accessibility
    def post(self, g8_name, account_id):
        jwt = get_current_user_info().jwt
        kwargs = g8_add_user_parser.parse_args()
        kwargs["access_right"] = AccessRight.get_by_name(kwargs["access_right"]).value
        return G8Client(g8_name, jwt=jwt).add_user_to_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/accounts/<int:account_id>/users/<username>")
class G8UserAccess(Resource):
    @authenticated
    @ns.doc(shortcut="getG8User", description="Get the user by the username")
    @check_g8_accessibility
    def get(self, g8_name, account_id, username):
        jwt = get_current_user_info().jwt
        users = G8_bizz.get_g8_account_users(g8_name, account_id, jwt)
        for user in users:
            if user["username"] == username:
                return user
        raise KeyError(f"Username {username} does not exist or does not have access to account {account_id}")

    @authenticated
    @ns.doc(shortcut="updateG8UserAccess", description="Update User access")
    @check_g8_accessibility
    @ns.expect(g8_update_user_parser)
    def put(self, g8_name, account_id, username):
        jwt = get_current_user_info().jwt
        kwargs = g8_update_user_parser.parse_args()
        kwargs["access_right"] = AccessRight.get_by_name(kwargs["access_right"]).value
        return G8Client(g8_name, jwt=jwt).update_user_access_to_account(
            account_id=account_id, username=username, **kwargs
        )

    @authenticated
    @ns.doc(shortcut="deleteG8UserAccess", description="Delete user access from the G8 account")
    @ns.expect(g8_del_user_parser)
    @check_g8_accessibility
    def delete(self, g8_name, account_id, username):  # pylint: disable=unused-argument
        jwt = get_current_user_info().jwt
        kwargs = g8_del_user_parser.parse_args()
        return G8Client(g8_name, jwt=jwt).delete_user_access_from_account(account_id=account_id, **kwargs)


@ns.route("/<g8_name>/vco-access")
class G8VCOs(Resource):
    @authenticated
    @ns.doc(shortcut="listG8VCOs", description="Add VCO access to the G8")
    @check_g8_accessibility
    def get(self, g8_name):
        g8 = G8Info.get_by_name(g8_name)
        return g8.vcos

    @authenticated
    @ns.doc(shortcut="addG8VCOAccess", description="Add VCO access to the G8")
    @ns.expect(add_vco_access_argparser)
    @check_g8_accessibility
    def post(self, g8_name):
        return G8_bizz.add_vco_access_to_g8(
            g8_name=g8_name, vco_id=add_vco_access_argparser.parse_args()["vco_id"], jwt=get_current_user_info().jwt
        )


@ns.route("/<g8_name>/vco-access/<vco_id>")
class G8VCOAccess(Resource):
    @authenticated
    @ns.doc(shortcut="deleteG8VCOAccess", description="Delete VCO access to the G8")
    @check_g8_accessibility
    def delete(self, g8_name, vco_id):
        return G8_bizz.delete_vco_access_to_g8(g8_name=g8_name, vco_id=vco_id, jwt=get_current_user_info().jwt)


@ns.route("/<g8_name>/consumption")
class G8Consumption(Resource):
    @authenticated
    @ns.doc(shortcut="getG8Consumption", description="Get G8 total and used capacity")
    @ns.marshal_with(g8_consumption_model)
    @check_g8_accessibility
    def get(self, g8_name):
        return G8Info.get_g8_capacity(g8_name=g8_name).to_mongo().to_dict()


ovs_arakoon_location_parser = reqparse.RequestParser(argument_class=FixedArgument)
ovs_arakoon_location_parser.add_argument(
    "ovs_arakoon_location",
    type=str,
    location="args",
    choices=ArakoonLocation.values(),
    required=True,
    help="OVS Arakoon location",
)


@ns.route("/<g8_name>/ovs-arakoon-location")
class OVSArakoonLocation(Resource):
    @authenticated
    @ns.doc(shortcut="setOVSArakoonLocation", description="Set G8 OVS aracoon location")
    @ns.expect(ovs_arakoon_location_parser)
    @requires_admin
    def put(self, g8_name):
        args = ovs_arakoon_location_parser.parse_args()
        ovs_arakoon_location = args["ovs_arakoon_location"]
        return G8_hardware.set_ovs_arakoon_location(g8_name, ArakoonLocation.from_string(ovs_arakoon_location))


@ns.route("/<g8_name>/consumption-series")
class G8ConsumptionSeries(Resource):
    @authenticated
    @ns.doc(shortcut="getG8ConsumptionSeries", description="Get G8 total and used capacity time series")
    @ns.expect(get_g8_series_parser)
    @ns.marshal_with(g8_consumption_archive_model)
    @check_g8_accessibility
    def get(self, g8_name):
        args = get_g8_series_parser.parse_args()
        start = args.get("start") or 0
        end = args.get("end") or 0
        sample_count = args.get("sample_count") or 0
        return get_g8_capacity_series(g8_name=g8_name, start=start, end=end, sample_count=sample_count)


g8_stat_archive_model = api.model(
    "G8StatHistory",
    {
        "name": fields.String(description="Performance measure"),
        "timeseries": fields.List(fields.Integer(description="Timeseries")),
        "average": fields.List(fields.Float(description="Average value series")),
        "practical_max": fields.List(fields.Float(description="Max value for one physical machine at 95 percentile")),
    },
)

all_g8_stats = G8StatsEnum.values() + G8ExternalNetworkStats.values()
get_performance_history_parser = get_g8_series_parser.copy()
get_performance_history_parser.add_argument(
    "include",
    type=str,
    action="append",
    location="args",
    choices=all_g8_stats,
    help=f"G8 performance history stats: {all_g8_stats}",
)


@ns.route("/<g8_name>/performance-history")
class G8CpuUsageHistory(Resource):
    @authenticated
    @ns.expect(get_performance_history_parser)
    @ns.doc(shortcut="getPerformanceHistory", description="Get G8 CPU usage time series")
    @ns.marshal_list_with(g8_stat_archive_model)
    @check_g8_accessibility
    def get(self, g8_name):
        args = get_performance_history_parser.parse_args()
        start = args.get("start") or 0
        end = args.get("end") or 0
        sample_count = args.get("sample_count") or 0
        include = args.get("include", [])
        return get_g8_stat_series(g8_name=g8_name, start=start, end=end, sample_count=sample_count, include=include)


@ns.route("/<g8_name>/backup-config")
class BackupConfig(Resource):
    @authenticated
    @requires_admin
    @ns.expect(set_backup_config_model, validate=True)
    @ns.doc(shortcut="setBackupConfig", description="sets backup config of a g8")
    @check_g8_accessibility
    def put(self, g8_name):
        G8_bizz.set_backup_config(g8_name, **ns.payload)

    @authenticated
    @requires_admin
    @ns.marshal_with(backup_config_model)
    @ns.doc(shortcut="getBackupConfig", description="Gets the backup config of a specific g8")
    @check_g8_accessibility
    def get(self, g8_name):
        return G8Info.get_backup_config(g8_name)


@ns.route("/default-backup-config")
class DefaultBackupConfig(Resource):
    @authenticated
    @requires_admin
    @ns.expect(set_backup_config_model, validate=True)
    @ns.doc(shortcut="setDefaultBackupConfig", description="sets default backup config")
    def put(self):
        G8_bizz.set_default_backup_config(**ns.payload)

    @authenticated
    @requires_admin
    @ns.marshal_with(backup_config_model)
    @ns.doc(shortcut="getDefaultBackupConfig", description="Gets the default backup config of a specific g8")
    def get(self):
        return DefaultBackupConfigModel.get_config()


@ns.route("/<g8_name>/documentation")
class Documentation(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(G8DocumentationStruct.model(api))
    @ns.doc(shortcut="getG8Documentation", description="Get g8 documentation")
    def get(self, g8_name):
        return g8_version.get_g8_doc_file(g8_name)

    @authenticated
    @requires_admin
    @ns.marshal_with(G8DocumentationStruct.model(api))
    @ns.doc(shortcut="createG8Documentation", description="create g8 documentation")
    def post(self, g8_name):
        return g8_version.create_g8_doc_file(g8_name)


@ns.route("/<g8_name>/storage-only")
class G8StorageOnline(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="enableG8StorageOnly", description="Enable G8 storage only")
    @check_g8_accessibility
    def post(self, g8_name):
        G8Info.enable_storage_only(g8_name)
        return SUCCESS

    @authenticated
    @requires_admin
    @ns.doc(shortcut="disableG8StorageOnly", description="Disable G8 storage only")
    @check_g8_accessibility
    def delete(self, g8_name):
        G8Info.disable_storage_only(g8_name)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/nodes")
class G8Nodes(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="getG8Nodes", description="Get G8 nodes")
    @check_g8_accessibility
    @ns.expect(node_argparser)
    @ns.marshal_with(NodeStruct.list_model())
    def get(self, g8_name):
        args = node_argparser.parse_args()
        return dict(result=list_g8_nodes(g8_name, **args))


@ns.route("/<string:g8_name>/hardware/nodes/<string:node_name>")
class G8Node(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="getG8Node", description="Get G8 nodes")
    @check_g8_accessibility
    @ns.marshal_with(NodeStruct.model())
    def get(self, g8_name, node_name):
        return G8HardwareSpec.get_by_name(g8_name).get_node_by_name(node_name)


@ns.route("/<string:g8_name>/externalnetworks/<int:externalnetwork_id>")
class G8ExternalNetwork(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="getExternalNetworkDetails", description="Get G8 external network details")
    @check_g8_accessibility
    @ns.marshal_with(ExternalNetworkStruct.model(api))
    def get(self, g8_name, externalnetwork_id):
        return get_g8_external_network_details(
            g8_name=g8_name, externalnetwork_id=externalnetwork_id, jwt=get_current_user_info().jwt
        )


@ns.route("/<g8_name>/vgpus")
class G8vGPUS(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="listG8vGPUs", description="List all vGPUs on the G8")
    @check_g8_accessibility
    @ns.marshal_with(VGPUStruct.list_model(api))
    def get(self, g8_name):
        jwt = get_current_user_info().jwt
        return {"result": list_active_vgpus(g8_name, jwt=jwt)}


@ns.route("/<string:g8_name>/gpus/<string:gpu_id>")
class G8GPUs(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateG8GPUs", description="update gpu")
    @check_g8_accessibility
    @ns.marshal_with(update_gpu_config_api_model)
    @ns.expect(GPUStruct.model(api))
    def put(self, g8_name, gpu_id):
        return update_gpu_config(g8_name, gpu_id, get_current_user_info().jwt, ns.payload)


@ns.route("/<string:g8_name>/backups/settings")
class G8BackupSettings(Resource):
    @authenticated
    @check_g8_accessibility
    @ns.doc(shortcut="getG8BackupSettings", description="Get G8 backup settings")
    @ns.marshal_with(BackupSettingsStruct.model(api))
    def get(self, g8_name):
        jwt = get_current_user_info().jwt
        g8_client = G8ClientApiV1(g8_name, jwt=jwt)
        return g8_client.locations.get_g8_location_settings()

    @authenticated
    @check_g8_accessibility
    @ns.doc(shortcut="updateG8BackupSettings", description="Update G8 backup settings")
    @ns.expect(BackupSettingsStruct.model(api))
    @ns.marshal_with(success_model)
    def put(self, g8_name):
        jwt = get_current_user_info().jwt
        payload = ns.payload
        return backup_business.update_ce_backup_settings(jwt, g8_name, payload)


@ns.route("/<g8_name>/power-status-history")
class PowerStatusHistory(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="getG8NodesPowerStatusHistory", description="get g8 nodes/switches power status history")
    @check_g8_accessibility
    @ns.expect(get_node_status_history_parser)
    @ns.marshal_with(G8PowerStatusStruct.model(api))
    def get(self, g8_name):
        return get_g8_power_status_history(g8_name=g8_name, **get_node_status_history_parser.parse_args())

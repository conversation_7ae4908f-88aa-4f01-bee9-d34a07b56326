# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring

from flask_itsyouonline import authenticated
from flask_restx import Resource, reqparse

from meneja.api import api
from meneja.api.meneja import <PERSON><PERSON><PERSON><PERSON>
from meneja.business.auth import check_g8_accessibility, requires_admin
from meneja.business.g8.hardware import (
    add_g8_node_to_block,
    add_hdd_backend,
    add_nvme_backend,
    add_vpool,
    create_g8_block_nodes,
    delete_g8_block,
    deploy_hdd_backend,
    deploy_nvme_backend,
    deploy_vpool,
    get_g8_block,
    list_g8_blocks,
    mark_g8_block_deployed,
    regenerate_block_nodes,
    remove_hdd_backend,
    remove_nvme_backend,
    remove_vpool,
    rename_g8_block,
    resize_g8_block,
    set_g8_block_allowed_vm_os_categories,
    set_g8_block_disallowed_vm_os_categories,
    set_g8_block_local_storage,
    set_g8_block_node_configuration,
    set_remote_block_name,
    set_reserved_resources_for_storage_node,
    update_backend_policy,
    update_hdd_backend,
    update_nvme_backend,
    update_vpool,
)
from meneja.lib.enumeration import DiskType, ErasureCodingPolicyTypes
from meneja.lib.fixes import FixedArgument
from meneja.structs.meneja.dataclasses.g8 import OSCategories
from meneja.structs.meneja.dataclasses.g8_node_block import (
    G8BlockFullStruct,
    G8BlocksStruct,
    G8BlockStruct,
    HDDBackend,
    LocalStoragePerNode,
    NodeConfiguration,
    NVMEBackend,
    Policy,
    ReservedStorageNodeResources,
    VPool,
)
from meneja.structs.vco.dataclasses.success import SuccessModel

ns = api.namespace("g8s", description="G8 deployments")


node_force_argparser = reqparse.RequestParser(argument_class=FixedArgument)
node_force_argparser.add_argument(
    "force", type=bool, default=False, help="If true, reset configured partitions on the block nodes"
)
erasure_coding_policy_argparser = reqparse.RequestParser(argument_class=FixedArgument)
erasure_coding_policy_argparser.add_argument(
    "policy_type",
    type=str,
    required=True,
    choices=ErasureCodingPolicyTypes.values(),
    help="Backend policy type (primary or fallback)",
)


@ns.route("/<string:g8_name>/hardware/blocks")
class G8Blocks(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="listG8Blocks", description="List G8 blocks")
    @check_g8_accessibility
    @ns.marshal_with(G8BlocksStruct.model(api))
    def get(self, g8_name):
        return list_g8_blocks(g8_name)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="createG8Block", description="Create G8 block")
    @check_g8_accessibility
    @ns.expect(G8BlockStruct.model(api))
    def post(self, g8_name):
        g8_block = G8BlockStruct.load(ns.payload)
        create_g8_block_nodes(g8_name, g8_block)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>")
class G8Block(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="getG8Block", description="Get G8 block")
    @check_g8_accessibility
    @ns.marshal_with(G8BlockFullStruct.model(api, show_hidden=True))
    def get(self, g8_name, block_name):
        return get_g8_block(g8_name, block_name)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deleteG8Block", description="Delete G8 block")
    @check_g8_accessibility
    def delete(self, g8_name, block_name):
        delete_g8_block(g8_name, block_name)  # will lead to reset of the node models
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/deployed")
class G8BlockDeploy(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="deployG8Block", description="Mark G8 block as deployed to prevent incompatible changes.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        mark_g8_block_deployed(g8_name, block_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/node_count/<int:node_count>")
class G8BlockResize(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="resizeG8Block", description="Resize G8 block.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, node_count):
        resize_g8_block(g8_name, block_name, node_count)


@ns.route(
    "/<string:g8_name>/hardware/blocks/<string:block_name>/preferred_remote_storage_block/<string:remote_block_name>"
)
class G8BlockRemoteStorage(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="setG8BlockRemoteStorage", description="Set remote storage block.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, remote_block_name):
        set_remote_block_name(g8_name, block_name, remote_block_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/resources_reserved_for_storage_node")
class G8BlockStorageResources(Resource):
    @authenticated
    @requires_admin
    @ns.expect(ReservedStorageNodeResources.model(api))
    @ns.doc(shortcut="setG8BlockStorageResources", description="Set resources reserved for the storage node.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        set_reserved_resources_for_storage_node(g8_name, block_name, ReservedStorageNodeResources.load(ns.payload))


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/<string:new_block_name>")
class G8BlockName(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="renameG8Block", description="Update G8 block name.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, new_block_name):
        rename_g8_block(g8_name, block_name, new_block_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/local_storage_per_node")
class G8BlockLocalStorage(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="setG8BlockLocalStoragePerNode", description="Set local storage per node.")
    @ns.expect(LocalStoragePerNode.model(api))
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        set_g8_block_local_storage(g8_name, block_name, LocalStoragePerNode.load(ns.payload).local_storage_per_node)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/allowed_vm_os_categories")
class G8BlockAllowedOsCategories(Resource):
    @authenticated
    @requires_admin
    @ns.expect(OSCategories.model(api))
    @ns.doc(shortcut="setG8BlockAllowedVMOSCategories", description="Set G8 block allowed VM OS categories.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        set_g8_block_allowed_vm_os_categories(g8_name, block_name, OSCategories.load(ns.payload).os_categories)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/disallowed_vm_os_categories")
class G8BlockDisAllowedOsCategories(Resource):
    @authenticated
    @requires_admin
    @ns.expect(OSCategories.model(api))
    @ns.doc(shortcut="setG8BlockDisallowedVMOSCategories", description="Set G8 block disallowed VM OS categories.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        set_g8_block_disallowed_vm_os_categories(g8_name, block_name, OSCategories.load(ns.payload).os_categories)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/node-configuration")
class G8BlockNodeConfiguration(Resource):
    @authenticated
    @requires_admin
    @ns.expect(NodeConfiguration.model(api))
    @ns.doc(shortcut="setG8BlockNodeConfiguration", description="Set G8 block node configuration.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        set_g8_block_node_configuration(g8_name, block_name, NodeConfiguration.load(ns.payload))


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/nodes")
class G8BlockNodeRegenerate(Resource):
    @authenticated
    @requires_admin
    @ns.expect(node_force_argparser)
    @ns.doc(shortcut="regenerateG8BlockNodes", description="Regenerate nodes of the G8 block.")
    @check_g8_accessibility
    def put(self, g8_name, block_name):
        args = node_force_argparser.parse_args()
        regenerate_block_nodes(g8_name, block_name, **args)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="addG8BlockNode", description="Add node to G8 block.")
    @ns.marshal_with(SuccessModel.model(api))
    @check_g8_accessibility
    def post(self, g8_name, block_name):
        add_g8_node_to_block(g8_name, block_name)
        return SUCCESS


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/hdd-backends")
class G8BlockHDDBackends(Resource):
    @authenticated
    @requires_admin
    @ns.expect(HDDBackend.model(api))
    @ns.doc(shortcut="createG8BlockHDDBackend", description="Create G8 block HDD backend.")
    @check_g8_accessibility
    def post(self, g8_name, block_name):
        add_hdd_backend(g8_name, block_name, HDDBackend.load(ns.payload))


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/hdd-backends/<string:backend_name>")
class G8BlockHDDBackend(Resource):
    @authenticated
    @requires_admin
    @ns.expect(NodeConfiguration.model(api))
    @ns.doc(shortcut="updateG8BlockHDDBackend", description="Update G8 block HDD backend.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        update_hdd_backend(g8_name, block_name, backend_name, HDDBackend.load(ns.payload))

    @authenticated
    @requires_admin
    @ns.doc(shortcut="removeG8BlockHDDBackend", description="Remove G8 block HDD backend.")
    @check_g8_accessibility
    def delete(self, g8_name, block_name, backend_name):
        remove_hdd_backend(g8_name, block_name, backend_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/hdd-backends/<string:backend_name>/deployed")
class DeployG8BlockHDDBackend(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="deployG8BlockHDDBackend", description="Mark HDD backend as deployes.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        deploy_hdd_backend(g8_name, block_name, backend_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/nvme-backends")
class G8BlockNVMEBackends(Resource):
    @authenticated
    @requires_admin
    @ns.expect(NVMEBackend.model(api))
    @ns.doc(shortcut="createG8BlockNVMEBackend", description="Create G8 block NVME backend.")
    @check_g8_accessibility
    def post(self, g8_name, block_name):
        add_nvme_backend(g8_name, block_name, NVMEBackend.load(ns.payload))


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/nvme-backends/<string:backend_name>")
class G8BlockNVMEBackend(Resource):
    @authenticated
    @requires_admin
    @ns.expect(NVMEBackend.model(api))
    @ns.doc(shortcut="updateG8BlockNVMEBackend", description="Update G8 block NVME backend.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        update_nvme_backend(g8_name, block_name, backend_name, NVMEBackend.load(ns.payload))

    @authenticated
    @requires_admin
    @ns.doc(shortcut="removeG8BlockNVMEBackend", description="Remove G8 block NVME backend.")
    @check_g8_accessibility
    def delete(self, g8_name, block_name, backend_name):
        remove_nvme_backend(g8_name, block_name, backend_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/nvme-backends/<string:backend_name>/deployed")
class G8BlockNVMEBackendDeploy(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="deployG8BlockNVMEBackend", description="Mark NVME backend as deployed")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        deploy_nvme_backend(g8_name, block_name, backend_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/vpools")
class G8BlockVPools(Resource):
    @authenticated
    @requires_admin
    @ns.expect(VPool.model(api))
    @ns.doc(shortcut="createG8BlockVPool", description="Create G8 block VPool.")
    @check_g8_accessibility
    def post(self, g8_name, block_name):
        add_vpool(g8_name, block_name, VPool.load(ns.payload))


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/vpools/<string:vpool_name>")
class G8BlockVPool(Resource):
    @authenticated
    @requires_admin
    @ns.expect(VPool.model(api))
    @ns.doc(shortcut="updateG8BlockVPool", description="Update G8 block VPool.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, vpool_name):
        update_vpool(g8_name, block_name, vpool_name, VPool.load(ns.payload))

    @authenticated
    @requires_admin
    @ns.doc(shortcut="removeG8BlockVPool", description="Remove G8 block VPool.")
    @check_g8_accessibility
    def delete(self, g8_name, block_name, vpool_name):
        remove_vpool(g8_name, block_name, vpool_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/vpools/<string:vpool_name>/deployed")
class G8BlockVPoolDeploy(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="deployG8BlockVPool", description="Set G8 block node configuration.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, vpool_name):
        deploy_vpool(g8_name, block_name, vpool_name)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/hdd-backends/<string:backend_name>/policy")
class G8BlockHDDBackendPolicy(Resource):
    @authenticated
    @requires_admin
    @ns.expect(erasure_coding_policy_argparser, Policy.model(api))
    @ns.doc(shortcut="updateG8BlockHDDBackendPolicy", description="Update G8 block HDD backend policy.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        policy_type = erasure_coding_policy_argparser.parse_args()["policy_type"]
        policy = Policy.load(ns.payload)
        update_backend_policy(g8_name, block_name, backend_name, policy, policy_type, DiskType.HDD)


@ns.route("/<string:g8_name>/hardware/blocks/<string:block_name>/nvme-backends/<string:backend_name>/policy")
class G8BlockNVMEBackendPolicy(Resource):
    @authenticated
    @requires_admin
    @ns.expect(erasure_coding_policy_argparser, Policy.model(api))
    @ns.doc(shortcut="updateG8BlockNVMEBackendPolicy", description="Update G8 block NVME backend policy.")
    @check_g8_accessibility
    def put(self, g8_name, block_name, backend_name):
        policy_type = erasure_coding_policy_argparser.parse_args()["policy_type"]
        policy = Policy.load(ns.payload)
        update_backend_policy(g8_name, block_name, backend_name, policy, policy_type, backend_type=DiskType.NVME)

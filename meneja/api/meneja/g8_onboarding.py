# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring
import logging
import time

from flask import g as flask_g
from flask import session
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean

from meneja.api import api
from meneja.api.meneja.g8 import ns
from meneja.business.auth import g8_request_authorized, get_accessible_g8s, is_admin, is_member_of, requires_admin
from meneja.business.db_model_2_rest import generate_request_parser, generate_restfull_model_spec
from meneja.business.g8 import onboarding
from meneja.lib.fixes import FixedArgument
from meneja.lib.itsyouonline import AccessDeniedWithoutScope
from meneja.model.g8_onboarding import StageStatus
from meneja.model.g8_onboarding import G8Request as OnboardingG8Request

logger = logging.getLogger()
REQUEST_ROUTE = "/onboarding/requests"

# models
g8_request_model = api.model("G8Request", generate_restfull_model_spec(OnboardingG8Request, api=api, add_id=True))
g8_requests_model = api.model(
    "G8Requests", generate_restfull_model_spec(OnboardingG8Request, api=api, exported_only=True, add_id=True)
)
g8_requests_model["current_status"] = fields.Nested(
    api.model("G8LatestStatus", generate_restfull_model_spec(StageStatus, api=api))
)

# parsers
list_g8_request_parser = reqparse.RequestParser(FixedArgument)
list_g8_request_parser.add_argument(
    "include_archived", type=boolean, location="args", default=False, help="include deleted request"
)

add_g8_request_parser = generate_request_parser(OnboardingG8Request)
add_g8_request_parser.add_argument(
    "ce_id", type=str, required=True, location="args", default=None, help="Cloud Enabler id"
)
add_g8_request_parser.add_argument("country", type=str, location="args", default=None, help="Country name")
add_g8_request_parser.add_argument("city", type=str, location="args", default=None, help="City name")
add_g8_request_parser.add_argument("datacenter_id", type=str, location="args", default=None, help="Datacenter id")
add_g8_request_parser.add_argument("datacenter_name", type=str, location="args", default=None, help="Datacenter name")
add_g8_request_parser.add_argument(
    "datacenter_address", type=str, location="args", default=None, help="Datacenter address"
)
add_g8_request_parser.add_argument(
    "datacenter_latitude", type=float, location="args", default=None, help="latitude coordinate"
)
add_g8_request_parser.add_argument(
    "datacenter_longitude", type=float, location="args", default=None, help="longitude coordinate"
)

edit_g8_request_parser = add_g8_request_parser.copy()
edit_g8_request_parser.add_argument("country", type=str, location="args", default=None, help="Country name")
edit_g8_request_parser.add_argument("city", type=str, location="args", default=None, help="City name")
edit_g8_request_parser.add_argument("datacenter_id", type=str, location="args", default=None, help="Datacenter id")
edit_g8_request_parser.add_argument("datacenter_name", type=str, location="args", default=None, help="Datacenter name")
edit_g8_request_parser.add_argument(
    "datacenter_address", type=str, location="args", default=None, help="Datacenter address"
)
edit_g8_request_parser.add_argument(
    "datacenter_latitude", type=float, location="args", default=None, help="latitude coordinate"
)
edit_g8_request_parser.add_argument(
    "datacenter_longitude", type=float, location="args", default=None, help="longitude coordinate"
)

request_changes = reqparse.RequestParser(FixedArgument)
request_changes.add_argument("requested_changes", required=True, type=str, location="args", help="Change requests")

mark_as_ordered = reqparse.RequestParser(FixedArgument)
mark_as_ordered.add_argument("eta", type=float, location="args", help="Estimated time of arrival")


@ns.route(REQUEST_ROUTE)
class G8Onboarding(Resource):
    @authenticated
    @ns.marshal_list_with(g8_requests_model)
    @ns.doc(
        shortcut="listG8Request",
        description="List G8 requests visible to the user",
    )
    @ns.expect(list_g8_request_parser)
    def get(self):
        args = list_g8_request_parser.parse_args()
        include_archived = args.get("include_archived")

        filters = dict(include_archived=include_archived)
        if not is_admin():
            filters["requestor_username"] = get_current_user_info().username

        return onboarding.list_requests(**filters)

    @authenticated
    @ns.expect(add_g8_request_parser)
    @ns.doc(
        shortcut="createG8Request",
        description="Make a request for a new G8",
    )
    def post(self):
        args = add_g8_request_parser.parse_args()
        owner_org = args.get("owner_org")

        if not is_member_of(owner_org):
            message = f"Not authorized to create G8 request for itsyou.online organization {owner_org}"
            raise AccessDeniedWithoutScope(message, f"user:memberof:{owner_org},user:phone")
        kwargs = _fetch_user_info()
        request = onboarding.create_request(
            base_domain=args.get("base_domain"),
            city=args.get("city"),
            country=args.get("country"),
            license_type=args.get("license_type"),
            ce_id=args.get("ce_id"),
            datacenter_id=args.get("datacenter_id"),
            datacenter_name=args.get("datacenter_name"),
            datacenter_address=args.get("datacenter_address"),
            datacenter_latitude=args.get("datacenter_latitude"),
            datacenter_longitude=args.get("datacenter_longitude"),
            owner_org=owner_org,
            **kwargs,
        )
        return str(request.id), 201  # pylint: disable=no-member


@ns.route(REQUEST_ROUTE + "/<request_id>")
class G8Request(Resource):
    @authenticated
    @g8_request_authorized
    @ns.marshal_with(g8_request_model)
    @ns.doc(
        shortcut="getG8Request",
        description="Get a G8 request",
    )
    def get(self, request_id):
        return onboarding.get_request(request_id)

    @authenticated
    @g8_request_authorized
    @ns.doc(
        shortcut="updateG8Request",
        description="Update G8 request",
    )
    @ns.expect(edit_g8_request_parser)
    def put(self, request_id):
        request = onboarding.get_request(request_id)
        args = edit_g8_request_parser.parse_args()
        latitude = longitude = None
        if request.datacenter.coordinates:
            latitude = request.datacenter.coordinates[0]
            longitude = request.datacenter.coordinates[1]

        onboarding.update_request(
            request_id=request_id,
            base_domain=args.get("base_domain", request.base_domain),
            license_type=args.get("license_type", request.license_type),
            owner_org=args.get("owner_org", request.owner_org),
            city=args.get("city", request.datacenter.city),
            country=args.get("country", request.datacenter.country),
            datacenter_id=args.get("datacenter_id", request.datacenter.id),
            datacenter_name=args.get("datacenter_name", request.datacenter.name),
            datacenter_address=args.get("datacenter_address", request.datacenter.address),
            datacenter_latitude=args.get("datacenter_latitude", latitude),
            datacenter_longitude=args.get("datacenter_longitude", longitude),
        )

    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="archiveG8Request",
        description="Archive G8 request",
    )
    def delete(self, request_id):
        return onboarding.archive_request(request_id)


@ns.route(REQUEST_ROUTE + "/<request_id>/approve")
class ApproveG8Request(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="approveG8Request",
        description="Approve G8 request",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        onboarding.approve_request(request_id, **kwargs)
        # Make sure the user's accessible g8s list is updated
        accessible_g8s_info = {"g8s": get_accessible_g8s(), "updated_at": int(time.time())}
        flask_g.accessible_g8s = accessible_g8s_info
        if session:
            session["accessible_g8s"] = accessible_g8s_info


@ns.route(REQUEST_ROUTE + "/<request_id>/request_changes")
class RequestChangesForG8Request(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="requestChangesG8Request",
        description="Request changes to the G8 request",
    )
    @ns.expect(request_changes)
    def post(self, request_id):
        args = request_changes.parse_args()
        notes = {"requested_changes": args.get("requested_changes")}
        kwargs = _fetch_user_info()
        return onboarding.request_changes(request_id, notes, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/cabled")
class MarkG8RequestAsCabled(Resource):
    @authenticated
    @g8_request_authorized
    @ns.doc(
        shortcut="markG8RequestAsCabled",
        description="Mark G8 request as cabled",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_cabled(request_id, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/ordered")
class MarkG8RequestAsOrdered(Resource):
    @authenticated
    @g8_request_authorized
    @ns.doc(
        shortcut="markG8RequestAsOrdered",
        description="Mark G8 request as ordered",
    )
    @ns.expect(mark_as_ordered)
    def post(self, request_id):
        args = mark_as_ordered.parse_args()
        notes = {"eta": args.get("eta")}
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_ordered(request_id, notes, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/delivered")
class MarkG8RequestAsDelivered(Resource):
    @authenticated
    @g8_request_authorized
    @ns.doc(
        shortcut="markG8RequestAsDelivered",
        description="Mark G8 request status as delivered",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_delivered(request_id, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/configured")
class MarkG8RequestAsConfigured(Resource):
    @authenticated
    @g8_request_authorized
    @ns.doc(
        shortcut="markG8RequestAsConfigured",
        description="Mark G8 request as configured",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_configured(request_id, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/network_configured")
class MarkG8RequestAsNetworkConfigured(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="markG8RequestAsNetworkConfigured",
        description="Mark G8 request as network configured",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_network_configured(request_id, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/fully_configured")
class MarkG8RequestAsFullyConfigured(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="markG8RequestAsFullyConfigured",
        description="Mark G8 request as fully configured",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.complete_configuration(request_id, **kwargs)


@ns.route(REQUEST_ROUTE + "/<request_id>/installed")
class MarkG8RequestAsInstalled(Resource):
    @authenticated
    @requires_admin
    @ns.doc(
        shortcut="markG8RequestAsInstalled",
        description="Mark G8 request as installed",
    )
    def post(self, request_id):
        kwargs = _fetch_user_info()
        return onboarding.mark_request_as_installed(request_id, **kwargs)


def _fetch_user_info() -> dict:
    user_info = get_current_user_info()
    return {
        "username": user_info.username,
        "first_name": user_info.firstname,
        "last_name": user_info.lastname,
        "phone_number": (
            user_info.info["phonenumbers"][0]["phonenumber"] if user_info.info.get("phonenumbers") else None
        ),
        "email": user_info.info["emailaddresses"][0]["emailaddress"] if user_info.info.get("emailaddresses") else None,
    }

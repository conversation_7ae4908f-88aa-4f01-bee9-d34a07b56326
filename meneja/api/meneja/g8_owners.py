# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DIS<PERSON>OSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115, W0613

from os import environ

from dacite import from_dict
from flask import send_file
from flask.helpers import make_response
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from werkzeug import exceptions
from werkzeug.datastructures import FileStorage

import meneja.business.g8_owners as g8o_business
from meneja.api import api
from meneja.api.vco import SUCCESS, success_model
from meneja.business import backups as backup_business
from meneja.business import billing_v2 as billing_business
from meneja.business import invoice_config_v2 as inv_cfg
from meneja.business import license_compliance_reports
from meneja.business.auth import (
    get_g8owner_id,
    is_admin,
    is_billing_member,
    is_ce_operations_member,
    is_operations_member,
    is_specific_g8_owner,
    is_vco_g8_owner,
    requires_admin,
    requires_custom,
    requires_specific_g8_owner,
)
from meneja.business.ce_partners import get_ce_partner_info, list_ce_partners, update_ce_partner_info
from meneja.business.db_model_2_rest import generate_request_parser, generate_restfull_model_spec
from meneja.business.g8_knowledge_base import (
    delete_knowledge_base_topic,
    get_g8_knowledge_base_tree,
    get_knowledge_base_image,
    get_knowledge_base_topic,
)
from meneja.business.payment_terms import get_payment_terms, set_payment_terms
from meneja.business.vco import create_vco, delete_vco, get_vco, update_vco
from meneja.business.vco_billing import (
    check_invoice_belongs_to_g8owner,
    get_ce_outgoing_invoice,
    list_ce_outgoing_invoices,
)
from meneja.lib.enumeration import BillingRole
from meneja.lib.fixes import FixedArgument
from meneja.model.backups import Policies, Targets
from meneja.model.billing_organization import BillingOrganization
from meneja.model.g8 import G8Info
from meneja.model.g8_owners import G8Owner as G8OwnerModel
from meneja.model.invoice_v2 import Invoice as InvoiceModel
from meneja.model.vco import VCO as VCOmodel
from meneja.structs.dataclasses.invoice import (
    CEOutgoingnvoiceStruct,
    CEOutgoingnvoiceStructFull,
    InvoiceExtraChargesStruct,
)
from meneja.structs.dataclasses.payment_terms import PaymentTermsStruct
from meneja.structs.dataclasses.pricing import GetVCORecommendedPricingStruct, SetVCORecommendedPricingStruct
from meneja.structs.dataclasses.software_licenses import VcoSoftwareLicenseOverviewStruct
from meneja.structs.meneja.dataclasses.ce_partner import CEPartnerSimpleStruct, CEPartnerStruct, CEPartnerUpdateStruct
from meneja.structs.meneja.dataclasses.cloud_enabler import (
    CloudEnablerDocumentationStruct,
    CloudEnablerLicenseKeyStruct,
    VCOComplianceStruct,
)
from meneja.structs.meneja.dataclasses.currency_exchange import CurrencyExchangeListStruct, CurrencyExchangeStruct
from meneja.structs.meneja.dataclasses.g8 import G8DocumentationStruct
from meneja.structs.meneja.dataclasses.g8_owners import CloudEnablerGetStruct
from meneja.structs.meneja.dataclasses.knowledge_base import KnowledgeBaseCategoryTreeStruct
from meneja.structs.meneja.dataclasses.vco import VCOGetStruct
from meneja.structs.vco.dataclasses.vco import (
    BackupCreateTargetStruct,
    BackupPoliciesStruct,
    BackupPolicyListStruct,
    BackupPolicyStruct,
    BackupTargetsStruct,
    BackupTargetStruct,
    IdModelStruct,
)

g8_owners_model = api.model("G8Owner", generate_restfull_model_spec(G8OwnerModel, api=api))
g8_owners_contact_info_model = api.model(
    "companyContactInfo",
    {
        "email": fields.String(description="G8 Owner contact email", required=True),
        "name": fields.String(description="G8 Owner contact name", required=True),
        "phone_number": fields.String(description="G8 Owner contact phone number", required=True),
    },
)
backup_target_cs_model = api.model("cloudspaceId", {"cloudspace_id": fields.String(description="cloudspace Id")})

vco_edit_parser = generate_request_parser(VCOmodel, recursive=True, editable_only=True)
vco_edit_parser.replace_argument("billable", type=boolean, required=False, location="args", help="VCO is billable")
vco_edit_parser.remove_argument("customers_show_prices")
vco_edit_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
vco_edit_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
vco_edit_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
vco_edit_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
vco_edit_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
vco_edit_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
vco_edit_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)

g8_simple_owners_model = api.model(
    "G8OwnerSimple",
    {
        "g8owner_id": fields.String(description="G8 Owner Id in meneja", required=True),
        "name": fields.String(description="G8 Owner company name", required=True),
        "organization": fields.String(description="G8 Owner organization"),
        "contact": fields.Nested(g8_owners_contact_info_model, description="Company contact information"),
    },
)
g8_owners_parser = generate_request_parser(G8OwnerModel, recursive=True)
g8_owners_parser.add_argument(
    "bill_tu", type=boolean, required=False, location="args", help="CloudEnabler gets billed for TU resources"
)
g8_owners_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
g8_owners_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
g8_owners_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
g8_owners_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
g8_owners_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
g8_owners_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
g8_owners_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)
g8_owners_update_parser = generate_request_parser(G8OwnerModel, recursive=True, exported_only=True)
g8_owners_update_parser.replace_argument(
    "billable", type=boolean, required=False, location="args", help="CloudEnabler is billable"
)
g8_owners_update_parser.add_argument(
    "bill_tu", type=boolean, required=False, location="args", help="CloudEnabler gets billed for TU resources"
)
g8_owners_update_parser.remove_argument(name="regional_partner_id")
g8_owners_update_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
g8_owners_update_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
g8_owners_update_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
g8_owners_update_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
g8_owners_update_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
g8_owners_update_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
g8_owners_update_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)

user_type_parser = reqparse.RequestParser(argument_class=FixedArgument)
user_type_parser.add_argument(
    "is_owner", type=boolean, required=False, default=False, location="args", help="IAM org user is Owner"
)

update_license_key_parser = reqparse.RequestParser(argument_class=FixedArgument)
update_license_key_parser.add_argument("license_key", type=str, required=True, location="args", help="License key")

g8_list_model = api.model("G8OwnerG8s", {"g8s": fields.List(fields.String(description="G8 Name"))})

vco_compliance_list_model = api.model(
    "VCOComplianceList", {"result": fields.List(fields.Nested(VCOComplianceStruct.model(api)))}
)

ns = api.namespace("g8owners", description="G8 Owners")
vco_simple_model = api.model(
    "VCOSimple",
    {
        "vco_id": fields.String(description="VCO identifier"),
        "company_name": fields.String(description="VCO Company Name", attribute="company_information.name"),
        "domain": fields.String(description="VCO portal domain"),
        "contact_name": fields.String(description="Contact name", attribute="company_information.contact.name"),
        "g8owner_id": fields.String(description="ID of G8 Owner of this VCO"),
        "status": fields.String(description="VCO status"),
    },
)
vco_create_parser = generate_request_parser(VCOmodel, recursive=True, creatable_only=True)
vco_create_parser.add_argument(
    "onboarding_session_key", type=str, required=False, default="", location="args", help="The onboarding session key"
)
vco_create_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
vco_create_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
vco_create_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
vco_create_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
vco_create_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
vco_create_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
vco_create_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)
vcoCreateResult = ns.model(
    "VCOCreateResult",
    {
        "workflow": fields.String,
    },
)

knowledge_base_parser = reqparse.RequestParser(argument_class=FixedArgument)
knowledge_base_parser.add_argument("path", type=str, default=None, help="topic path")

subscribe_g8_parser = reqparse.RequestParser(argument_class=FixedArgument)
subscribe_g8_parser.add_argument(
    "externalnetwork_id", location="args", type=int, default=None, required=True, help="external network id"
)
subscribe_g8_parser.add_argument(
    "g8_name",
    location="args",
    default=None,
    required=True,
    help="g8 name",
)

unsubscribe_g8_parser = reqparse.RequestParser(argument_class=FixedArgument)

unsubscribe_g8_parser.add_argument(
    "g8_name",
    type=str,
    location="args",
    default=None,
    required=True,
    help="g8 name",
)


@ns.route("/")
class G8Owners(Resource):
    """Cloud enabler management"""

    @authenticated
    @ns.doc(shortcut="listG8Owners", description="List all G8 Owners")
    @ns.marshal_list_with(g8_simple_owners_model)
    @requires_custom(is_admin, is_billing_member)
    def get(self):
        """Get list of cloud enablers"""
        return [
            dict(
                g8owner_id=g.g8owner_id,
                name=g.company_information.name,
                organization=g.organization,
                contact=dict(
                    name=g.company_information.contact.name,
                    email=g.company_information.contact.email,
                    phone_number=g.company_information.contact.phone_number,
                ),
            )
            for g in G8OwnerModel.list_simple()
        ]

    @authenticated
    @ns.expect(g8_owners_parser)
    @requires_admin
    @ns.doc(shortcut="createG8Owner", description="Create a G8 Owners")
    def post(self):
        """Create cloud enabler"""
        return g8o_business.create(
            meneja_org=environ["MNJ_IAM_ORGANIZATION"], jwt=environ["MNJ_TOKEN"], **g8_owners_parser.parse_args()
        )


@ns.route("/<g8owner_id>")
class G8Owner(Resource):
    """Specific cloud enabler management"""

    @authenticated
    @ns.doc(shortcut="getG8Owner", description="Get G8 Owner")
    @ns.marshal_with(CloudEnablerGetStruct.model(api))
    @requires_custom(is_admin, is_specific_g8_owner, is_billing_member)
    def get(self, g8owner_id):
        """Get cloud enabler

        Args:
            g8owner_id (str): Cloud enabler ID

        Returns:
            G8Owner: Cloud enabler object
        """
        return g8o_business.get_g8_owner(g8owner_id)

    @authenticated
    @ns.expect(g8_owners_update_parser)
    @ns.doc(shortcut="updateG8Owner", description="Update G8 Owner")
    @requires_custom(is_admin, is_specific_g8_owner, is_billing_member)
    def put(self, g8owner_id):
        g8o_business.update(g8owner_id, **g8_owners_update_parser.parse_args())

    @authenticated
    @ns.doc(shortcut="deleteG8Owner", description="Delete G8 Owners")
    @requires_admin
    def delete(self, g8owner_id):
        g8o_business.delete_g8_owner(g8owner_id)


@ns.route("/<g8owner_id>/recommended-vco-selling-prices")
class RecommendedVCOPricesForCloudenablers(Resource):
    @authenticated
    @requires_custom(is_specific_g8_owner)
    @ns.doc(shortcut="SetRecommendedVCOSellingPrice", description="Set recommended vco selling prices")
    @ns.expect(SetVCORecommendedPricingStruct.model(api))
    @ns.marshal_with(success_model)
    def post(self, g8owner_id):
        return g8o_business.set_vco_recommended_selling_prices(
            g8owner_id, SetVCORecommendedPricingStruct.load(ns.payload)
        )

    @authenticated
    @requires_custom(is_specific_g8_owner)
    @ns.doc(shortcut="getRecommendedVCOSellingPrice", description="Get recommended vco selling prices")
    @ns.marshal_with(GetVCORecommendedPricingStruct.model(api))
    def get(self, g8owner_id):
        return g8o_business.get_vco_recommended_selling_prices(g8owner_id)


@ns.route("/<g8owner_id>/vcos")
class G8OwnerVCOs(Resource):
    @authenticated
    @requires_custom(is_specific_g8_owner, is_billing_member)
    @ns.doc(shortcut="listG8OwnerVCOS", description="List VCO objects for g8 owner")
    @ns.marshal_list_with(vco_simple_model)
    def get(self, g8owner_id):
        only = ["company_information__name", "domain", "company_information__contact__name", "g8owner_id", "status"]
        return VCOmodel.list(g8owner_id=g8owner_id, only=only)

    @authenticated
    @requires_custom(is_admin, is_specific_g8_owner)
    @ns.doc(shortcut="createG8OwnerVCO", description="Create VCO object")
    @ns.expect(vco_create_parser)
    @ns.marshal_with(vcoCreateResult)
    def post(self, g8owner_id):
        kwargs = vco_create_parser.parse_args()
        g8owner_id = kwargs.get("g8owner_id")
        if g8owner_id and not is_specific_g8_owner(g8owner_id):
            raise exceptions.Forbidden("User doesn't have access to the G8 Owner")
        if not g8owner_id:
            g8owner_id = get_g8owner_id()
        return dict(workflow=create_vco(**kwargs))


@ns.route("/<g8owner_id>/vcos/<vco_id>")
class G8OwnerVCO(Resource):
    @authenticated
    @requires_custom(is_vco_g8_owner, is_billing_member)
    @ns.doc(shortcut="getG8ownerVCO", description="Get VCO object")
    @ns.marshal_with(VCOGetStruct.model(api))
    def get(self, g8owner_id, vco_id):  # pylint: disable=unused-argument
        return get_vco(vco_id)

    @authenticated
    @requires_custom(is_vco_g8_owner, is_billing_member)
    @ns.doc(shortcut="updateG8ownerVCO", description="Update VCO object")
    @ns.expect(vco_edit_parser)
    def put(self, g8owner_id, vco_id):
        update_vco(vco_id, **vco_edit_parser.parse_args())

    @authenticated
    @requires_admin
    @requires_custom(is_vco_g8_owner, is_billing_member)
    @ns.doc(shortcut="deleteG8ownerVCO", description="Delete VCO")
    def delete(self, g8owner_id, vco_id):  # pylint: disable=unused-argument
        delete_vco(vco_id)


@ns.route("/<g8owner_id>/compliance")
class G8OwnerCompliance(Resource):
    @authenticated
    @ns.doc(shortcut="getG8OwnerCompliance", description="Get information about license compliance")
    @ns.marshal_with(vco_compliance_list_model)
    @requires_custom(is_admin, is_specific_g8_owner)
    def get(self, g8owner_id):
        return dict(result=license_compliance_reports.list_vco_compliancy(g8owner_id))


@ns.route("/<g8owner_id>/g8s")
class G8OwnerLocations(Resource):
    @authenticated
    @ns.doc(shortcut="listG8OwnerG8s", description="List G8 Owner G8s")
    @requires_custom(is_admin, is_specific_g8_owner)
    def get(self, g8owner_id):
        return {"g8s": list(G8Info.get_g8_owner_g8_names(g8owner_id))}


ce_rp_argparser = reqparse.RequestParser(argument_class=FixedArgument)
ce_rp_argparser.add_argument(
    "regional_partner_id", type=str, required=False, default=None, location="args", help="Regional Partner ID"
)


@ns.route("/<g8owner_id>/regional-partners")
class CERegionalPartner(Resource):
    @authenticated
    @requires_admin
    @ns.expect(ce_rp_argparser)
    @ns.doc(shortcut="setRegionalPartner", description="adding CE to an RP. If None is passed, CE belongs to GIG")
    def put(self, g8owner_id):
        g8o_business.set_regional_partner(g8owner_id=g8owner_id, **ce_rp_argparser.parse_args())


list_invoices_argparser = reqparse.RequestParser(argument_class=FixedArgument)
list_invoices_argparser.add_argument(
    "vco_id", type=str, required=False, default=None, location="args", help="VCO ID to filter Invoices on"
)
list_invoices_argparser.add_argument(
    "month",
    type=int,
    required=False,
    default=None,
    location="args",
    help="Month to filter Invoices on in timestamp format UTC epoch of YYYY-MM-01 00:00:00",
)
list_invoices_argparser.add_argument(
    "partner_ce_id", type=str, required=False, default=None, location="args", help="Partner CE ID to filter Invoices on"
)
generate_invoices_argparser = reqparse.RequestParser(argument_class=FixedArgument)
generate_invoices_argparser.add_argument(
    "month",
    type=int,
    required=True,
    location="args",
    help="Month to generate Invoices for in timestamp format UTC epoch of YYYY-MM-01 00:00:00",
)
generate_invoices_argparser.add_argument(
    "vco_id",
    type=str,
    required=False,
    location="args",
    help="Optional VCO ID. If passed, Invoices will only be generated for this VCO",
)
generate_invoices_argparser.add_argument(
    "partner_ce_id",
    type=str,
    required=False,
    location="args",
    help="Optional Partner CE ID. If passed, Invoices will only be generated for this partner CE",
)
generate_invoice_argparser = reqparse.RequestParser(argument_class=FixedArgument)
generate_invoice_argparser.add_argument(
    "exchange_rate",
    type=float,
    required=False,
    location="args",
    help="Exchange rate between cloud enabler and vco currency (Required if currencies are different)",
)


@ns.route("/<g8owner_id>/invoices")
class Invoices(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.expect(list_invoices_argparser)
    @ns.marshal_with(CEOutgoingnvoiceStruct.list_model(api))
    @ns.doc(shortcut="listG8OwnerInvoices", description="List G8Owner to VCOs Invoices")
    def get(self, g8owner_id):
        args = list_invoices_argparser.parse_args()
        if args["vco_id"]:
            buyer_id = BillingOrganization.get_id(BillingRole.VCO, args["vco_id"])
        elif args["partner_ce_id"]:
            buyer_id = BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, args["partner_ce_id"])
        else:
            buyer_id = None
        return {
            "result": list_ce_outgoing_invoices(
                cloudenabler_org_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id),
                month=args["month"],
                buyer_id=buyer_id,
            )
        }

    @authenticated
    @requires_specific_g8_owner
    @ns.doc(shortcut="generateG8OwnerInvoices", description="(Re)generate Invoices")
    @ns.expect(generate_invoices_argparser, CurrencyExchangeListStruct.model(api))
    def post(self, g8owner_id):
        currency_exchange = CurrencyExchangeListStruct.load(ns.payload) or []
        args = generate_invoices_argparser.parse_args()
        if args["vco_id"]:
            buyer_id = BillingOrganization.get_id(BillingRole.VCO, args["vco_id"])
        elif args["partner_ce_id"]:
            buyer_id = BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, args["partner_ce_id"])
        else:
            buyer_id = None
        billing_business.generate_invoices(
            seller_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id),
            month=args["month"],
            exchange_rates=currency_exchange,
            buyer_id=buyer_id,
        )


set_invoice_parser = reqparse.RequestParser(argument_class=FixedArgument)
set_invoice_parser.add_argument(
    "pdf", type=FileStorage, required=True, location="files", help="PDF to send for this invoice"
)
set_invoice_parser.add_argument(
    "sequence_number", type=int, required=True, location="args", help="Invoice sequence number"
)
set_invoice_parser.add_argument("number", type=str, required=True, location="args", help="formatted invoice number")


@ns.route("/<g8owner_id>/invoices/<invoice_id>")
class Invoice(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(CEOutgoingnvoiceStructFull.model(api))
    @check_invoice_belongs_to_g8owner
    @ns.doc(shortcut="getG8OwnerInvoice", description="Get Invoice")
    def get(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        return get_ce_outgoing_invoice(invoice_id)

    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.expect(generate_invoice_argparser, InvoiceExtraChargesStruct.list_model(api, field="extra_charges"))
    @ns.doc(shortcut="regenerateG8OwnerInvoice", description="Regenerate Invoice")
    def post(self, g8owner_id, invoice_id):
        extra_charges_list = [
            InvoiceExtraChargesStruct(**xtr_charge) for xtr_charge in ns.payload["extra_charges"]
        ] or []
        billing_business.regenerate_invoice(
            invoice_id=invoice_id,
            seller_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id),
            extra_charges_list=extra_charges_list,
            exchange_rate=generate_invoice_argparser.parse_args()["exchange_rate"],
        )

    @authenticated
    @requires_specific_g8_owner
    @ns.response(204, "Deleted")
    @ns.response(400, "Unable to delete non-draft invoice")
    @check_invoice_belongs_to_g8owner
    @ns.doc(shortcut="deleteG8OwnerInvoice", description="Delete Invoice")
    def delete(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        billing_business.delete_invoice(invoice_id)

    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.expect(set_invoice_parser)
    @ns.doc(shortcut="setG8OwnerInvoice", description="Set custom Invoice")
    def put(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        kwargs = set_invoice_parser.parse_args()
        billing_business.mark_invoice_as_sent(
            invoice_id, kwargs["pdf"].read(), kwargs["sequence_number"], kwargs["number"]
        )


send_invoice_parser = reqparse.RequestParser(argument_class=FixedArgument)
send_invoice_parser.add_argument(
    "sequence_number", type=int, default=None, location="args", help="Invoice sequence number"
)

change_invoice_payment_status_parser = reqparse.RequestParser(argument_class=FixedArgument)
change_invoice_payment_status_parser.add_argument(
    "payment_status", type=str, required=True, location="args", help="Invoice payment status"
)


@ns.route("/<g8owner_id>/invoices/<invoice_id>/status")
class InvoicePaymentStatusChange(Resource):
    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.response(204, "Payment Status Changed")
    @ns.response(400, "Unable to Change Payment Status")
    @ns.expect(change_invoice_payment_status_parser)
    @ns.doc(shortcut="changeG8OwnerInvoicePaymentStatus", description="Change Invoice Payment Status")
    def put(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        billing_business.change_invoice_payment_status(invoice_id, **change_invoice_payment_status_parser.parse_args())


@ns.route("/<g8owner_id>/invoices/<invoice_id>/send")
class InvoiceSending(Resource):
    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.expect(send_invoice_parser)
    @ns.doc(shortcut="sendG8OwnerInvoice", description="(Re)Send Invoice")
    def post(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        billing_business.send_invoice(invoice_id, **send_invoice_parser.parse_args())


@ns.route("/<g8owner_id>/invoices/<invoice_id>/pdf")
class InvoicePDF(Resource):
    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.produces(["application/pdf"])
    @ns.doc(shortcut="getG8OwnerInvoicePDF", description="Get Invoice PDF")
    def get(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        pdf = billing_business.get_invoice_pdf(invoice_id)
        return make_response(pdf, 200, {"Content-Type": "application/pdf"})


sequence_number_model = api.model(
    "ExpectedSequenceNumber", {"sequence_number": fields.Integer(description="Expected Sequence number")}
)


@ns.route("/<g8owner_id>/invoices/<invoice_id>/sequence-number")
class LatestSequenceNumber(Resource):
    @authenticated
    @requires_specific_g8_owner
    @check_invoice_belongs_to_g8owner
    @ns.marshal_with(sequence_number_model)
    @ns.doc(shortcut="getG8OwnerLatestSequenceNumber", description="Get Sequence Number")
    def get(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        return {
            "sequence_number": billing_business.get_expected_seq_num(
                seller_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id)
            )
        }


numbering_format_model = api.model(
    "InvoiceNumberingFormat", {"numbering_format": fields.String(description="Invoice numbering format")}
)

numbering_format_argparser = reqparse.RequestParser(argument_class=FixedArgument)
numbering_format_argparser.add_argument(
    "numbering_format", type=str, required=True, location="args", help="Invoice numbering format"
)


@ns.route("/<g8owner_id>/invoices/numbering-format")
class InvoiceNumberingFormat(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(numbering_format_model)
    @ns.doc(shortcut="getG8OwnerInvoiceNumberingFormat", description="Get Numbering Format")
    def get(self, g8owner_id):
        numbering_format = inv_cfg.get_invoice_numbering_format(
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id)
        )
        return {"numbering_format": numbering_format}

    @authenticated
    @requires_specific_g8_owner
    @ns.expect(numbering_format_argparser)
    @ns.doc(shortcut="updateG8OwnerInvoiceNumberingFormat", description="Update Numbering Format")
    def put(self, g8owner_id):
        numbering_format = numbering_format_argparser.parse_args()["numbering_format"]
        inv_cfg.update_invoice_number_format(
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id), numbering_format
        )


legal_terms_model = api.model(
    "InvoiceLegalTerms", {"legal_terms": fields.String(description="Invoice Legal terms", required=True)}
)

payment_terms_model = api.model(
    "InvoicePaymentTerms", {"payment_terms": fields.String(description="Invoice Payment terms", required=True)}
)


@ns.route("/<g8owner_id>/invoices/legal-terms")
class LegalTerms(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(legal_terms_model)
    @ns.doc(shortcut="getG8OwnerInvoiceLegalTerms", description="Get Legal Terms")
    def get(self, g8owner_id):
        return {
            "legal_terms": inv_cfg.get_invoice_legal_terms(
                BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id)
            )
        }

    @authenticated
    @requires_specific_g8_owner
    @ns.expect(legal_terms_model, validate=True)
    @ns.doc(shortcut="updateG8OwnerInvoiceLegalTerms", description="Update Legal Terms")
    def put(self, g8owner_id):
        legal_terms = ns.payload["legal_terms"]
        inv_cfg.update_invoice_legal_terms(
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id), legal_terms
        )


@ns.route("/<g8owner_id>/invoices/payment-terms")
class PaymentTerms(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(payment_terms_model)
    @ns.doc(shortcut="getG8OwnerInvoicePaymentTerms", description="Get Payment Terms")
    def get(self, g8owner_id):
        return {
            "payment_terms": inv_cfg.get_invoice_payment_terms(
                BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id)
            )
        }

    @authenticated
    @requires_specific_g8_owner
    @ns.expect(payment_terms_model, validate=True)
    @ns.doc(shortcut="updateG8OwnerInvoicePaymentTerms", description="Update Payment Terms")
    def put(self, g8owner_id):
        payment_terms = ns.payload["payment_terms"]
        inv_cfg.update_invoice_payment_terms(
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id), payment_terms
        )


ce_invoices_model = api.model(
    "CloudEnablerIncomingInvoiceList",
    {
        "result": fields.List(
            fields.Nested(
                api.model(
                    "CloudEnablerIncomingInvoice",
                    {
                        "invoice_id": fields.String(description="Invoice ID"),
                        "number": fields.String(description="Invoice Number"),
                        "month": fields.Integer(description="Invoice month timestamp"),
                        "payment_status": fields.String(description="Invoice number"),
                        "currency": fields.String(description="Invoice currency"),
                        "total_incl": fields.Float(description="Total Invoice amount after VAT"),
                    },
                )
            )
        )
    },
)


@ns.route("/<g8owner_id>/incoming-invoices")
class IncomingInvoices(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(ce_invoices_model)
    @ns.doc(shortcut="listCloudEnablerIncomingInvoices", description="List Cloudenabler Incoming Invoices")
    def get(self, g8owner_id):
        return {"result": InvoiceModel.list(buyer_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id))}


@ns.route("/<g8owner_id>/incoming-invoices/<invoice_id>/pdf")
class IncomingInvoicePDF(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.produces(["application/pdf"])
    @ns.doc(shortcut="getCloudEnablerIncomingInvoicePDF", description="Get Cloudenabler Incoming Invoice PDF")
    def get(self, g8owner_id, invoice_id):  # pylint: disable=unused-argument
        return make_response(
            billing_business.get_invoice_pdf(invoice_id),
            200,
            {"Content-Type": "application/pdf"},
        )


@ns.route("/<g8owner_id>/documentation")
class Documentation(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(CloudEnablerDocumentationStruct.model(api))
    @ns.doc(shortcut="getCloudEnablerDocumentation", description="Get cloud enabler documentation")
    def get(self, g8owner_id):
        return g8o_business.get_ce_doc_file(g8owner_id)

    @authenticated
    @requires_admin
    @ns.marshal_with(CloudEnablerDocumentationStruct.model(api))
    @ns.doc(shortcut="createCloudEnablerDocumentation", description="create cloud enabler documentation")
    def post(self, g8owner_id):
        return g8o_business.create_ce_doc_file(g8owner_id)


@ns.route("/<g8owner_id>/license-keys")
class LicenseKeys(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(CloudEnablerLicenseKeyStruct.list_model(api))
    @requires_admin
    @ns.doc(
        shortcut="listCeLicenseKeys",
        description="List all cloud enabler license keys",
    )
    def get(self, g8owner_id):
        return {"result": G8OwnerModel.get_ce_license_keys(ce_id=g8owner_id)}

    @authenticated
    @requires_admin
    @ns.expect(CloudEnablerLicenseKeyStruct.model(api))
    @ns.marshal_with(success_model)
    @requires_admin
    @ns.doc(
        shortcut="addCeLicenseKey",
        description="Add new cloud enabler license key",
    )
    def post(self, g8owner_id):
        g8o_business.add_ce_license_key(g8owner_id, **ns.payload)
        return SUCCESS


@ns.route("/<g8owner_id>/license-keys/<os_name>")
class LicenseKey(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(CloudEnablerLicenseKeyStruct.model(api))
    @requires_admin
    @ns.doc(shortcut="getCeLicenseKeyByOs", description="Get cloud enabler license key by os name")
    def get(self, g8owner_id, os_name):
        return g8o_business.get_ce_license_key_by_os(g8owner_id, os_name)

    @authenticated
    @requires_admin
    @ns.expect(update_license_key_parser)
    @ns.marshal_with(success_model)
    @requires_admin
    @ns.doc(shortcut="updateCeLicenseKeysByOs", description="update cloud enabler license key by os name")
    def put(
        self,
        g8owner_id,
        os_name,
    ):
        g8o_business.update_ce_license_key_by_os(g8owner_id, os_name, **update_license_key_parser.parse_args())
        return SUCCESS

    @authenticated
    @requires_admin
    @ns.marshal_with(success_model)
    @requires_admin
    @ns.doc(
        shortcut="deleteCeLicenseKey",
        description="Delete cloud enabler license key",
    )
    def delete(self, g8owner_id, os_name):
        g8o_business.delete_ce_license_key(ce_id=g8owner_id, os_name=os_name)
        return SUCCESS


get_exchange_rates_argparser = reqparse.RequestParser(argument_class=FixedArgument)
get_exchange_rates_argparser.add_argument(
    "vco_id",
    type=str,
    required=False,
    location="args",
    help="Optional VCO ID. If passed, gets Exchange rates only from Cloud Enabler to this VCO",
)


@ns.route("/<g8owner_id>/invoices/exchange-rates")
class InvoicesRequiredRates(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(CurrencyExchangeStruct.list_model(api))
    @ns.expect(get_exchange_rates_argparser)
    @ns.doc(
        shortcut="listRequiredCurrenciesExchangeRate",
        description="List cloud enabler required currencies exchange rate to generate invoice",
    )
    def get(self, g8owner_id):
        vco_id = get_exchange_rates_argparser.parse_args()["vco_id"]
        return {
            "result": billing_business.get_required_currencies(
                seller_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id),
                buyer_id=BillingOrganization.get_id(BillingRole.VCO, vco_id) if vco_id else None,
            )
        }


@ns.route("/<g8owner_id>/invoices/exchange-rates/<currency>")
class InvoicesRequiredRate(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(CurrencyExchangeStruct.model(api))
    @ns.doc(shortcut="getExchangeRate", description="Get currency exchange rate")
    def get(self, currency, g8owner_id):
        return billing_business.get_exchange_rate(
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id), currency
        )


list_licenses_argparser = reqparse.RequestParser(argument_class=FixedArgument)
list_licenses_argparser.add_argument(
    "vco_id", type=str, required=False, default=None, location="args", help="VCO ID to filter Invoices on"
)
list_licenses_argparser.add_argument(
    "month",
    type=int,
    required=False,
    default=None,
    location="args",
    help="Month to filter Invoices on in timestamp format UTC epoch of YYYY-MM-01 00:00:00",
)


@ns.route("/<g8owner_id>/invoices/licenses")
class InvoicesLicenses(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.expect(list_licenses_argparser)
    @ns.marshal_with(VcoSoftwareLicenseOverviewStruct.list_model(api))
    @ns.doc(shortcut="getVCOInvoicedSoftwareLicenses", description="Get Software Licenses in VCO Invoices")
    def get(self, g8owner_id):
        args = list_licenses_argparser.parse_args()
        return {
            "result": billing_business.get_vco_software_licenses_overview(
                seller_id=BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, g8owner_id),
                month=args["month"],
                buyer_id=BillingOrganization.get_id(BillingRole.VCO, args["vco_id"]) if args["vco_id"] else None,
            )
        }


@ns.route("/<g8owner_id>/partners")
class CEPartners(Resource):
    @authenticated
    @ns.doc(shortcut="listCePartners", description="List CE partners")
    @ns.marshal_with(CEPartnerSimpleStruct.list_model(api))
    @requires_specific_g8_owner
    def get(self, g8owner_id):
        return dict(result=list_ce_partners(ce_id=g8owner_id))


@ns.route("/<g8owner_id>/partners/<partner_ce_id>")
class CEPartner(Resource):
    @authenticated
    @ns.doc(shortcut="getCePartnerDetails", description="List CE partners")
    @ns.marshal_with(CEPartnerStruct.model(api))
    @requires_specific_g8_owner
    def get(self, g8owner_id, partner_ce_id):
        return get_ce_partner_info(ce_id=g8owner_id, partner_ce_id=partner_ce_id)

    @authenticated
    @ns.doc(shortcut="updateCePartner", description="Update CE partner details")
    @requires_specific_g8_owner
    @ns.expect(CEPartnerUpdateStruct.model(api))
    def put(self, g8owner_id, partner_ce_id):
        return update_ce_partner_info(
            ce_id=g8owner_id, partner_ce_id=partner_ce_id, update_info=CEPartnerUpdateStruct.load(ns.payload)
        )


@ns.route("/<g8owner_id>/partners/<partner_ce_id>/payment-terms")
class CEPartnerPaymentTerms(Resource):
    @authenticated
    @ns.doc(shortcut="getCePartnerPaymentTerms", description="Get CE partner payment terms")
    @ns.marshal_with(PaymentTermsStruct.model(api))
    @requires_specific_g8_owner
    def get(self, g8owner_id, partner_ce_id):
        return get_payment_terms(
            seller_role=BillingRole.CLOUD_ENABLER,
            seller_id=g8owner_id,
            buyer_role=BillingRole.CLOUD_ENABLER,
            buyer_id=partner_ce_id,
        )

    @authenticated
    @ns.doc(shortcut="setCePartnerPaymentTerms", description="Update CE partner payment terms")
    @requires_specific_g8_owner
    @ns.expect(PaymentTermsStruct.model(api))
    @ns.marshal_with(success_model)
    def put(self, g8owner_id, partner_ce_id):
        set_payment_terms(
            seller_role=BillingRole.CLOUD_ENABLER,
            seller_id=g8owner_id,
            buyer_role=BillingRole.CLOUD_ENABLER,
            buyer_id=partner_ce_id,
            payment_terms=PaymentTermsStruct.load(ns.payload),
        )
        return SUCCESS


@ns.route("/<g8owner_id>/vcos/<vco_id>/payment-terms")
class VCOPaymentTerms(Resource):
    @authenticated
    @ns.doc(shortcut="getVCOPaymentTerms", description="Get VCO payment terms")
    @ns.marshal_with(PaymentTermsStruct.model(api))
    @requires_specific_g8_owner
    def get(self, g8owner_id, vco_id):
        return get_payment_terms(
            seller_role=BillingRole.CLOUD_ENABLER,
            seller_id=g8owner_id,
            buyer_role=BillingRole.VCO,
            buyer_id=vco_id,
        )

    @authenticated
    @ns.doc(shortcut="setVCOPaymentTerms", description="Update CE VCO payment terms")
    @requires_specific_g8_owner
    @ns.marshal_with(success_model)
    @ns.expect(PaymentTermsStruct.model(api))
    def put(self, g8owner_id, vco_id):
        set_payment_terms(
            seller_role=BillingRole.CLOUD_ENABLER,
            seller_id=g8owner_id,
            buyer_role=BillingRole.VCO,
            buyer_id=vco_id,
            payment_terms=PaymentTermsStruct.load(ns.payload),
        )
        return SUCCESS


@ns.route("/<g8owner_id>/backup-targets")
class BackupTargets(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.doc(shortcut="listG8ownerBackupTargets", description="list backup targets")
    @ns.marshal_with(BackupTargetsStruct.list_model(api))
    def get(self, g8owner_id):
        return {"result": Targets.list(ce_id=g8owner_id)}

    @authenticated
    @requires_specific_g8_owner
    @ns.doc(shortcut="createG8ownerBackupTarget", description="create backup target")
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    def post(self, g8owner_id):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        return {"id": backup_business.create_target(ce_id=g8owner_id, data=target_data, by_ce=True)}


@ns.route("/<g8owner_id>/backup-targets/<target_id>")
class BackupTarget(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(BackupTargetStruct.model(api))
    @ns.doc(shortcut="getG8ownerBackupTarget", description="Get a backup target")
    def get(self, g8owner_id, target_id):
        return Targets.get_by_id(
            _id=target_id,
        )

    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteG8ownerBackupTarget", description="Delete a backup target")
    def delete(self, g8owner_id, target_id):
        return backup_business.delete_target(target_id=target_id)

    @authenticated
    @requires_specific_g8_owner
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateG8ownerBackupTarget", description="Update a backup target")
    def put(self, g8owner_id, target_id):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        backup_business.update_target(target_id=target_id, target_data=target_data, by_ce=True, ce_id=g8owner_id)
        return SUCCESS


@ns.route("/<g8owner_id>/backup-targets/<target_id>/subscribe")
class SubscribeBackupTarget(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(success_model)
    @ns.expect(subscribe_g8_parser)
    @ns.doc(shortcut="SubscribeG8ownerBackupTarget", description="subscribe g8 to backup target")
    def post(self, g8owner_id, target_id):
        args = subscribe_g8_parser.parse_args()
        jwt = get_current_user_info().jwt
        backup_business.subscribe_backup_target(
            jwt=jwt,
            target_id=target_id,
            location=args.get("g8_name"),
            externalnetwork_id=args.get("externalnetwork_id"),
            ce_id=g8owner_id,
            by_ce=True,
        )
        return SUCCESS

    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(success_model)
    @ns.expect(unsubscribe_g8_parser)
    @ns.doc(shortcut="UnsubscribeG8ownerBackupTarget", description="unsubscribe g8 to backup target")
    def delete(self, g8owner_id, target_id):
        args = unsubscribe_g8_parser.parse_args()
        jwt = get_current_user_info().jwt
        backup_business.unsubscribe_backup_target(
            target_id=target_id, location=args.get("g8_name"), jwt=jwt, by_ce=True
        )
        return SUCCESS


@ns.route("/<g8owner_id>/backup-policies")
class BackupPolicies(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.doc(shortcut="listG8ownerBackupPolicies", description="List backup policies")
    @ns.marshal_with(BackupPolicyListStruct.list_model(api))
    def get(self, g8owner_id):
        return {"result": Policies.list(by_ce=True, ce_id=g8owner_id)}

    @authenticated
    @requires_specific_g8_owner
    @ns.doc(shortcut="createG8ownerBackupPolicy", description="create backup policy")
    @ns.expect(BackupPolicyStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    def post(self, g8owner_id):
        policy_data = from_dict(BackupPolicyStruct, ns.payload)
        jwt = get_current_user_info().jwt
        return {"id": backup_business.create_policy(policy_data=policy_data, jwt=jwt, by_ce=True, ce_id=g8owner_id)}


@ns.route("/<g8owner_id>/backup-policies/<policy_id>")
class BackupPolicy(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(BackupPoliciesStruct.model(api))
    @ns.doc(shortcut="getG8ownerBackupPolicy", description="Get a backup policy")
    def get(self, g8owner_id, policy_id):
        return Policies.get_by_id(_id=policy_id)

    @authenticated
    @requires_specific_g8_owner
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteG8ownerBackupPolicy", description="Delete a backup policy")
    def delete(self, g8owner_id, policy_id):
        backup_business.delete_policy(policy_id=policy_id)
        return SUCCESS


@ns.route("/knowledge-base-tree")
class KnowledgeBaseTree(Resource):
    @authenticated
    @ns.doc(shortcut="getKnowledgeBaseTree", description="Get knowledge base tree")
    @ns.marshal_with(KnowledgeBaseCategoryTreeStruct.list_model(api))
    @requires_custom(is_admin, is_ce_operations_member)
    def get(self):
        return {"result": get_g8_knowledge_base_tree()}


@ns.route("/knowledge-base")
class KnowledgeBase(Resource):
    @authenticated
    @ns.doc(shortcut="getKnowledgeBaseTopic", description="Get knowledge base topic content")
    @ns.expect(knowledge_base_parser)
    @ns.marshal_with(G8DocumentationStruct.model(api))
    @requires_custom(is_admin, is_ce_operations_member)
    def get(self):
        args = knowledge_base_parser.parse_args()
        return get_knowledge_base_topic(args.get("path"))

    @authenticated
    @ns.expect(knowledge_base_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteKnowledgeBaseTopic", description="Delete knowledge base topic file")
    @requires_custom(is_admin, is_operations_member)
    def delete(self):
        args = knowledge_base_parser.parse_args()
        delete_knowledge_base_topic(args.get("path"))
        return SUCCESS


@ns.route("/knowledge-base/image/<path:image_name>")
class KnowledgeBaseImage(Resource):
    @authenticated
    @ns.doc(shortcut="getKnowledgeBaseImage", description="Get knowledge base topic image")
    @requires_custom(is_admin, is_ce_operations_member)
    def get(self, image_name):
        return send_file(
            get_knowledge_base_image(image_name),
            as_attachment=True,
            attachment_filename=f"{image_name}",
            mimetype="image/png",
        )

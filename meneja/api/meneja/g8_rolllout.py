# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring
from flask_itsyouonline import authenticated
from flask_restx import Resource, reqparse

from meneja.api import api
from meneja.business.auth import requires_admin
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.business.g8_rollout import (
    cancel_rollout,
    create_rollout,
    delete_rollout,
    resume_rollout,
    retry_rollout,
    submit_rollout,
    update_rollout,
)
from meneja.lib.fixes import FixedArgument
from meneja.lib.pagination import get_pagination_model, pagination_handler
from meneja.model.g8_rollout import G8Rollout
from meneja.structs.g8_rollout.g8_rollout_config import G8RolloutConfigStruct

ns = api.namespace("g8-rollouts", description="G8 scheduled rollouts")

create_rollout_model = G8RolloutConfigStruct.model()
get_rollout_fields = generate_restfull_model_spec(G8Rollout, api, True)
get_rollout_model = api.model("Rollout", get_rollout_fields)
rollout_pagination_model = get_pagination_model("rolloutPagination", get_rollout_model)

rollout_list_parser = reqparse.RequestParser(argument_class=FixedArgument)
rollout_list_parser.add_argument(
    "limit", type=int, default=15, location="args", help="Flag to limit the amount of results. 0 means no limit"
)
rollout_list_parser.add_argument(
    "start_after", type=int, location="args", default=0, help="Start returning records after index"
)
rollout_list_parser.add_argument("search", type=str, location="args", default=None, help="Wild card to search by")


@ns.route("/")
class Rollouts(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(rollout_pagination_model)
    @ns.expect(rollout_list_parser)
    @ns.doc(shortcut="listRollouts", description="list G8s rollout")
    def get(self):
        args = rollout_list_parser.parse_args()
        limit = args.get("limit")
        start_after = args.get("start_after")
        search = args.get("search")
        return pagination_handler(G8Rollout.list, limit=limit, start_after=start_after, search=search)

    @authenticated
    @requires_admin
    @ns.expect(create_rollout_model)
    @ns.doc(shortcut="createRollout", description="Create G8s rollout")
    def post(self):
        return create_rollout(G8RolloutConfigStruct(ns.payload).to_mongoengine())


@ns.route("/<string:rollout_id>")
class Rollout(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(get_rollout_model)
    @ns.doc(shortcut="getRollout", description="get G8s rollout")
    def get(self, rollout_id):
        return G8Rollout.get_by_id(rollout_id)

    @authenticated
    @requires_admin
    @ns.expect(create_rollout_model)
    @ns.doc(shortcut="updateRollout", description="Update G8s rollout")
    def put(self, rollout_id):
        return update_rollout(G8RolloutConfigStruct(ns.payload).to_mongoengine(), rollout_id)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deleteRollout", description="delete G8s rollout")
    def delete(self, rollout_id):
        return delete_rollout(rollout_id)


@ns.route("/<string:rollout_id>/submit")
class SubmitRollout(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="submitRollout", description="submit G8s rollout")
    def put(self, rollout_id):
        return submit_rollout(rollout_id)


@ns.route("/<string:rollout_id>/cancel")
class CancelRollout(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="cancelRollout", description="Cancel G8s rollout")
    def put(self, rollout_id):
        return cancel_rollout(rollout_id)


@ns.route("/<string:rollout_id>/resume")
class ResumeRollout(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="resumeRollout", description="Resume G8s rollout")
    def put(self, rollout_id):
        return resume_rollout(rollout_id)


@ns.route("/<string:rollout_id>/retry")
class RetryRollout(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="retryRollout", description="Retry G8s rollout")
    def put(self, rollout_id):
        return retry_rollout(rollout_id)

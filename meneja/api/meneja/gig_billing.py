# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from flask.helpers import make_response
from flask_itsyouonline import authenticated
from flask_restx import Resource, fields, reqparse
from werkzeug.datastructures import FileStorage

import meneja.business.gig_billing as gig_billing
from meneja.api import api
from meneja.business import billing_v2 as billing_business
from meneja.business import invoice_config_v2 as inv_cfg
from meneja.business.auth import (
    is_billing_member,
    is_specific_cloudenabler,
    requires_admin,
    requires_billing_member,
    requires_custom,
)
from meneja.business.gig_billing import get_gig_to_ce_invoice, list_gig_to_ce_invoices
from meneja.business.payment_terms import get_payment_terms, set_payment_terms
from meneja.lib.enumeration import BillingRole
from meneja.lib.fixes import FixedArgument
from meneja.model.billing_organization import BillingOrganization, BillingRelation
from meneja.structs.dataclasses.invoice import GIGToCEInvoiceStruct, InvoiceExtraChargesStruct
from meneja.structs.dataclasses.payment_terms import PaymentTermsStruct
from meneja.structs.dataclasses.pricing import GigFlaggedPricingStruct, GigPricingStruct
from meneja.structs.meneja.dataclasses.currency_exchange import CurrencyExchangeListStruct, CurrencyExchangeStruct
from meneja.structs.meneja.dataclasses.gig_billing_info import GIGBillingInfoStruct

ns = api.namespace("billing", description="Gig Billing to cloud enablers")


@ns.route("/prices")
class GigStandardPrices(Resource):
    """Standard pricing set from Whitesky.cloud to cloud enablers"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(GigPricingStruct.model(api))
    @ns.doc(shortcut="getStandardPrices", description="Get standard prices")
    def get(self):
        """Get standard prices"""
        return gig_billing.get_standard_prices()

    @authenticated
    @requires_billing_member
    @ns.expect(GigPricingStruct.model(api), validate=True)
    @ns.response(204, "Updated")
    @ns.doc(shortcut="setGigToCloudEnablerStandardPrices", description="Set standard prices from GIG to cloud enablers")
    def put(self):
        """Update standard prices"""
        gig_billing.set_standard_prices(GigPricingStruct(**ns.payload))


@ns.route("/prices/<ce_id>")
class GigCloudEnablerPrices(Resource):
    """Specific cloud enabler price management"""

    @authenticated
    @requires_custom(is_billing_member, is_specific_cloudenabler)
    @ns.marshal_with(GigFlaggedPricingStruct.model(api))
    @ns.doc(shortcut="getGigToCloudEnablerPrices", description="Get cloud enabler prices")
    def get(self, ce_id):
        """Get CE prices

        Args:
            ce_id (str): CE ID

        Returns:
            GigFlaggedPricingStruct: pricing object
        """
        return gig_billing.get_cloud_enabler_pricing(cloudenabler_id=ce_id)

    @authenticated
    @requires_billing_member
    @ns.expect(GigPricingStruct.model(api), validate=True)
    @ns.doc(shortcut="setGigToCloudEnablerPrices", description="Update cloud enabler prices")
    def put(self, ce_id):
        """Set custom CE pricing

        Args:
            ce_id (str): CE ID
        """
        gig_billing.set_custom_prices(ce_id, GigPricingStruct(**ns.payload))

    @authenticated
    @requires_billing_member
    @ns.doc(shortcut="deleteGigToCloudEnablerPrices", description="Delete cloud enabler prices")
    def delete(self, ce_id):
        """Reset CE prices to standard prices

        Args:
            ce_id (str): CE ID
        """
        return BillingRelation.delete_pricing(
            BillingRelation.get_id(
                BillingRole.GIG,
                BillingOrganization.parse_organization_id(BillingOrganization.get_gig_organization_id()).entity_id,
                BillingRole.CLOUD_ENABLER,
                ce_id,
            )
        )


list_invoices_argparser = reqparse.RequestParser(argument_class=FixedArgument)
list_invoices_argparser.add_argument(
    "cloudenabler_id",
    type=str,
    required=False,
    default=None,
    location="args",
    help="CloudEnabler ID to filter Invoices on",
)
list_invoices_argparser.add_argument(
    "month",
    type=int,
    required=False,
    default=None,
    location="args",
    help="Month to filter Invoices on in timestamp format UTC epoch of YYYY-MM-01 00:00:00",
)

generate_invoices_argparser = reqparse.RequestParser(argument_class=FixedArgument)
generate_invoices_argparser.add_argument(
    "month",
    type=int,
    required=True,
    location="args",
    help="Month to generate Invoices for in timestamp format UTC epoch of YYYY-MM-01 00:00:00",
)
generate_invoices_argparser.add_argument(
    "cloud_enabler_id",
    type=str,
    required=False,
    location="args",
    help="Optional Cloud Enabler ID. If passed, Invoices will only be generated for this CE",
)


@ns.route("/invoices")
class Invoices(Resource):
    """Whitesky.cloud invoice management"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(GIGToCEInvoiceStruct.list_model(api))
    @ns.expect(list_invoices_argparser)
    @ns.doc(shortcut="listGigInvoices", description="Get Gig to Cloud Enabler Invoices")
    def get(self):
        """Get list of Whitesky.cloud invoices"""
        args = list_invoices_argparser.parse_args()
        cloudenabler_org_id = (
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, args["cloudenabler_id"])
            if args["cloudenabler_id"]
            else None
        )
        return {
            "result": list_gig_to_ce_invoices(
                gig_org_id=BillingOrganization.get_gig_organization_id(),
                month=args["month"],
                cloudenabler_org_id=cloudenabler_org_id,
            )
        }

    @authenticated
    @requires_billing_member
    @ns.response(204, "(Re)generated")
    @ns.expect(generate_invoices_argparser, CurrencyExchangeListStruct.model(api))
    @ns.doc(shortcut="generateGigInvoices", description="(Re)generate Gig to Cloud Enabler Invoices")
    def post(self):
        """Generate invoices for cloudenablers"""
        currency_exchange = CurrencyExchangeListStruct.load(ns.payload) or []
        args = generate_invoices_argparser.parse_args()
        cloudenabler_org_id = (
            BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, args["cloud_enabler_id"])
            if args["cloud_enabler_id"]
            else None
        )
        billing_business.generate_invoices(
            seller_id=BillingOrganization.get_gig_organization_id(),
            month=args["month"],
            exchange_rates=currency_exchange,
            buyer_id=cloudenabler_org_id,
        )


set_invoice_parser = reqparse.RequestParser(argument_class=FixedArgument)
set_invoice_parser.add_argument(
    "pdf", type=FileStorage, required=True, location="files", help="PDF to send for this invoice"
)
set_invoice_parser.add_argument(
    "sequence_number", type=int, required=True, location="args", help="Invoice sequence number"
)
set_invoice_parser.add_argument("number", type=str, required=True, location="args", help="Formatted invoice number")

regenerate_invoice_argparser = reqparse.RequestParser(argument_class=FixedArgument)
regenerate_invoice_argparser.add_argument(
    "exchange_rate",
    type=float,
    required=False,
    default=0,
    location="args",
    help="Exchange rate between Gig and Cloud enabler currency (Required if currencies are different)",
)


@ns.route("/invoices/<invoice_id>")
class Invoice(Resource):
    """Manage invoice"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(GIGToCEInvoiceStruct.model(api))
    @ns.doc(shortcut="getGigInvoice", description="Get Invoice")
    def get(self, invoice_id):
        """Get invoice details

        Args:
            invoice_id (str): Invoice ID

        Returns:
            Invoice: Invoice object
        """
        return get_gig_to_ce_invoice(invoice_id)

    @authenticated
    @requires_billing_member
    @ns.expect(regenerate_invoice_argparser, InvoiceExtraChargesStruct.list_model(api, field="extra_charges"))
    @ns.response(204, "Regenerated")
    @ns.doc(shortcut="regenerateGigInvoice", description="Regenerate Invoice")
    def post(self, invoice_id):
        """Regenerate Invoice

        Args:
            invoice_id (str): Invoice ID
        """
        extra_charges_list = [
            InvoiceExtraChargesStruct(**xtr_charge) for xtr_charge in ns.payload["extra_charges"]
        ] or []
        billing_business.regenerate_invoice(
            invoice_id=invoice_id,
            seller_id=BillingOrganization.get_gig_organization_id(),
            extra_charges_list=extra_charges_list,
            exchange_rate=regenerate_invoice_argparser.parse_args()["exchange_rate"],
        )

    @authenticated
    @requires_billing_member
    @ns.expect(set_invoice_parser)
    @ns.response(204, "Success")
    @ns.doc(shortcut="setGigInvoice", description="Set custom Invoice")
    def put(self, invoice_id):
        """Send invoice

        Args:
            invoice_id (str): Invoice ID
        """
        kwargs = set_invoice_parser.parse_args()
        billing_business.mark_invoice_as_sent(
            invoice_id, kwargs["pdf"].read(), kwargs["sequence_number"], kwargs["number"]
        )

    @authenticated
    @requires_billing_member
    @ns.response(204, "Deleted")
    @ns.response(400, "Unable to delete non-draft invoice")
    @ns.doc(shortcut="deleteGigInvoice", description="Delete Invoice")
    def delete(self, invoice_id):
        """Delete invoice

        Args:
            invoice_id (str): Invoice ID
        """
        billing_business.delete_invoice(invoice_id)


sequence_number_model = api.model(
    "ExpectedSequenceNumber", {"sequence_number": fields.Integer(description="Expected Sequence number")}
)


@ns.route("/invoices/<invoice_id>/sequence-number")
class LatestSequenceNumber(Resource):
    """Get invoice latest sequence number"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(sequence_number_model)
    @ns.doc(shortcut="getGigLatestSequenceNumber", description="Get Sequence Number")
    def get(self, invoice_id):  # pylint: disable=unused-argument
        """Get invoice latest sequence number"""
        return {
            "sequence_number": billing_business.get_expected_seq_num(
                seller_id=BillingOrganization.get_gig_organization_id()
            )
        }


@ns.route("/invoices/<invoice_id>/pdf")
class InvoicePDF(Resource):
    """Get invoice PDF"""

    @authenticated
    @requires_billing_member
    @ns.produces(["application/pdf"])
    @ns.doc(shortcut="getGigInvoicePDF", description="Get Invoice PDF")
    def get(self, invoice_id):
        """Get invoice PDF"""
        pdf = billing_business.get_invoice_pdf(invoice_id)
        return make_response(pdf, 200, {"Content-Type": "application/pdf"})


send_invoice_parser = reqparse.RequestParser(argument_class=FixedArgument)
send_invoice_parser.add_argument(
    "sequence_number", type=int, default=None, location="args", help="Invoice sequence number"
)


@ns.route("/invoices/<invoice_id>/send")
class InvoiceSending(Resource):
    """Send invoice"""

    @authenticated
    @requires_billing_member
    @ns.response(204, "Sent")
    @ns.expect(send_invoice_parser)
    @ns.doc(shortcut="sendGigInvoice", description="(Re)Send Invoice")
    def post(self, invoice_id):
        """Send invoice

        Args:
            invoice_id (str): Invoice ID
        """
        billing_business.send_invoice(
            invoice_id=invoice_id, sequence_number=send_invoice_parser.parse_args()["sequence_number"]
        )


change_invoice_payment_status_parser = reqparse.RequestParser(argument_class=FixedArgument)
change_invoice_payment_status_parser.add_argument(
    "payment_status", type=str, required=True, location="args", help="Invoice payment status"
)


@ns.route("/invoices/<invoice_id>/status")
class InvoicePaymentStatusChange(Resource):
    """Change invoice payment status"""

    @authenticated
    @requires_billing_member
    @ns.response(204, "Payment Status Changed")
    @ns.response(400, "Unable to Change Payment Status")
    @ns.expect(change_invoice_payment_status_parser)
    @ns.doc(shortcut="changeGigInvoicePaymentStatus", description="Change Invoice Payment Status")
    def put(self, invoice_id):
        """Update invoice payment status

        Args:
            invoice_id (str): Invoice ID
        """
        billing_business.change_invoice_payment_status(
            invoice_id=invoice_id, payment_status=change_invoice_payment_status_parser.parse_args()["payment_status"]
        )


get_exchange_rates_argparser = reqparse.RequestParser(argument_class=FixedArgument)
get_exchange_rates_argparser.add_argument(
    "cloudenabler_id",
    type=str,
    required=False,
    location="args",
    help="Optional CloudEnabler ID. If passed, gets Exchange rates only from Gig to this CE",
)


@ns.route("/invoices/exchange-rates")
class InvoicesRequiredRates(Resource):
    """Exchange rates"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(CurrencyExchangeStruct.list_model(api))
    @ns.expect(get_exchange_rates_argparser)
    @ns.doc(
        shortcut="listGIGRequiredCurrenciesExchangeRate",
        description="List required currencies exchange rate to generate invoice",
    )
    def get(self):
        """Get exchange rates"""
        cloudenabler_id = get_exchange_rates_argparser.parse_args()["cloudenabler_id"]
        return {
            "result": billing_business.get_required_currencies(
                seller_id=BillingOrganization.get_gig_organization_id(),
                buyer_id=(
                    BillingOrganization.get_id(BillingRole.CLOUD_ENABLER, cloudenabler_id) if cloudenabler_id else None
                ),
            )
        }


@ns.route("/invoices/exchange-rates/<currency>")
class InvoicesRequiredRate(Resource):
    """Invoice exchange rate"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(CurrencyExchangeStruct.model(api))
    @ns.doc(shortcut="getGigExchangeRate", description="Get currency exchange rate")
    def get(self, currency):
        """Invoice exchange rate"""
        return billing_business.get_exchange_rate(
            billing_org_id=BillingOrganization.get_gig_organization_id(), currency=currency
        )


numbering_format_model = api.model(
    "InvoiceNumberingFormat",
    {
        "numbering_format": fields.String(
            description="""Invoice numbering format
Should be any valid string with numbering components
Components include: YYYY, MM and DD for dates, and ####...# for sequence numbers
At least the sequence number component should exist, i.e. `#+` """
        )
    },
)

numbering_format_argparser = reqparse.RequestParser(argument_class=FixedArgument)
numbering_format_argparser.add_argument(
    "numbering_format", type=str, required=True, location="args", help="Invoice numbering format"
)


@ns.route("/invoices/numbering-format")
class InvoiceNumberingFormat(Resource):
    """Invoice numbering format"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(numbering_format_model)
    @ns.doc(shortcut="getGigInvoiceNumberingFormat", description="Get Numbering Format")
    def get(self):
        """Get invoice numbering format"""
        return {
            "numbering_format": inv_cfg.get_invoice_numbering_format(
                seller_id=BillingOrganization.get_gig_organization_id()
            )
        }

    @authenticated
    @requires_billing_member
    @ns.expect(numbering_format_argparser)
    @ns.doc(shortcut="updateGigInvoiceNumberingFormat", description="Update Numbering Format")
    def put(self):
        """Update invoice numbering format"""
        inv_cfg.update_invoice_number_format(
            seller_id=BillingOrganization.get_gig_organization_id(),
            numbering_format=numbering_format_argparser.parse_args()["numbering_format"],
        )


legal_terms_model = api.model(
    "InvoiceLegalTerms", {"legal_terms": fields.String(description="Invoice Legal terms", required=True)}
)

payment_terms_model = api.model(
    "InvoicePaymentTerms", {"payment_terms": fields.String(description="Invoice Payment terms", required=True)}
)


@ns.route("/invoices/legal-terms")
class LegalTerms(Resource):
    """Legal terms"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(legal_terms_model)
    @ns.doc(shortcut="getGigInvoiceLegalTerms", description="Get Legal Terms")
    def get(self):
        """Get legal terms"""
        return {"legal_terms": inv_cfg.get_invoice_legal_terms(seller_id=BillingOrganization.get_gig_organization_id())}

    @authenticated
    @requires_billing_member
    @ns.expect(legal_terms_model, validate=True)
    @ns.doc(shortcut="updateGigInvoiceLegalTerms", description="Update Legal Terms")
    def put(self):
        """Update legal terms"""
        legal_terms = ns.payload["legal_terms"]
        inv_cfg.update_invoice_legal_terms(seller_id=BillingOrganization.get_gig_organization_id(), terms=legal_terms)


@ns.route("/invoices/payment-terms")
class PaymentTerms(Resource):
    """Payment terms"""

    @authenticated
    @requires_billing_member
    @ns.marshal_with(payment_terms_model)
    @ns.doc(shortcut="getGigInvoicePaymentTerms", description="Get Payment Terms")
    def get(self):
        """Get payment terms"""
        return {
            "payment_terms": inv_cfg.get_invoice_payment_terms(seller_id=BillingOrganization.get_gig_organization_id())
        }

    @authenticated
    @requires_billing_member
    @ns.expect(payment_terms_model, validate=True)
    @ns.doc(shortcut="updateGigInvoicePaymentTerms", description="Update Payment Terms")
    def put(self):
        """Update payment terms"""
        payment_terms = ns.payload["payment_terms"]
        inv_cfg.update_invoice_payment_terms(
            seller_id=BillingOrganization.get_gig_organization_id(), terms=payment_terms
        )


@ns.route("/info")
class GIGBillingInfo(Resource):
    """Gig billing info"""

    @authenticated
    @requires_admin
    @ns.expect(GIGBillingInfoStruct.model(api), validate=True)
    @ns.doc(shortcut="updateGIGBillingInfo", description="Update GIG billing info")
    def put(self):
        """Update billing info"""
        gig_billing.update_gig_billing_info(GIGBillingInfoStruct.load(ns.payload))

    @authenticated
    @requires_admin
    @ns.marshal_with(GIGBillingInfoStruct.model(api))
    @ns.doc(shortcut="getGIGBillingInfo", description="Get GIG Billing Info")
    def get(self):
        """Get billing info"""
        return gig_billing.get_gig_billing_info()


@ns.route("/<ce_id>/payment-terms")
class CEPaymentTerms(Resource):
    """Cloud enabler payment terms"""

    @authenticated
    @ns.doc(shortcut="getCePaymentTerms", description="Get CE payment terms")
    @ns.marshal_with(PaymentTermsStruct.model(api))
    def get(self, ce_id):
        """Cloud enabler payment terms

        Args:
            ce_id (str): CE ID
        """
        return get_payment_terms(
            seller_role=BillingRole.GIG,
            seller_id=BillingOrganization.get_gig_id(),
            buyer_role=BillingRole.CLOUD_ENABLER,
            buyer_id=ce_id,
        )

    @authenticated
    @ns.doc(shortcut="setCePaymentTerms", description="Update CE payment terms")
    @ns.expect(PaymentTermsStruct.model(api))
    def put(self, ce_id):
        """Update Cloud enabler payment terms

        Args:
            ce_id (str): CE ID
        """
        set_payment_terms(
            seller_role=BillingRole.GIG,
            seller_id=BillingOrganization.get_gig_id(),
            buyer_role=BillingRole.CLOUD_ENABLER,
            buyer_id=ce_id,
            payment_terms=PaymentTermsStruct.load(ns.payload),
        )

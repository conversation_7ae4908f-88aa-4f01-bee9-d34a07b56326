# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>DUC<PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring
import logging

from flask import request, send_file
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from gig.g8.osimages import OS_MAP
from werkzeug import exceptions

from meneja.api import api
from meneja.api.common import os_names_model
from meneja.business.auth import (
    get_accessible_g8s,
    get_publishers,
    is_admin,
    is_member_of,
    requires_admin,
    requires_image_publisher,
)
from meneja.business.db_model_2_rest import generate_request_parser, generate_restfull_model_spec
from meneja.business.g8 import attach_image, detach_image, get_user_accounts
from meneja.business.image import (
    create_publisher,
    create_vm_os_image,
    create_vm_os_image_template,
    create_vm_os_image_version,
    delete_publisher,
    delete_vm_os_image,
    delete_vm_os_image_template,
    get_image_version_download_url,
    get_subscribers,
    grant_publisher,
    list_publishers,
    mark_image_as_uploaded,
    ungrant_publisher,
    update_publisher,
    update_publisher_logo,
    update_vm_os_image,
    update_vm_os_image_logo,
    update_vm_os_image_template,
    upload_image_version_part,
)
from meneja.lib.enumeration import ImageCategory, ImageVersionStatus, OSType, PublisherGrants
from meneja.lib.fixes import FixedArgument
from meneja.lib.itsyouonline import AccessDeniedWithoutScope
from meneja.lib.pagination import get_pagination_model, pagination_handler
from meneja.lib.utils import get_logo_as_file
from meneja.model.g8 import G8ImageAvailability, G8Info
from meneja.model.image import Publisher as PublisherModel
from meneja.model.image import UserDataTemplate
from meneja.model.image import VMOSImage as VMOSImageModel
from meneja.model.image import VMOSVersion as VersionModel
from meneja.structs.vco.dataclasses.vco import (
    CreateImageUserDataTemplateStruct,
    UploadVersionExpectStruct,
    UploadVersionStruct,
)

logger = logging.getLogger()

publishers = api.namespace("publishers", description="Publisher organizations")
images = api.namespace("images", description="Virtual Machine Operating System Images")

# models
get_list_publisher_model = api.model(
    "Publishers", generate_restfull_model_spec(PublisherModel, exported_only=True, add_id=True)
)
image_model = api.model(
    "VMOSImage", generate_restfull_model_spec(VMOSImageModel, api=api, add_id=True, exported_only=True)
)
image_pagination_model = get_pagination_model("VMOSImages", image_model)

version_model = api.model("VMOSImageVersion", generate_restfull_model_spec(VersionModel, add_id=True, api=api))
versions_model = api.model(
    "VMOSImageVersions", generate_restfull_model_spec(VersionModel, add_id=True, api=api, exported_only=True)
)
create_version_model = api.model(
    "CreateVMOSImageVersion",
    generate_restfull_model_spec(VersionModel, api=api, skip_readonly=True, exported_only=True),
)
# parsers
create_publisher_argparser = generate_request_parser(PublisherModel)
update_publisher_argparser = generate_request_parser(PublisherModel, editable_only=True)
create_image_argparser = generate_request_parser(VMOSImageModel)
update_image_argparser = generate_request_parser(VMOSImageModel, editable_only=True)
pagination_argparser = reqparse.RequestParser(argument_class=FixedArgument)
pagination_argparser.add_argument(
    "limit", type=int, default=25, location="args", help="Flag to limit the amount of results. default 25 item"
)
pagination_argparser.add_argument(
    "start_after", type=int, location="args", default=None, help="Start returning records after index"
)
image_pagination_argparser = pagination_argparser.copy()
image_pagination_argparser.add_argument(
    "publisher_id", type=str, location="args", action="split", help="Filter images by the publisher id"
)
image_pagination_argparser.add_argument("search", type=str, location="args", default=None, help="search filter")

image_pagination_argparser.add_argument(
    "published_only", type=boolean, location="args", default=False, help="Flag to list only published images"
)
image_pagination_argparser.add_argument(
    "is_appliance",
    type=boolean,
    location="args",
    default=None,
    help="Flag to list appliance images (True), other images (False), or all images (None)",
)
image_pagination_argparser.add_argument(
    "categories",
    type=str,
    action="append",
    location="args",
    choices=ImageCategory.values(),
    help=f"Filter images by one or several categories: {ImageCategory.values()}",
)

logo_model = api.model(
    "Logo",
    {
        "logo": fields.String("Base64 of the image", required=True),
    },
)

attach_image_argparser = reqparse.RequestParser(argument_class=FixedArgument)
attach_image_argparser.add_argument("g8_name", type=str, location="args", required=True, help="G8 name")
attach_image_argparser.add_argument(
    "account_id", type=int, location="args", required=True, help="The id of the owner account of the image in the G8"
)

detach_image_argparser = reqparse.RequestParser(argument_class=FixedArgument)
detach_image_argparser.add_argument("g8_name", type=str, location="args", required=True, help="G8 name")

image_subscribers_model = api.model(
    "ImageSubscribers",
    {
        "g8_name": fields.String(readOnly=True, help_text="G8 name"),
        "availability": fields.String(readOnly=True, help_text="Image availability"),
        "account_id": fields.Integer(readOnly=True, help_text="The id of the owner account"),
    },
)

upload_image_part_parser = reqparse.RequestParser(argument_class=FixedArgument)
upload_image_part_parser.add_argument("object_name", required=True, type=str, location="args", help="object name")

template_model = api.model("VMOSImageTemplate", generate_restfull_model_spec(UserDataTemplate, add_id=True, api=api))

create_template_model = api.model(
    "CreateVMOSImageTemplate",
    generate_restfull_model_spec(UserDataTemplate, api=api, skip_readonly=True, exported_only=True),
)

list_image_user_data_templates_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_image_user_data_templates_parser.add_argument(
    "include_deleted", type=boolean, location="args", default=False, help="Flag to include deleted user data templates"
)


@publishers.route("/")
class Publishers(Resource):
    @authenticated
    @publishers.marshal_list_with(get_list_publisher_model)
    @publishers.doc(shortcut="listPublishers", description="Lists registered publishers")
    def get(self):
        """List publishers

        Returns:
            Iterable: List of Publishers available for current user
        """
        publisher_lists = list_publishers()
        if not is_admin():
            own_publishers = get_publishers()
            shown_publishers = []
            for publisher in publisher_lists:
                if PublisherGrants.LISTED in publisher.grants or publisher.id in own_publishers:
                    shown_publishers.append(publisher)
            publisher_lists = shown_publishers
        return publisher_lists

    @authenticated
    @publishers.doc(shortcut="registerPublisher", description="Register a new publisher and return its id")
    @publishers.expect(create_publisher_argparser)
    def post(self):
        args = create_publisher_argparser.parse_args()
        name = args.get("name")
        www = args.get("www")
        email = args.get("email")
        administrators = args.get("administrators")
        about = args.get("about")
        # Validate that the current user is a memberof administrators
        if not is_member_of(administrators):
            message = f"Not authorized to create publisher for itsyou.online organization {administrators}"
            raise AccessDeniedWithoutScope(message, f"user:memberof:{administrators}")
        meneja_publishers_org = f"{get_current_user_info().organization}.publishers"
        publisher_id = create_publisher(name, www, email, administrators, about, meneja_publishers_org)
        return publisher_id, 201


@publishers.route("/<publisher_id>")
class Publisher(Resource):
    @authenticated
    @publishers.marshal_with(get_list_publisher_model)
    @publishers.doc(shortcut="getPublisher", description="Get Publisher")
    def get(self, publisher_id):
        publisher: PublisherModel = PublisherModel.get_by_id(publisher_id)
        if (
            not is_admin()
            and not is_member_of(publisher.meneja_organization)
            and PublisherGrants.LISTED not in publisher.grants
        ):
            raise exceptions.Forbidden("You don't have access to this Publisher")
        return publisher

    @authenticated
    @publishers.expect(update_publisher_argparser)
    @publishers.doc(shortcut="updatePublisher", description="Update Publisher")
    def put(self, publisher_id):
        args = update_publisher_argparser.parse_args()
        name = args.get("name")
        www = args.get("www")
        email = args.get("email")
        about = args.get("about")

        publisher: PublisherModel = PublisherModel.get_by_id(publisher_id)

        if not is_admin() and not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access to modify this Publisher")

        update_publisher(publisher_id, name, www, email, about)

    @authenticated
    @publishers.doc(shortcut="deletePublisher", description="Delete Publisher")
    def delete(self, publisher_id):
        publisher: PublisherModel = PublisherModel.get_by_id(publisher_id)
        if not is_admin() and not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access to delete this Publisher")
        delete_publisher(publisher_id)
        return True, 200


@publishers.route("/<publisher_id>/logo")
class PublisherLogo(Resource):
    @authenticated
    @publishers.doc(shortcut="getPublisherLogo", description="Get Publisher logo")
    def get(self, publisher_id):
        publisher: PublisherModel = PublisherModel.get_by_id(publisher_id)
        if not publisher.logo:
            return None, 404
        return send_file(
            get_logo_as_file(publisher.logo), as_attachment=True, attachment_filename=f"{publisher.id}.png"
        )

    @authenticated
    @publishers.expect(logo_model)
    @publishers.doc(shortcut="updatePublisherLogo", description="Update Publisher logo")
    def post(self, publisher_id):
        publisher: PublisherModel = PublisherModel.get_by_id(publisher_id)
        if not is_admin():
            if not is_member_of(publisher.meneja_organization):
                raise exceptions.Forbidden("You don't have access rights on this Publisher")

        logo = publishers.payload.get("logo")
        update_publisher_logo(publisher_id, logo)


@publishers.route("/<publisher_id>/grants/<grant>")
class Grants(Resource):
    @authenticated
    @requires_admin
    @publishers.doc(shortcut="grant", description="Grant a specific right to a publisher")
    def post(self, publisher_id, grant):
        grant_publisher(publisher_id, PublisherGrants.from_string(grant))

    @authenticated
    @requires_admin
    @publishers.doc(shortcut="ungrant", description="Ungrants a specific right from a publisher")
    def delete(self, publisher_id, grant):
        ungrant_publisher(publisher_id, PublisherGrants.from_string(grant))


@images.route("/")
class Images(Resource):
    @authenticated
    @images.expect(image_pagination_argparser)
    @images.marshal_with(image_pagination_model)
    @images.doc(shortcut="listImages", description="List virtual machine operating system images")
    def get(self):
        args = image_pagination_argparser.parse_args()
        limit = args.get("limit")
        start_after = args.get("start_after")
        publisher_id = args.get("publisher_id")
        is_appliance = args.get("is_appliance")
        search = args.get("search")
        categories = args.get("categories", [])
        if publisher_id:
            publisher = PublisherModel.get_by_id(publisher_id[0])
            is_publisher_member = is_member_of(publisher.meneja_organization)
        else:
            is_publisher_member = False

        # unpublished images are only visible to the Admins and the Publisher owners
        published_only = args.get("published_only") or (not is_publisher_member and not is_admin())

        return pagination_handler(
            VMOSImageModel.list,
            limit=limit,
            search=search,
            start_after=start_after,
            publisher_id=publisher_id,
            published_only=published_only,
            is_appliance=is_appliance,
            categories=categories,
        )

    @authenticated
    @images.expect(create_image_argparser)
    @images.doc(shortcut="createImage", description="Creates a new image and return its id")
    def post(self):
        args = create_image_argparser.parse_args()
        name = args.get("name")
        os_type = args.get("os_type")
        os_name = args.get("os_name")
        publisher_id = args.get("publisher_id")
        is_appliance = args.get("is_appliance")
        categories = args.get("categories", [])
        description = args.get("description")
        docs_url = args.get("docs_url")
        publisher = PublisherModel.get_by_id(publisher_id)
        if not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on this Publisher to create an image")

        if PublisherGrants.PUBLISH not in publisher.grants:
            raise exceptions.BadRequest("This publisher doesn't have the right to create image")

        return create_vm_os_image(
            name, os_type, os_name, publisher_id, bool(is_appliance), categories, description, docs_url
        )


@images.route("/<image_id>")
class Image(Resource):
    @authenticated
    @images.marshal_with(image_model)
    @images.doc(shortcut="getImage", description="Get virtual machine operating system image")
    def get(self, image_id):
        image = VMOSImageModel.get_by_id(image_id)
        publisher = PublisherModel.get_by_id(image.publisher_id)
        if not is_admin() and not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on to see this image")
        return image

    @authenticated
    @images.expect(update_image_argparser)
    @images.doc(shortcut="updateImage", description="Update virtual machine operating system image")
    def put(self, image_id):
        image = VMOSImageModel.get_by_id(image_id)
        publisher = PublisherModel.get_by_id(image.publisher_id)

        if not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on this Publisher to update images")

        args = update_image_argparser.parse_args()
        name = args.get("name")
        os_type = args.get("os_type")
        os_name = args.get("os_name")
        categories = args.get("categories", [])
        description = args.get("description")
        docs_url = args.get("docs_url")
        update_vm_os_image(image_id, name, os_type, os_name, categories, description, docs_url)

    @authenticated
    @requires_image_publisher
    @images.doc(shortcut="deleteImage", description="Delete Image")
    def delete(self, image_id):
        delete_vm_os_image(image_id)
        return True, 200


@images.route("/<image_id>/subscribers")
class ImageSubscribers(Resource):
    @authenticated
    @images.marshal_list_with(image_subscribers_model)
    @images.doc(shortcut="getImageSubscribers", description="Get image subscribers")
    def get(self, image_id):
        accessible_g8s = get_accessible_g8s()
        return get_subscribers(image_id, accessible_g8s)


@images.route("/<image_id>/attach")
class AttachImage(Resource):
    @authenticated
    @images.expect(attach_image_argparser)
    @images.doc(shortcut="attachPrivateImage", description="Attach private image to a G8")
    def post(self, image_id):
        args = attach_image_argparser.parse_args()
        g8_name = args.get("g8_name")
        account_id = args.get("account_id")
        image = VMOSImageModel.get_by_id(image_id)

        publisher = PublisherModel.get_by_id(image.publisher_id)
        if not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on this Publisher")

        if PublisherGrants.PUBLISH not in publisher.grants:
            raise exceptions.BadRequest("This publisher doesn't have the right to attach images")

        user_info = get_current_user_info()
        user_accounts = get_user_accounts(username=user_info.username, jwt=user_info.jwt, g8_name=g8_name)
        if account_id not in [a["id"] for a in user_accounts]:
            raise exceptions.Forbidden("You don't have access to this G8 account")

        attach_image(g8_name, image_id, user_info.username, account_id)


@images.route("/<image_id>/detach")
class DetachImage(Resource):
    @authenticated
    @images.expect(detach_image_argparser)
    @images.doc(shortcut="detachPrivateImage", description="detach private image from a G8")
    def delete(self, image_id):
        args = detach_image_argparser.parse_args()
        g8_name = args.get("g8_name")
        image = G8Info.get_image_by_id(g8_name, image_id)

        if not image:
            raise ValueError(f"Image {image_id} is not attached to the G8")

        publisher = PublisherModel.get_by_id(image.publisher_id)
        if not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on this Publisher")

        if image.availability == G8ImageAvailability.PUBLIC:
            raise exceptions.BadRequest("Can't detach public images")

        user_info = get_current_user_info()
        user_accounts = get_user_accounts(username=user_info.username, jwt=user_info.jwt, g8_name=g8_name)
        if image.account_id not in [a["id"] for a in user_accounts]:
            raise exceptions.Forbidden("You don't have access to the image's account")

        detach_image(g8_name, image_id)


@images.route("/<image_id>/logo")
class ImageLogo(Resource):
    @authenticated
    @images.doc(shortcut="getImageLogo", description="Get virtual machine operating system image logo")
    def get(self, image_id):
        image: VMOSImageModel = VMOSImageModel.get_by_id(image_id)
        if not image.logo:
            return None, 404
        return send_file(get_logo_as_file(image.logo), as_attachment=True, attachment_filename=f"{image.id}.png")

    @authenticated
    @images.expect(logo_model)
    @images.doc(shortcut="setImageLogo", description="set virtual machine operating system image logo")
    def post(self, image_id):
        image: VMOSImageModel = VMOSImageModel.get_by_id(image_id)
        publisher = PublisherModel.get_by_id(image.publisher_id)
        if not is_member_of(publisher.meneja_organization):
            raise exceptions.Forbidden("You don't have access rights on this Publisher")

        logo = publishers.payload.get("logo")
        update_vm_os_image_logo(image_id, logo)


@images.route("/<image_id>/versions")
class Versions(Resource):
    @authenticated
    @images.expect(pagination_argparser)
    @images.marshal_list_with(versions_model)
    @images.doc(shortcut="listVMOSImageVersions", description="List virtual machine operating system image versions")
    def get(self, image_id):
        args = pagination_argparser.parse_args()
        limit = args.get("limit")
        start_after = args.get("start_after")
        versions = VMOSImageModel.list_versions(image_id, limit, start_after)
        return list(versions)

    @authenticated
    @images.expect(create_version_model)
    @images.marshal_with(UploadVersionStruct.model(api))
    @images.doc(
        shortcut="createVMOSImageVersion", description="Create a virtual machine operating system image version"
    )
    def post(self, image_id):
        return create_vm_os_image_version(image_id=image_id, version_payload=images.payload)


@images.route("/<image_id>/upload-version/<upload_id>/upload-part/<part_id>")
class UploadImageVersionPart(Resource):
    @authenticated
    @images.expect(upload_image_part_parser)
    @images.doc(
        shortcut="UploadImageVersionPart",
        description="Upload image version part. Part must be sent in the request body as `application/octet-stream`",
    )
    def put(self, image_id, upload_id, part_id):  # pylint: disable=unused-argument
        kwargs = upload_image_part_parser.parse_args()
        object_name = kwargs.get("object_name")
        return upload_image_version_part(upload_id, part_id, object_name, request.get_data())


@images.route("/<image_id>/current-version")
class CurrentVersion(Resource):
    @authenticated
    @images.marshal_with(version_model)
    @images.doc(
        shortcut="getCurrentVMOSImageVersion",
        description="Get the current published virtual machine operating system image version",
    )
    def get(self, image_id):
        return VMOSImageModel.get_version_by_status(image_id, ImageVersionStatus.READY.value)


@images.route("/<image_id>/versions/<version_id>")
class Version(Resource):
    @authenticated
    @images.marshal_with(version_model)
    @images.doc(shortcut="getVMOSImageVersion", description="Get the virtual machine operating system image version")
    def get(self, image_id, version_id):
        return VMOSImageModel.get_version_by_id(image_id, version_id)


@images.route("/<image_id>/versions/<version_id>/uploaded")
class VersionUploaded(Resource):
    @authenticated
    @images.doc(
        shortcut="markAsUploaded",
        description="Notifies meneja about the fact that the image has been uploaded to the location as requested",
    )
    @images.expect(UploadVersionExpectStruct.model(api))
    def post(self, image_id, version_id):
        return mark_image_as_uploaded(image_id, version_id, **images.payload)


@images.route("/<image_id>/versions/<version_id>/download")
class DownloadVersion(Resource):
    @authenticated
    @images.doc(shortcut="downloadVMImageVersion", description="Download vm os image version")
    def get(self, image_id, version_id):
        image = VMOSImageModel.get_by_id(image_id)
        version = VMOSImageModel.get_version_by_id(image_id, version_id)
        url = get_image_version_download_url(image, version)
        return str(url)


@images.route("/os-types")
class OSTypes(Resource):
    @authenticated
    @images.doc(shortcut="getOSTypes", description="Get OS types")
    def get(self):
        return OSType.values()


@images.route("/os-names")
class OSNames(Resource):
    @authenticated
    @images.doc(shortcut="getOSNames", description="Get supported OS names")
    @images.marshal_with(os_names_model)
    def get(self):
        return {"result": [{"os_names": list(os_names), "os_type": os_type} for os_type, os_names in OS_MAP.items()]}


@images.route("/categories")
class ImageCategories(Resource):
    @authenticated
    @images.doc(shortcut="getImageCategories", description="Get Image Categories")
    def get(self):
        return ImageCategory.values()


@images.route("/<image_id>/user-templates")
class ImageTemplates(Resource):
    @authenticated
    @requires_image_publisher
    @images.expect(list_image_user_data_templates_parser)
    @images.marshal_list_with(template_model)
    @images.doc(
        shortcut="listVMOSImageUserDataTemplates",
        description="List virtual machine operating system image user data templates.",
    )
    def get(self, image_id):
        args = list_image_user_data_templates_parser.parse_args()
        include_deleted = args.get("include_deleted")
        return VMOSImageModel.list_templates(image_id, include_deleted)

    @authenticated
    @requires_image_publisher
    @images.expect(create_template_model)
    @images.marshal_with(CreateImageUserDataTemplateStruct.model(api))
    @images.doc(
        shortcut="createVMOSImageUserDataTemplate",
        description="Create a virtual machine operating system image template.",
    )
    def post(self, image_id):
        payload = images.payload
        return create_vm_os_image_template(
            image_id,
            payload.get("label"),
            payload.get("content"),
            payload.get("description"),
            payload.get("parameters", []),
        )


@images.route("/<image_id>/user-templates/<template_id>")
class ImageTemplate(Resource):
    @authenticated
    @requires_image_publisher
    @images.marshal_with(template_model)
    @images.doc(shortcut="getVMOSImageUserDataTemplate", description="Get image user data template")
    def get(self, image_id, template_id):
        return VMOSImageModel.get_template_by_id(image_id, template_id)

    @authenticated
    @requires_image_publisher
    @images.expect(create_template_model)
    @images.doc(shortcut="updateVMOSImageUserDataTemplate", description="Update image user data template")
    def put(self, image_id, template_id):
        payload = images.payload
        update_vm_os_image_template(
            image_id,
            template_id,
            payload.get("label"),
            payload.get("content"),
            payload.get("description"),
            payload.get("parameters", []),
        )

    @authenticated
    @requires_image_publisher
    @images.doc(shortcut="deleteVMOSImageUserDataTemplate", description="Delete template")
    def delete(self, image_id, template_id):
        return delete_vm_os_image_template(image_id, template_id)

# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import os
import time

from flask import g as flask_g
from flask import session
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse

import meneja.business.g8 as G8_business
from meneja.api import api
from meneja.business.auth import (
    get_accessible_g8_objects,
    get_accessible_g8s,
    get_organizations,
    get_publishers,
    is_admin,
    is_billing_member,
    is_developer,
    is_g8_owner,
    is_member_of,
)
from meneja.business.g8_owners import list_accessible_g8owners
from meneja.business.notifications import get_email_subscription, get_new_notification_types, update_email_subscription
from meneja.lib.enumeration import AccessRight, NotificationIssuer, NotificationPlatform
from meneja.lib.fixes import FixedArgument
from meneja.structs.notification_subscription.user_email_subscription_config import (
    UserEmailSubscriptionConfigEmailPreferences,
)

MNJ_CE_OPERATIONS_ORG = os.environ.get("MNJ_CE_OPERATIONS_ORG")
OPERATIONS_ORG = os.environ.get("OPERATIONS_ORG")

ns = api.namespace("me", description="Me")

user_avatar = api.model(
    "UserAvatar",
    {
        "label": fields.String(readOnly=True, description="Avatar's label"),
        "source": fields.String(readOnly=True, description="Avatar's source"),
    },
)

user_model = api.model(
    "User",
    {
        "username": fields.String(readOnly=True, description="The username of the user"),
        "firstname": fields.String(readOnly=True, description="User's first name"),
        "lastname": fields.String(readOnly=True, description="User's last name"),
        "email": fields.String(readOnly=True, description="User's email"),
        "avatar": fields.List(fields.Nested(user_avatar), description="List of user's avatars"),
        "is_admin": fields.Boolean(readOnly=True, description="is user admin or not"),
        "is_developer": fields.Boolean(readOnly=True, description="is user developer or not"),
        "is_g8_owner": fields.Boolean(readOnly=True, description="is user own at least one G8"),
        "is_billing_member": fields.Boolean(readOnly=True, description="does the user have billing access"),
        "cloudenablers": fields.Nested(
            api.model(
                "AccessibleCloudenablers",
                {
                    "g8owner_id": fields.String(description="Cloudenabler Id in meneja", required=True),
                    "name": fields.String(description="Cloudenabler company name", required=True),
                },
            )
        ),
        "is_operations_member": fields.Boolean(readOnly=True, description="is user member of operations team"),
        "is_ce_operations_member": fields.Boolean(
            readOnly=True, description="is user member of meneja operations team"
        ),
        "g8s": fields.List(fields.String(), help_text="List of g8s that the user has access to"),
        "publishers": fields.List(fields.String(), help_text="List of publishers that the user has access to"),
    },
)

user_accounts_model = api.model(
    "UserAccount",
    {
        "id": fields.Integer(readOnly=True, description="Account id"),
        "name": fields.String(readOnly=True, description="Account name"),
        "right": fields.String(readOnly=True, description="Access rights"),
        "g8": fields.String(readOnly=True, description="G8 name"),
        "g8_url": fields.String(readOnly=True, description="G8 url"),
    },
)

user_accounts_parser = reqparse.RequestParser(argument_class=FixedArgument)
user_accounts_parser.add_argument(
    "g8_name", type=str, default=None, location="args", help="Optional flag to limit results for specific G8"
)
user_accounts_parser.add_argument(
    "access_right",
    type=str,
    default=AccessRight.ADMIN.name,
    location="args",
    help="User access rights on the accounts",
    choices=list(AccessRight.__members__.keys()),
)


@ns.route("/")
class User(Resource):
    """User info Class"""

    @authenticated
    @ns.marshal_with(user_model)
    @ns.doc(shortcut="getCurrentUserInfo", description="Gets current user info")
    def get(self):
        """Get user info"""
        user_info = get_current_user_info()
        orgs = list(get_organizations())  # to query IAM once isof 5 times
        g8s = get_accessible_g8s(organizations=orgs)

        # update session with the accessible g8s
        accessible_g8s_info = {"g8s": get_accessible_g8s(), "updated_at": int(time.time())}
        flask_g.accessible_g8s = accessible_g8s_info
        if session:
            session["accessible_g8s"] = accessible_g8s_info
        root_org = os.environ["MNJ_IAM_ORGANIZATION"]

        return dict(
            username=user_info.username,
            firstname=user_info.firstname,
            lastname=user_info.lastname,
            email=user_info.email,
            avatar=user_info.info.get("avatar"),
            is_admin=is_admin(),
            is_developer=is_developer(),
            is_g8_owner=is_g8_owner(organizations=orgs),
            is_billing_member=is_billing_member(organizations=orgs),
            cloudenablers=list_accessible_g8owners(root_org, orgs),
            publishers=get_publishers(organizations=orgs),
            is_operations_member=is_member_of(organization=OPERATIONS_ORG, organizations=orgs),
            is_ce_operations_member=is_member_of(organization=MNJ_CE_OPERATIONS_ORG, organizations=orgs),
            g8s=g8s,
        )


@ns.route("/accounts")
class UserG8Accounts(Resource):
    """User G8 account class"""

    @authenticated
    @ns.expect(user_accounts_parser)
    @ns.marshal_list_with(user_accounts_model)
    @ns.doc(shortcut="getCurrentUserG8Account", description="Gets current user G8(s) accounts")
    def get(self):
        """Get current user g8 account"""
        user_info = get_current_user_info()
        args = user_accounts_parser.parse_args()
        g8_name = args.get("g8_name")
        access_right = AccessRight.get_by_name(args.get("access_right"))
        return G8_business.get_user_accounts(user_info.username, user_info.jwt, g8_name, access_right)


@ns.route("/jwt")
class UserJWT(Resource):
    """User JWT class"""

    @authenticated
    @ns.doc(shortcut="getCurrentUserJWT", description="Gets current user JWT token")
    def get(self):
        """Get current user jwt"""
        return get_current_user_info().jwt


@ns.route("/email-subscriptions")
class UserEmailSubscriptions(Resource):
    """User email subscriptions"""

    @authenticated
    @ns.marshal_with(UserEmailSubscriptionConfigEmailPreferences.model())
    @ns.doc(shortcut="getEmailSubscriptions", description="Gets Email Subscriptions")
    def get(self):
        """Get user email subscriptions"""
        user_name = get_current_user_info().username
        g8s = get_accessible_g8_objects()
        platform = str(NotificationPlatform.MENEJA)
        return get_email_subscription(platform=platform, user_name=user_name, g8s=g8s)

    @authenticated
    @ns.expect(UserEmailSubscriptionConfigEmailPreferences.model())
    @ns.doc(shortcut="updateEmailSubscription", description="update Email Subscription")
    def put(self):
        """Modify user email subscription"""
        user_info = get_current_user_info()
        g8s = get_accessible_g8_objects()
        platform = str(NotificationPlatform.MENEJA)
        issuer_id = str(NotificationIssuer.GIG)
        return update_email_subscription(
            user_name=user_info.username,
            email=user_info.email,
            preferences=UserEmailSubscriptionConfigEmailPreferences(ns.payload).to_mongoengine(),
            g8s=g8s,
            platform=platform,
            issuer_id=issuer_id,
        )


@ns.route("/new-email-subscriptions")
class UserNewEmailSubscriptions(Resource):
    """New email subscription"""

    @authenticated
    @ns.marshal_with(UserEmailSubscriptionConfigEmailPreferences.model())
    @ns.doc(shortcut="getNewEmailSubscriptions", description="Gets new email subscriptions")
    def get(self):
        """Get new email subscription"""
        user_info = get_current_user_info()
        g8s = get_accessible_g8_objects()
        platform = str(NotificationPlatform.MENEJA)
        return get_new_notification_types(
            user_name=user_info.username, g8s=g8s, platform=platform, email=user_info.email
        )

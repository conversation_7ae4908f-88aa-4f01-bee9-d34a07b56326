# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=missing-class-docstring
# pylint: disable=missing-function-docstring
from dynaqueue.server import STATUS_FAILED, STATUS_QUEUED, <PERSON>ATUS_RUNNING, STATUS_SUCCEEDED
from flask_itsyouonline import authenticated
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean

from meneja.api import api
from meneja.api.meneja import SUCCESS, audit_model, audits_list_argparser, audits_pagination_model
from meneja.api.vco import success_model
from meneja.business.auth import is_admin, is_developer, requires_admin, requires_custom
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.business.issue import snooze_issue
from meneja.business.meneja import (
    get_alarm_room_user_name,
    get_task_logs,
    get_workflow_info,
    health_status,
    kill_task,
    list_all_workflows,
    set_alarm_room_credentials,
)
from meneja.business.notifications import (
    create_gig_blog,
    create_notification,
    delete_notification,
    get_blog_preview,
    get_notifications,
    schedule_send_notification_emails,
    send_gig_notification,
    send_test_gig_notification,
    unpublish_blog,
    update_notification,
)
from meneja.lib.enumeration import ComponentStatus, NotificationIssuer, NotificationType
from meneja.lib.fixes import FixedArgument
from meneja.lib.pagination import get_pagination_model, pagination_handler, pagination_handler_for_audits
from meneja.model.audit import AuditLog
from meneja.model.error import ErrorLog
from meneja.model.healthchecks import HealthChecksLog
from meneja.model.issue import Issue as issue_model
from meneja.model.notifications import EmailOutbox, Notifications
from meneja.model.prerelease import PreReleaseFeatures
from meneja.structs.dataclasses.issue import IssueSimpleStruct, IssueStruct
from meneja.structs.meneja.dataclasses.alarm_room import AlarmRoomStruct
from meneja.structs.meneja.dataclasses.k8s_certs import K8sCertValidity
from meneja.structs.meneja.dataclasses.logs import LogLine
from meneja.structs.meneja.dataclasses.meneja_health_logs import MenejaHealthLogsStruct, MenejaHealthLogStruct
from meneja.structs.meneja.dataclasses.tasks import TaskStatus
from meneja.structs.notification_email_outbox.notification_email_outbox_config import (
    NotificationEmailOutboxConfigStruct,
)
from meneja.structs.notification_subscription.dataclasses import TestEmailsStruct
from meneja.structs.notification_subscription.notification_config import NotificationConfigStruct
from meneja.structs.vco.dataclasses.prerelease_feature import PreReleaseFeatureStruct

ns = api.namespace("meneja", description="Generic meneja information")

menejaStatusDynaqueueWorker = api.model(
    "MenejaStatusDynaqueueWorker",
    {
        "mongodb": fields.String(
            readOnly=True, description="Shows if the mongodb is reachable from the meneja server", enum=("OK", "ERROR")
        ),
        "redis": fields.String(
            readOnly=True, description="Shows if the redis is reachable from the meneja server", enum=("OK", "ERROR")
        ),
        "minio": fields.String(
            readOnly=True, description="Shows if the minio is reachable from the meneja server", enum=("OK", "ERROR")
        ),
        "dynaqueue": fields.String(
            readOnly=True, description="Shows if the dynaqueue is working properly", enum=("OK", "ERROR")
        ),
    },
)

menejaStatusVCODownload = api.model(
    "MenejaStatusVCODownload",
    {
        "vco_id": fields.String(readOnly=True, description="Virtual Cloud Operator ID"),
        "cli": fields.String(readOnly=True, description="Shows if the cli is downloadable", enum=("OK", "ERROR")),
        "tfp": fields.String(
            readOnly=True, description="Shows if the terraform plugin is downloadable", enum=("OK", "ERROR")
        ),
        "csi": fields.String(
            readOnly=True, description="Shows if the csi driver is downloadable", enum=("OK", "ERROR")
        ),
    },
)
k8s_certificates_response = api.model("K8sCertificateStatus", K8sCertValidity.model(api))

menejaStatusDetail = api.model(
    "MenejaStatusDetail",
    {
        "mongodb": fields.String(
            readOnly=True,
            description="Shows if the mongodb is reachable from the meneja server",
            enum=ComponentStatus.values(),
        ),
        "collectors": fields.String(
            readOnly=True,
            description="Shows if the collectors have run properly over the last 10 minutes",
            enum=ComponentStatus.values(),
        ),
        "redis": fields.String(
            readOnly=True,
            description="Shows if the redis is reachable from the meneja server",
            enum=ComponentStatus.values(),
        ),
        "minio": fields.String(
            readOnly=True,
            description="Shows if the minio is reachable from the meneja server",
            enum=ComponentStatus.values(),
        ),
        "dynaqueue": fields.String(
            readOnly=True, description="Shows if the dynaqueue is working properly", enum=ComponentStatus.values()
        ),
        "dynaqueue_worker": fields.Nested(
            menejaStatusDynaqueueWorker,
            description="Detailed status information of the dynaqueue workers without queue",
        ),
        "dns": fields.String(
            readOnly=True, description="Shows if dns is working properly", enum=ComponentStatus.values()
        ),
        "meneja_backup": fields.String(
            readOnly=True, description="Check if recent backups were made", enum=ComponentStatus.values()
        ),
        "iam_backup": fields.String(
            readOnly=True, description="Check if recent backups were made", enum=ComponentStatus.values()
        ),
        "vco_operator": fields.String(
            readOnly=True, description="Check if vco operator is capturing changes", enum=ComponentStatus.values()
        ),
        "customer_bi_collection": fields.String(
            readOnly=True,
            description="Check if customer BI collection is working properly",
            enum=ComponentStatus.values(),
        ),
        "k8s_certificates": fields.List(
            fields.Nested(k8s_certificates_response), description="List of status for k8s certificates"
        ),
        "notification_emails": fields.String(
            readOnly=True, description="Shows if notification email outbox is empty", enum=ComponentStatus.values()
        ),
        "update_certificates": fields.String(
            readOnly=True,
            description="Check if certificate updating is working properly",
            enum=ComponentStatus.values(),
        ),
        "update_cluster_certificate": fields.String(
            readOnly=True,
            description="Check if certificate updating is working properly",
            enum=ComponentStatus.values(),
        ),
        "nodes": fields.String(
            readOnly=True,
            description="Check if cluster nodes are working properly",
            enum=ComponentStatus.values(),
        ),
        "backup_s3": fields.String(
            readOnly=True,
            description="Check if recent S3 backups were made",
            enum=ComponentStatus.values(),
        ),
        "emails_export": fields.String(
            readOnly=True,
            description="check if exporting emails is working properly",
            enum=ComponentStatus.values(),
        ),
    },
)

menejaStatus = api.model(
    "MenejaStatus",
    {
        "status": fields.String(readOnly=True, description="General Meneja status", enum=("OK", "WARNING", "CRITICAL")),
        "detail": fields.Nested(menejaStatusDetail, description="Detailed status information"),
        "vco_downloads": fields.List(
            fields.Nested(menejaStatusVCODownload), description="Detailed status information about VCO downloads"
        ),
    },
)

workflowSummary = api.model(
    "Workflow",
    {
        "id": fields.String(readOnly=True, description="Globally unique workflow id"),
        "title": fields.String(readOnly=True, description="Short description of the workflow"),
        "status": fields.String(
            readOnly=True,
            description="Current status of the task",
            enum=(STATUS_QUEUED, STATUS_RUNNING, STATUS_SUCCEEDED, STATUS_FAILED),
        ),
        "created_on": fields.Float(readOnly=True, description="Epoch timestamp when the workflow was submitted"),
        "executed_on": fields.Float(readOnly=True, description="Epoch timestamp when the workflow finished executing"),
        "picked_up_on": fields.Float(
            readOnly=True, description="Epoch timestamp when the workflow was picked up by a worker"
        ),
        "workflow_status": fields.String(readonly=True, description="Workflow status"),
    },
)
workflow_pagination_model = get_pagination_model("WorkflowsPagination", workflowSummary)

log_line_model = LogLine.model(api)

taskStatus = TaskStatus.model(api)

workflow_list_parser = reqparse.RequestParser(argument_class=FixedArgument)
workflow_list_parser.add_argument(
    "limit", type=int, default=15, location="args", help="Flag to limit the amount of results. 0 means no limit"
)
workflow_list_parser.add_argument(
    "start_after",
    type=str,
    location="args",
    default=None,
    help="Start returning records after workflow with id start_after",
)
workflow_list_parser.add_argument(
    "start_time", type=float, location="args", default=None, help="Return workflows created after this date"
)
workflow_list_parser.add_argument(
    "end_time", type=float, location="args", default=None, help="Return workflows created before this date"
)
workflow_list_parser.add_argument("search", type=str, location="args", default=None, help="Wild card to search by")
workflow_list_parser.add_argument("status", type=str, location="args", default=None, help="Workflow status")

workflow_get_parser = reqparse.RequestParser(argument_class=FixedArgument)
workflow_get_parser.add_argument(
    "include_logs",
    type=boolean,
    location="args",
    default=True,
    help="Flag to whether to include logs in the result set",
)
workflow_get_parser.add_argument(
    "include_debug_logs",
    type=boolean,
    location="args",
    default=True,
    help="Flag to whether to include debug log records. " + "This parameter is only relevant if include_logs is True",
)

task_log_get_parser = reqparse.RequestParser(argument_class=FixedArgument)
task_log_get_parser.add_argument(
    "include_debug_logs",
    type=boolean,
    location="args",
    default=True,
    help="Flag to whether to include debug log records. ",
)

errors_list_argparser = reqparse.RequestParser(argument_class=FixedArgument)
errors_list_argparser.add_argument(
    "limit", type=int, default=25, location="args", help="Flag to limit the amount of results."
)
errors_list_argparser.add_argument(
    "start_after", type=int, location="args", default=None, help="Start returning records after index"
)
errors_list_argparser.add_argument("search", type=str, location="args", default=None, help="search for the error")
errors_list_argparser.add_argument(
    "last_occurrence_start_time", type=int, location="args", default=None, help="Starting time of error occurrence"
)
errors_list_argparser.add_argument(
    "last_occurrence_end_time", type=int, location="args", default=None, help="Ending time of error occurrence"
)

error_log_model = api.model("ErrorLog", generate_restfull_model_spec(ErrorLog, api=api))
error_log_model["message"] = fields.String(readOnly=True, description="error msg")
error_logs_model = api.model("ErrorLogs", generate_restfull_model_spec(ErrorLog, api=api, exported_only=True))
error_logs_model["message"] = fields.String(readOnly=True, description="error msg")
error_logs_pagination_model = get_pagination_model("ErrorLogsPagination", error_logs_model)
meneja_health_logs_model = get_pagination_model("MenejaHealthLogs", MenejaHealthLogsStruct.model(api))
detailed_health_error_log = MenejaHealthLogStruct.model(api)
healthcheck_log_parser = reqparse.RequestParser()
healthcheck_log_parser.add_argument(
    "start_after", type=int, default=0, help="Start returning records after  this index"
)
healthcheck_log_parser.add_argument("limit", type=int, default=10, help="Limits the amount of returned results")

email_notification_model = NotificationConfigStruct.model()

test_email_model = TestEmailsStruct.model(api)

email_notification_get_parser = reqparse.RequestParser(argument_class=FixedArgument)

email_notification_get_parser.add_argument(
    "include_past", type=boolean, default=False, location="args", help="include past maintenance notifications"
)
email_notification_get_parser.add_argument(
    "notification_type", type=str, choices=NotificationType.values(), location="args", help="notification type"
)

resources_domain_parser = reqparse.RequestParser(argument_class=FixedArgument)
resources_domain_parser.add_argument("record_type", type=str, location="args", help="Type of record to be checked")
audits_list_argparser.add_argument("vco", type=str, default=None, location="args", help="Filter by VCO ID")

issue_list_parser = reqparse.RequestParser(argument_class=FixedArgument)
issue_list_parser.add_argument(
    "limit", type=int, default=15, location="args", help="Flag to limit the amount of results. 0 means no limit"
)
issue_list_parser.add_argument(
    "start_after",
    type=int,
    location="args",
    default=0,
    help="Start returning records after n records",
)
issue_list_parser.add_argument(
    "start_time", type=int, location="args", default=None, help="Return issues last occured after this date"
)
issue_list_parser.add_argument(
    "end_time", type=int, location="args", default=None, help="Return issues last occured before this date"
)
issue_list_parser.add_argument(
    "include_snoozed", type=bool, location="args", default=None, help="Include snoozed issues"
)
issue_list_pagination_model = get_pagination_model("IssueListPaginationModel", IssueSimpleStruct.model(api))
edit_issue_parser = reqparse.RequestParser(argument_class=FixedArgument)
edit_issue_parser.add_argument(
    "snooze_until",
    type=int,
    required=True,
    location="args",
    help="snooze untill",
)

pre_release_features_parser = reqparse.RequestParser()
pre_release_features_parser.add_argument("vco", type=str, location="args", help="VCO")
pre_release_features_parser.add_argument("feature", type=str, location="args", help="Feature")
create_pre_release_feature_parser = reqparse.RequestParser()
create_pre_release_feature_parser.add_argument("feature", type=str, required=True, help="Feature name for the feature")
create_pre_release_feature_parser.add_argument(
    "vcos", type=str, action="append", required=False, help="List of vcos is required"
)


@ns.route("/health-status")
class MenejaHealth(Resource):
    @ns.doc("getMenejaHealthStatus", description="Calculates the meneja health status")
    @ns.marshal_with(menejaStatus)
    def get(self):
        status = health_status()
        return status, 200


@ns.route("/health-logs")
class MenejaHealthLogs(Resource):
    @ns.doc("getMenejaHealthLogs", description="Gets all meneja health error logs")
    @ns.marshal_with(meneja_health_logs_model)
    @ns.expect(healthcheck_log_parser)
    def get(self):
        args = healthcheck_log_parser.parse_args()
        return pagination_handler(HealthChecksLog.list, **args)


@ns.route("/health-logs/<traceback_hash>")
class MenejaHealthLog(Resource):
    @ns.doc("getMenejaHealthLog", description="Gets Healthcheck Log error by traceback hash")
    @ns.marshal_with(detailed_health_error_log)
    def get(self, traceback_hash):
        return HealthChecksLog.get_by_trace_hash(traceback_hash)


@ns.route("/workflow")
class Workflows(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("listWorkflows", description="Lists workflows running in the meneja server")
    @ns.marshal_with(workflow_pagination_model)
    @ns.expect(workflow_list_parser)
    def get(self):
        args = workflow_list_parser.parse_args()
        limit = args.get("limit")
        start_after = args.get("start_after")
        start_time = args.get("start_time")
        end_time = args.get("end_time")
        search = args.get("search")
        status = args.get("status")
        return pagination_handler(
            list_all_workflows,
            limit=limit,
            start_after=start_after,
            start_time=start_time,
            end_time=end_time,
            search=search,
            status=status,
        )


@ns.route("/workflow/<workflow_id>")
class Workflow(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("getWorkflow", description="Gets details of a specific workflow")
    @ns.marshal_with(taskStatus)
    @ns.expect(workflow_get_parser)
    def get(self, workflow_id):
        args = workflow_get_parser.parse_args()
        include_logs = args.get("include_logs")
        include_debug_logs = args.get("include_debug_logs")
        return get_workflow_info(workflow_id, include_logs, include_debug_logs)

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("killTask", description="Kill task")
    def put(self, workflow_id):
        kill_task(workflow_id)


@ns.route("/workflow/<workflow_id>/task/<task_id>/logs")
class TaskLogs(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("getTaskLogs", description="Gets the logs of a specific task")
    @ns.marshal_list_with(log_line_model)
    @ns.expect(task_log_get_parser)
    def get(self, workflow_id, task_id):
        args = task_log_get_parser.parse_args()
        include_debug_logs = args.get("include_debug_logs")
        return get_task_logs(workflow_id, task_id, include_debug_logs)


@ns.route("/errorconditions")
class ErrorConditions(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("listErrorConditions", description="List error conditions")
    @ns.marshal_with(error_logs_pagination_model)
    @ns.expect(errors_list_argparser)
    def get(self):
        args = errors_list_argparser.parse_args()
        search = args.get("search")
        limit = args.get("limit")
        start_after = args.get("start_after")
        last_occurrence_start_time = args.get("last_occurrence_start_time")
        last_occurrence_end_time = args.get("last_occurrence_end_time")
        return pagination_handler(
            ErrorLog.list,
            limit=limit,
            start_after=start_after,
            search=search,
            last_occurrence_start_time=last_occurrence_start_time,
            last_occurrence_end_time=last_occurrence_end_time,
        )

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("deleteErrorConditions", description="Delete all obsolete errors (older than a week)")
    @ns.marshal_with(success_model)
    def delete(self):
        ErrorLog.delete_obsolete_errors()
        return SUCCESS


@ns.route("/errorconditions/<traceback_hash>")
class ErrorCondition(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("getErrorCondition", description="get error condition")
    @ns.marshal_with(error_log_model)
    def get(self, traceback_hash):
        return ErrorLog.get_error_by_traceback_hash(traceback_hash)


@ns.route("/audits")
class Audits(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("listAuditLogs", description="List audit logs")
    @ns.marshal_with(audits_pagination_model)
    @ns.expect(audits_list_argparser)
    def get(self):
        args = audits_list_argparser.parse_args()
        return pagination_handler_for_audits(AuditLog.list(**args), args["limit"])


@ns.route("/audits/<audit_id>")
class Audit(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc("getAuditLog", description="Get audit logs")
    @ns.marshal_with(audit_model)
    def get(self, audit_id):
        return AuditLog.get_by_id(audit_id)


@ns.route("/email-notifications")
class EmailNotifications(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="listGIGNotifications", description="get email notifications")
    @ns.expect(email_notification_get_parser)
    @ns.marshal_list_with(email_notification_model)
    def get(self):
        args = email_notification_get_parser.parse_args()
        issuer_id = str(NotificationIssuer.GIG)
        return get_notifications(issuer_id, **args)

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="createGIGNotification", description="Create email notification")
    @ns.expect(email_notification_model)
    def post(self):
        issuer_id = str(NotificationIssuer.GIG)
        return create_notification(NotificationConfigStruct(ns.payload).to_mongoengine(), issuer_id)


@ns.route("/email-notifications/<notification_id>")
class EmailNotification(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="getGIGNotification", description="Get details of an email notification")
    @ns.marshal_with(email_notification_model)
    def get(self, notification_id):
        return Notifications.get_by_id(notification_id, str(NotificationIssuer.GIG))

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="updateGIGNotification", description="Update email notification")
    @ns.expect(email_notification_model)
    def put(self, notification_id):
        issuer_id = str(NotificationIssuer.GIG)
        return update_notification(issuer_id, notification_id, NotificationConfigStruct(ns.payload).to_mongoengine())

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="deleteGIGNotification", description="Delete email notification")
    def delete(self, notification_id):
        issuer_id = str(NotificationIssuer.GIG)
        return delete_notification(issuer_id, notification_id)


@ns.route("/email-notifications/<notification_id>/send")
class SendEmailNotification(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="sendGIGNotification", description="Send email notification")
    def put(self, notification_id):
        return send_gig_notification(notification_id)


@ns.route("/email-notifications/<notification_id>/send-test")
class SendTestEmailNotification(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.expect(test_email_model)
    @ns.doc(shortcut="sendGIGTestNotification", description="Send test email notification")
    def put(self, notification_id):
        emails = ns.payload["emails"]
        return send_test_gig_notification(notification_id, emails)


@ns.route("/alarm-room")
class AlertRoom(Resource):
    @authenticated
    @requires_admin
    @ns.doc("setCredentials", description="set credentials for alarm room")
    @ns.expect(AlarmRoomStruct.model(api))
    def post(self):
        user_name = ns.payload.get("user_name")
        password = ns.payload.get("password")
        return set_alarm_room_credentials(user_name, password)

    @authenticated
    @requires_admin
    @ns.doc("getUsername", description="get user name")
    def get(self):
        return get_alarm_room_user_name()


@ns.route("/email-outbox")
class NotificationEmailOutbox(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="listEmailOutbox", description="list email outbox")
    @ns.marshal_list_with(NotificationEmailOutboxConfigStruct.model())
    def get(self):
        return EmailOutbox.list()

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="sendEmailOutbox", description="send email outbox")
    def post(self):
        schedule_send_notification_emails()


@ns.route("/email-notifications/<notification_id>/blog")
class CreateNotificationBlog(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="createGigNotificationBlog", description="Create GIG notification blog")
    def post(self, notification_id):
        return create_gig_blog(notification_id)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="unpublishGIGNotification", description="Unpublish email notification")
    def delete(self, notification_id):
        issuer_id = str(NotificationIssuer.GIG)
        return unpublish_blog(notification_id, issuer_id)


@ns.route("/email-notifications/<notification_id>/preview")
class EmailNotificationPreview(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="getGIGNotificationPreview", description="get email notification preview")
    @ns.marshal_with(email_notification_model)
    def get(self, notification_id):
        return get_blog_preview(notification_id, str(NotificationIssuer.GIG))


@ns.route("/issues")
class Issues(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.expect(issue_list_parser)
    @ns.doc(shortcut="listIssues", description="List all issues")
    @ns.marshal_with(issue_list_pagination_model)
    def get(self):
        kwargs = issue_list_parser.parse_args()
        limit = kwargs.pop("limit")
        start_after = kwargs.pop("start_after")
        return pagination_handler(issue_model.list, limit, start_after, **kwargs)

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="deleteIssues", description="Delete all issue")
    @ns.marshal_with(success_model)
    def delete(self):
        issue_model.delete_all_issue()
        return SUCCESS


@ns.route("/issues/<deduplication_id>")
class Issue(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="getIssue", description="Get issue by ID")
    @ns.marshal_with(IssueStruct.model(api))
    def get(self, deduplication_id):
        return issue_model.get_by_did(deduplication_id=deduplication_id).to_mongo().to_dict()

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="deleteIssue", description="Delete issue")
    @ns.marshal_with(success_model)
    def delete(self, deduplication_id):
        issue_model.delete_by_did(deduplication_id=deduplication_id)
        return SUCCESS

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.expect(edit_issue_parser)
    @ns.doc(shortcut="editIssue", description="Edit issue")
    @ns.marshal_with(success_model)
    def put(self, deduplication_id):
        snooze_issue(deduplication_id=deduplication_id, snooze_until=edit_issue_parser.parse_args()["snooze_until"])
        return SUCCESS


@ns.route("/pre-release-features")
class PreReleaseFeatureList(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.expect(pre_release_features_parser)
    @ns.doc(shortcut="listPreReleaseFeatures", description="List all pre-release features")
    @ns.marshal_with(PreReleaseFeatureStruct.list_model(api))
    def get(self):
        args = pre_release_features_parser.parse_args()
        vco = args.get("vco")
        feature = args.get("feature")
        if feature:
            result = list(PreReleaseFeatures.objects(feature=feature))
        elif vco:
            result = PreReleaseFeatures.get_by_vco(vco)
        else:
            result = PreReleaseFeatures.list()
        return {"result": result}

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="createPreReleaseFeature", description="Create a new pre-release feature")
    @ns.expect(create_pre_release_feature_parser)
    @ns.marshal_with(PreReleaseFeatureStruct.model(api))
    def post(self):
        args = create_pre_release_feature_parser.parse_args()
        feature_name = args.get("feature")
        vcos = args.get("vcos") or []
        existing = PreReleaseFeatures.objects(feature=feature_name).first()
        if existing:
            raise ValueError(f"Feature {feature_name} already exists.")
        feature = PreReleaseFeatures(feature=feature_name, vcos=vcos)
        feature.save()
        return feature


@ns.route("/pre-release-features/<feature>")
class PreReleaseFeatureByFeature(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="deleteFeatureByFeature", description="Delete a pre-release feature by its feature name")
    @ns.marshal_with(success_model)
    def delete(self, feature):
        PreReleaseFeatures.delete_by_feature(feature)
        return SUCCESS


@ns.route("/pre-release-features/<feature>/vcos/<vco_id>")
class PreReleaseFeatureVCOOps(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="addVcoToFeature", description="Add a VCO to a pre-release feature")
    @ns.marshal_with(success_model)
    def post(self, feature, vco_id):
        PreReleaseFeatures.add_vco_to_feature(feature=feature, vco=vco_id)
        return SUCCESS

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(shortcut="removeVcoFromFeature", description="Remove a VCO from a pre-release feature")
    @ns.marshal_with(success_model)
    def delete(self, feature, vco_id):
        PreReleaseFeatures.remove_vco_from_feature(feature=feature, vco=vco_id)
        return SUCCESS

# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115

from flask_itsyouonline import authenticated
from flask_restx import Resource

from meneja.api import api
from meneja.business.notifications import list_notification_utilities
from meneja.structs.notification_subscription.dataclasses import NotificationUtilitiesStruct

ns = api.namespace("notifications-utilities", description="Notifications utilities")


@ns.route("/email-notification/utilities")
class EmailNotificationUtilities(Resource):
    @authenticated
    @ns.marshal_with(NotificationUtilitiesStruct.model(api))
    @ns.doc(shortcut="listNotificationUtilities", description="List notification utilities")
    def get(self):
        return list_notification_utilities()

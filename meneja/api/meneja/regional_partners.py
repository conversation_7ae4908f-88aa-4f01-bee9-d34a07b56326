# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115

from os import environ

from flask_itsyouonline import authenticated
from flask_restx import Resource

import meneja.business.regional_partners as rp_business
from meneja.api import api
from meneja.business.auth import requires_admin
from meneja.business.db_model_2_rest import generate_request_parser
from meneja.model.regional_partners import RegionalPartner as RegionalPartnerModel
from meneja.structs.meneja.dataclasses.g8_owners import G8OwnerSimpleStruct
from meneja.structs.meneja.dataclasses.regional_partner import RegionalPartnerSimpleStruct
from meneja.structs.meneja.regional_partners.regional_partner import RegionalPartnerStruct
from meneja.structs.meneja.regional_partners.regional_partner_create import RegionalPartnerCreateStruct
from meneja.structs.meneja.regional_partners.regional_partner_update import RegionalPartnerUpdateStruct

regional_partner_parser = generate_request_parser(RegionalPartnerModel, recursive=True)

ns = api.namespace("regional_partners", description="Regional Partners")


@ns.route("/")
class RegionalPartners(Resource):
    @authenticated
    @ns.doc(shortcut="listRegionalPartners", description="List all Regional Partners")
    @ns.marshal_with(RegionalPartnerSimpleStruct.list_model(api))
    @requires_admin
    def get(self):
        return {"result": rp_business.list_simple_regional_partners()}

    @authenticated
    @ns.expect(RegionalPartnerCreateStruct.model())
    @requires_admin
    @ns.doc(shortcut="createRegionalPartner", description="Create a Regional Partner")
    def post(self):
        return rp_business.create(
            meneja_org=environ["MNJ_IAM_ORGANIZATION"],
            jwt=environ["MNJ_TOKEN"],
            rp_payload=RegionalPartnerStruct(ns.payload).to_mongoengine(),
        )


@ns.route("/<regional_partner_id>")
class RegionalPartner(Resource):
    @authenticated
    @ns.doc(shortcut="getRegionalPartner", description="Get Regional Partner")
    @ns.marshal_with(RegionalPartnerStruct.model())
    @requires_admin
    def get(self, regional_partner_id):
        return RegionalPartnerModel.get_by_id(regional_partner_id)

    @authenticated
    @ns.expect(RegionalPartnerUpdateStruct.model())
    @ns.doc(shortcut="updateRegionalPartner", description="Update Regional Partner")
    @requires_admin
    def put(self, regional_partner_id):
        rp_business.update(regional_partner_id, rp_payload=RegionalPartnerStruct(ns.payload).to_mongoengine())

    @authenticated
    @ns.doc(shortcut="deleteRegionalPartner", description="Delete Regional Partner")
    @requires_admin
    def delete(self, regional_partner_id):
        rp_business.delete_regional_partner(regional_partner_id)


@ns.route("/<regional_partner_id>/cloud-enablers")
class RegionalPartnerCEs(Resource):
    @authenticated
    @ns.doc(shortcut="listRegionalPartnerCEs", description="list the Cloud Enablers connected to a Regional Partner")
    @ns.marshal_with(G8OwnerSimpleStruct.list_model(api))
    @requires_admin
    def get(self, regional_partner_id):
        return {"result": rp_business.list_simple_rp_g8_owners(regional_partner_id)}

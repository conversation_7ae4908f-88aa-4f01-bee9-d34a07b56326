# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115
import logging

from flask import send_file
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields

from meneja.api import api
from meneja.business import g8_config, ssl_certificate
from meneja.business.auth import is_admin, is_developer, requires_admin, requires_custom
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.model.healthchecks import UpdateCertificate
from meneja.model.ssl_certificate import SSLCertificates

logger = logging.getLogger()
ns = api.namespace("certificates", description="G8 deployments")

certificate_chain = api.model(
    "CertificateChain",
    {
        "issuer": fields.String(help_text="certificate issuer"),
        "content": fields.String(help_text="certificate details"),
    },
)

certificates_model = api.model(
    "Certificates", generate_restfull_model_spec(SSLCertificates, exported_only=True, add_id=True)
)

certificate_model = api.model("Certificate", generate_restfull_model_spec(SSLCertificates, add_id=True))
certificate_model["crt"] = fields.List(fields.Nested(certificate_chain), help_text="certificate chain info")
certificate_model.pop("key")

create_edit_certificate_model = api.model(
    "CreateCertificate", generate_restfull_model_spec(SSLCertificates, editable_only=True)
)
failed_certificates_model = api.model("Certificates", {"result": fields.List(fields.Nested(certificates_model))})


@ns.route("/")
class Certificates(Resource):
    @authenticated
    @ns.marshal_list_with(certificates_model)
    @ns.doc(shortcut="listCertificates", description="List certificates")
    def get(self):
        return ssl_certificate.certificate_list()

    @authenticated
    @ns.expect(create_edit_certificate_model)
    @ns.doc(shortcut="addCertificate", description="add new certificate")
    @requires_admin
    def post(self):
        data = ns.payload
        username = get_current_user_info().username
        certificate = ssl_certificate.add(crt=data["crt"], key=data["key"], created_by=username)
        return str(certificate.id), 201  # pylint: disable=no-member


@ns.route("/<certificate_id>")
class Certificate(Resource):
    @authenticated
    @ns.marshal_list_with(certificate_model)
    @ns.doc(shortcut="getCertificateInfo", description="Get certificate info")
    @requires_admin
    def get(self, certificate_id):
        return ssl_certificate.get(certificate_id=certificate_id, parsed=True)

    @authenticated
    @ns.expect(create_edit_certificate_model)
    @ns.doc(shortcut="updateCertificateInfo", description="Update certificate info")
    @requires_admin
    def put(self, certificate_id):
        data = ns.payload
        crt = ssl_certificate.update(certificate_id=certificate_id, crt=data["crt"], key=data["key"])
        g8_config.update_certificate_on_all_g8s(certificate_domain=crt.domain)

    @authenticated
    @ns.doc(shortcut="deleteCertificate", description="Delete certificate")
    @requires_admin
    def delete(self, certificate_id):
        return ssl_certificate.delete(certificate_id=certificate_id)


@ns.route("/<certificate_id>/key/download")
class DownloadPrivateKey(Resource):
    @authenticated
    @ns.doc(shortcut="downloadPrivateKey", description="Download certificate private key")
    @requires_admin
    def get(self, certificate_id):
        buffer_ = ssl_certificate.download_private_key(certificate_id)
        filename = f"{certificate_id}.key"
        return send_file(buffer_, as_attachment=True, attachment_filename=filename)


@ns.route("/vco/failed")
class FailedUpdateVcoCertificates(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.marshal_with(failed_certificates_model)
    @ns.doc(
        shortcut="listFailedToUpdateVcoCertificates",
        description="List vco certificates that failed to update in the last scheduled job",
    )
    def get(self):
        last_job = UpdateCertificate.get_last()
        return {"result": last_job.failed_jobs} if last_job else {"result": []}

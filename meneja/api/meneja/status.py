# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115
import logging

from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse

import meneja.business.auth as auth_manager
from meneja.api import api
from meneja.api.vco import success_model
from meneja.business.auth import (
    alarm_room_authorized,
    get_accessible_g8s,
    is_admin,
    is_developer,
    requires_admin,
    requires_custom,
)
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.business.g8 import get_five_minute_status_of_last_hour, get_hour_status_of_last_twelve_hours
from meneja.business.g8.g8 import update_five_minutes_status_note, update_hour_status_note
from meneja.business.g8_monitor import monitor_cron_job
from meneja.business.g8_status import action_on_failed_healthchecks, get_failed_healthchecks, get_g8_status_history
from meneja.lib.clients.g8.lib.rest import ApiException
from meneja.lib.connection import MongoConnection
from meneja.lib.enumeration import FailedHealthCheckAction, G8StatusColour
from meneja.lib.fixes import FixedArgument
from meneja.lib.itsyouonline import AccessDeniedWithoutScope
from meneja.model.g8 import G8CapacityConsumption, G8FiveMinuteStatus, G8Info, PerformanceStat
from meneja.structs.meneja.dataclasses.failed_health_checks import FailedHealthCheck

logger = logging.getLogger()
MongoConnection.get_client()

ns = api.namespace("status", description="G8 status information")

status_item = api.model(
    "StatusItem",
    {
        "id": fields.String(readOnly=True, description="Status ID"),
        "g8_name": fields.String(readOnly=True, description="Globally unique G8 identifier"),
        "status": fields.String(readOnly=True, description="G8 status color", enum=G8StatusColour.values()),
        "note": fields.String(readOnly=True, description="Note for the status when the G8 in error state"),
    },
)
status = api.model(
    "Status",
    {
        "scheduled_at": fields.Float(readOnly=True, description="Timestamp when the status was calculated"),
        "requested_by": fields.String(
            readOnly=True, description="Meneja server location from where the status was calculated"
        ),
        "g8s": fields.List(
            fields.Nested(status_item), readOnly=True, description="Statuses of the g8's at the requested_at timestamp"
        ),
    },
)

status_note_parser = reqparse.RequestParser(argument_class=FixedArgument)
status_note_parser.add_argument("note", type=str, default="", location="args", help="Description of the note")

failed_health_check_actions_parser = reqparse.RequestParser(argument_class=FixedArgument)
failed_health_check_actions_parser.add_argument("message_uid", type=str, location="args", help="Message")
failed_health_check_actions_parser.add_argument("node_id", type=int, location="args", help="Node id")
failed_health_check_actions_parser.add_argument("g8_name", type=str, location="args", help="G8 name")
mute_health_check_parser = reqparse.RequestParser(argument_class=FixedArgument)
mute_health_check_parser.add_argument("severity", type=str, required=True, help="Severity to be muted", location="json")
mute_health_check_parser.add_argument("description", type=str, required=False, help="Description", location="json")
mute_health_check_parser.add_argument("summary", type=str, required=False, help="Summary", location="json")


@ns.route("/five-minute")
class FiveMinStatus(Resource):
    @authenticated
    @ns.marshal_list_with(status)
    @ns.doc(shortcut="getFiveMinuteStatus", description="Gets the six minute status records for the last hour")
    def get(self):
        accessible_g8s = get_accessible_g8s()
        return get_five_minute_status_of_last_hour(accessible_g8s)


@ns.route("/latest-status")
class LatestStatus(Resource):
    @alarm_room_authorized
    @ns.marshal_with(status)
    @ns.doc(shortcut="latestStatus", description="Gets the latest five minute status record")
    def get(self):
        return G8FiveMinuteStatus.get_last()


@ns.route("/hour")
class HourStatus(Resource):
    @authenticated
    @ns.marshal_list_with(status)
    @ns.doc(shortcut="getHourStatus", description="Gets the hour status records for the last 12 hours")
    def get(self):
        accessible_g8s = get_accessible_g8s()
        return get_hour_status_of_last_twelve_hours(accessible_g8s)


@ns.route("/hour/<status_id>/status-note")
class HourStatusNote(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(success_model)
    @ns.expect(status_note_parser)
    @ns.doc(
        shortcut="updateHourStatusNote",
        description="Update the description or issue url of G8 hour status note in error state",
    )
    def post(self, status_id):
        args = status_note_parser.parse_args()
        note = args.get("note")
        return {"success": update_hour_status_note(status_id, note)}


@ns.route("/five-minute/<status_id>/status-note")
class FiveMinStatusNote(Resource):
    @authenticated
    @requires_admin
    @ns.marshal_with(success_model)
    @ns.expect(status_note_parser)
    @ns.doc(
        shortcut="updateFiveMinuteStatusNote",
        description="Update the description or issue url of G8 five minute status note in error state",
    )
    def post(self, status_id):
        args = status_note_parser.parse_args()
        note = args.get("note")
        return {"success": update_five_minutes_status_note(status_id, note)}


capacity_overview_model = api.model(
    "G8CapacityOverview",
    {
        "g8_name": fields.String(description="G8 name"),
        "capacity": fields.Nested(
            api.model("G8Consumption", generate_restfull_model_spec(G8CapacityConsumption, api=api))
        ),
    },
)


@ns.route("/capacity")
class CapacityStatus(Resource):
    @authenticated
    @ns.marshal_list_with(capacity_overview_model)
    @ns.doc(shortcut="getCapacityStatus", description="Gets current capacity consumption of G8s")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        capacities = [
            {"g8_name": g8_name, "capacity": G8Info.get_g8_capacity(g8_name=g8_name).to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]
        capacities.sort(key=lambda k: k["capacity"]["index"], reverse=True)
        for capacity in capacities:
            capacity["capacity"]["backends"].sort(
                key=lambda k: k["capacity"]["used"] / k["capacity"]["total"] if k["capacity"]["total"] else 0,
                reverse=True,
            )
        return capacities


performance_stat_model = generate_restfull_model_spec(PerformanceStat, api=api)
g8_stat_model = api.model(
    "G8CpuUsage",
    {
        "g8_name": fields.String(description="G8 name"),
        "usage": fields.Nested(api.model("CpuUsage", performance_stat_model)),
    },
)


@ns.route("/cpu-usage")
class CpuUsage(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getCpuUsage", description="Gets current g8 cpu usage")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).cpu.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/disks-iops")
class G8DiskIOPS(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getDiskIops", description="Get IOPS of the disks on this G8")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).iops.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/access-network-bandwith")
class AccessNetworkBandwith(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getAccessNetworkBandwith", description="Get Access Network bandwith stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {
                "g8_name": g8_name,
                "usage": G8Info.get_g8_stats(g8_name=g8_name).access_network_bandwith.to_mongo().to_dict(),
            }
            for g8_name in accessible_g8s
        ]


@ns.route("/access-network-packets")
class AccessNetworkPackets(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getAccessNetworkPackets", description="Get Access Network Packets per second stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {
                "g8_name": g8_name,
                "usage": G8Info.get_g8_stats(g8_name=g8_name).access_network_packets.to_mongo().to_dict(),
            }
            for g8_name in accessible_g8s
        ]


@ns.route("/management-network-bandwith")
class ManagementNetworkBandwith(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getManagementNetworkBandwith", description="Get Management Network bandwith stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {
                "g8_name": g8_name,
                "usage": G8Info.get_g8_stats(g8_name=g8_name).management_network_bandwith.to_mongo().to_dict(),
            }
            for g8_name in accessible_g8s
        ]


@ns.route("/management-network-packets")
class ManagementNetworkPackets(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getManagementNetworkPackets", description="Get Management Network packets per second stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {
                "g8_name": g8_name,
                "usage": G8Info.get_g8_stats(g8_name=g8_name).management_network_packets.to_mongo().to_dict(),
            }
            for g8_name in accessible_g8s
        ]


@ns.route("/nvme-temperature")
class NvmeTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getNvmeTemperature", description="Get NVME Temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).nvme_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/cpu-temperature")
class CpuTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getCpuTemperature", description="Get CPU Temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).cpu_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/inlet-temperature")
class InletTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getInletTemperature", description="Get inlet temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).inlet_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/memory-temperature")
class MemoryTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getMemoryTemperature", description="Get Memory temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).memory_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/nic-temperature")
class NicTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getNicTemperature", description="Get NIC temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).nic_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


@ns.route("/hdd-temperature")
class HddTemperature(Resource):
    @authenticated
    @ns.marshal_list_with(g8_stat_model)
    @ns.doc(shortcut="getHddTemperature", description="Get HDD Temperature evolution stats")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return [
            {"g8_name": g8_name, "usage": G8Info.get_g8_stats(g8_name=g8_name).hdd_temperature.to_mongo().to_dict()}
            for g8_name in accessible_g8s
        ]


get_status_history_parser = reqparse.RequestParser(argument_class=FixedArgument)
get_status_history_parser.add_argument("start", type=int, location="args", help="Start timestamp")
get_status_history_parser.add_argument("end", type=int, location="args", help="End timestamp")

output_status_history_model = api.model(
    "StatusHistory",
    {
        "g8_name": fields.String(description="G8 name"),
        "statuses": fields.List(
            fields.Nested(
                api.model(
                    "StatusOccurrence",
                    {
                        "status": fields.String(description="Status"),
                        "percentage": fields.Float(description="Occurrence percentage of this status"),
                    },
                )
            )
        ),
    },
)


@ns.route("/status-history")
class StatusHistory(Resource):
    @authenticated
    @ns.marshal_list_with(output_status_history_model)
    @ns.expect(get_status_history_parser)
    @ns.doc(shortcut="getStatusHistory", description="Get G8 status history")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return get_g8_status_history(g8_names=accessible_g8s, **get_status_history_parser.parse_args())


@ns.route("/failed-healthchecks")
class FailedHealthChecks(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.marshal_with(FailedHealthCheck.list_model(api))
    @ns.doc(shortcut="getG8FailedHealthChecks", description="Get G8 failed healthchecks")
    def get(self):
        accessible_g8s = get_accessible_g8s(include_inactive=False)
        return {"result": get_failed_healthchecks(accessible_g8s)}

    @authenticated
    @requires_admin
    @ns.doc(shortcut="refreshG8FailedHealthChecks", description="refresh G8 failed healthchecks")
    def put(self):
        monitor_cron_job()


@ns.route("/failed-healthchecks/mute")
class MuteFailedHealthChecks(Resource):
    @ns.expect(failed_health_check_actions_parser, mute_health_check_parser)
    @requires_admin
    @authenticated
    @ns.doc(shortcut="muteG8FailedHealthCheck", description="mute G8 failed healthchecks")
    def put(self):
        jwt = get_current_user_info().jwt
        failed_healthcheck_args = failed_health_check_actions_parser.parse_args()
        payload = mute_health_check_parser.parse_args()
        try:
            action_on_failed_healthchecks(
                jwt,
                payload=payload,
                **failed_healthcheck_args,
                action=FailedHealthCheckAction.MUTE,
            )
        except ApiException as exp:
            if exp.status in (403, 401):
                scope = f"user:memberof:greenitglobe.environments.{failed_healthcheck_args['g8_name']}.admin"
                if not auth_manager.has_scope(scope):
                    raise AccessDeniedWithoutScope("No admin scope", scope) from exp

            raise ValueError(f"Error from G8 {exp.body}") from exp


@ns.route("/failed-healthchecks/unmute")
class UnMuteFailedHealthChecks(Resource):
    @ns.expect(failed_health_check_actions_parser)
    @authenticated
    @requires_admin
    @ns.doc(shortcut="unmuteG8FailedHealthCheck", description="unmute G8 failed healthchecks")
    def put(self):
        jwt = get_current_user_info().jwt
        failed_healthcheck_args = failed_health_check_actions_parser.parse_args()
        try:
            action_on_failed_healthchecks(
                jwt,
                **failed_healthcheck_args,
                action=FailedHealthCheckAction.UNMUTE,
            )
        except ApiException as exp:
            if exp.status in (403, 401):
                scope = f"user:memberof:greenitglobe.environments.{failed_healthcheck_args['g8_name']}.admin"
                if not auth_manager.has_scope(scope):
                    raise AccessDeniedWithoutScope("No admin scope", scope) from exp

            raise ValueError(f"Error from G8 {exp.body}") from exp


@ns.route("/failed-healthchecks/rerun")
class RerunFailedHealthChecks(Resource):
    @ns.expect(failed_health_check_actions_parser)
    @authenticated
    @requires_admin
    @ns.doc(shortcut="rerunG8FailedHealthCheck", description="rerun G8 failed healthchecks")
    def put(self):
        jwt = get_current_user_info().jwt
        failed_healthcheck_args = failed_health_check_actions_parser.parse_args()
        try:
            action_on_failed_healthchecks(
                jwt,
                **failed_healthcheck_args,
                action=FailedHealthCheckAction.RERUN,
            )
        except ApiException as exp:
            if exp.status in (403, 401):
                scope = f"user:memberof:greenitglobe.environments.{failed_healthcheck_args['g8_name']}.admin"
                if not auth_manager.has_scope(scope):
                    raise AccessDeniedWithoutScope("No admin scope", scope) from exp
            raise ValueError(f"Error from G8 {exp.body}") from exp

# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115
from flask_itsyouonline import authenticated
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from werkzeug import exceptions

import meneja.business.license_compliance_reports as license_compliance_reports
from meneja.api import api
from meneja.api.meneja import SUCCESS
from meneja.api.vco import success_model
from meneja.business.auth import (
    get_g8owner_id,
    is_admin,
    is_billing_member,
    is_developer,
    is_g8_owner,
    is_specific_g8_owner,
    is_vco_g8_owner,
    requires_admin,
    requires_custom,
    requires_developer,
)
from meneja.business.db_model_2_rest import generate_request_parser
from meneja.business.translations_reporting import (
    delete_suggestion,
    delete_suggestion_before_timestamp,
    get_suggestion,
    list_suggestions,
    update_suggestion_status,
)
from meneja.business.vco import (
    create_vco,
    delete_vco,
    disable_vco,
    enable_vco,
    get_vco,
    invite_member_to_vco_org,
    list_organization_users,
    list_vco_locations,
    move_vco_to_cloud_enabler,
    revoke_member_from_vco_org,
    trigger_utility_rebuild,
    update_vco,
)
from meneja.lib.enumeration import AvailableLanguages, TranslationReportStatus
from meneja.lib.fixes import FixedArgument
from meneja.lib.utils import geocode_address
from meneja.model.vco import VCO as VCOmodel
from meneja.structs.meneja.dataclasses.vco import VCOGetStruct
from meneja.structs.vco.dataclasses.translation_suggestion import TranslationSuggestionStruct

ns = api.namespace("vco", description="Virtual Cloud Operator")
vco_list_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_list_parser.add_argument("g8owner_id", type=str, required=False, location="args", help="g8owner_id")
vco_create_parser = generate_request_parser(VCOmodel, recursive=True, creatable_only=True)
vco_create_parser.add_argument(
    "onboarding_session_key", type=str, required=False, default="", location="args", help="The onboarding session key"
)
vco_create_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
vco_create_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
vco_create_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
vco_create_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
vco_create_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
vco_create_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
vco_create_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)
vco_edit_parser = generate_request_parser(VCOmodel, recursive=True, editable_only=True)
vco_edit_parser.replace_argument("billable", type=boolean, required=False, location="args", help="VCO is billable")
vco_edit_parser.remove_argument("customers_show_prices")
vco_edit_parser.add_argument("billing_information_address", type=str, location="args", help="Billing address")
vco_edit_parser.add_argument(
    "billing_information_contact_name", type=str, location="args", help="Billing contact person"
)
vco_edit_parser.add_argument(
    "billing_information_email", type=str, location="args", help="Email address for billing messages"
)
vco_edit_parser.add_argument("billing_information_phone", type=str, location="args", help="Billing phone")
vco_edit_parser.add_argument("billing_information_vat", type=str, location="args", help="VAT number")
vco_edit_parser.add_argument(
    "billing_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
vco_edit_parser.add_argument(
    "company_coordinates_lat", type=float, location="args", required=False, default=0, help="Latitude"
)
vco_edit_parser.add_argument(
    "billing_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)
vco_edit_parser.add_argument(
    "company_coordinates_long", type=float, location="args", required=False, default=0, help="Longitude"
)
vco_simple_model = api.model(
    "VCOSimple",
    {
        "vco_id": fields.String(description="VCO identifier"),
        "company_name": fields.String(description="VCO Company Name", attribute="company_information.name"),
        "domain": fields.String(description="VCO portal domain"),
        "contact_name": fields.String(description="Contact name", attribute="company_information.contact.name"),
        "g8owner_id": fields.String(description="ID of G8 Owner of this VCO"),
        "status": fields.String(description="VCO status"),
    },
)
vco_compliance_series_model = api.model(
    "VCOComplianceSeries",
    {
        "timeseries": fields.List(fields.Integer("epoch timestamps corresponding to the count series")),
        "count": fields.List(
            fields.Integer("count of license issues recorded over time. corresponds to timestamps in timeseries list")
        ),
    },
)
user_type_parser = reqparse.RequestParser(argument_class=FixedArgument)
user_type_parser.add_argument(
    "is_owner", type=boolean, required=False, default=False, location="args", help="IAM org user is Owner"
)

vcoCreateResult = ns.model(
    "VCOCreateResult",
    {
        "workflow": fields.String,
    },
)

geocode_parser = reqparse.RequestParser()
geocode_parser.add_argument("address", type=str, location="args", required=True, help="Address query")

vco_locations_model = api.model(
    "VCOLocations",
    {
        "result": fields.List(
            fields.Nested(api.model("VCOLocation", {"location": fields.String(help_text="Location name")}))
        )
    },
)


list_translations_parser = reqparse.RequestParser()
list_translations_parser.add_argument(
    "language", choices=AvailableLanguages.values(), type=str, location="args", default=None, help="filter by language"
)
list_translations_parser.add_argument(
    "status", choices=TranslationReportStatus.values(), default=None, type=str, location="args", help="filter by status"
)

update_translation_suggestion_status_parser = reqparse.RequestParser()
update_translation_suggestion_status_parser.add_argument(
    "status", type=str, location="args", required=True, choices=TranslationReportStatus.values(), help="status"
)

delete_suggested_translation_before_timestamp = reqparse.RequestParser()
delete_suggested_translation_before_timestamp.add_argument(
    "timestamp", type=float, location="args", required=True, help="timestamp"
)


@ns.route("/")
class VCOs(Resource):
    """VCO management"""

    @authenticated
    @requires_custom(is_admin, is_g8_owner, is_billing_member)
    @ns.doc(shortcut="listVCOS", description="List VCO objects")
    @ns.marshal_list_with(vco_simple_model)
    def get(self):
        """Get vco list
        Returns:
            VCOmodel: VCO list
        """
        only = ["company_information__name", "domain", "company_information__contact__name", "g8owner_id", "status"]
        kwargs = vco_list_parser.parse_args()
        g8owner_id = kwargs.get("g8owner_id")
        if g8owner_id and not (is_admin() or is_specific_g8_owner(g8owner_id)):
            raise exceptions.Forbidden("User doesn't have access to the G8 Owner")
        if g8owner_id:
            return VCOmodel.list(g8owner_id=g8owner_id, only=only)
        else:
            return VCOmodel.list(only=only)

    @authenticated
    @requires_admin
    @ns.doc(shortcut="createVCO", description="Create VCO object")
    @ns.expect(vco_create_parser)
    @ns.marshal_with(vcoCreateResult)
    def post(self):
        """Create VCO
        Returns:
            dict: task id
        """
        kwargs = vco_create_parser.parse_args()
        g8owner_id = kwargs.get("g8owner_id")
        if g8owner_id and not (is_admin() or is_specific_g8_owner(g8owner_id)):
            raise exceptions.Forbidden("User doesn't have access to the G8 Owner")
        if not g8owner_id:
            g8owner_id = get_g8owner_id()
        return dict(workflow=create_vco(**kwargs))


@ns.route("/<vco_id>")
class VCO(Resource):
    """Specific VCO management"""

    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner, is_billing_member)
    @ns.doc(shortcut="getVCO", description="Get VCO object")
    @ns.marshal_with(VCOGetStruct.model(api))
    def get(self, vco_id):
        """Get VCO details

        Args:
            vco_id (str): VCO ID

        Returns:
            VCO: VCO details
        """
        return get_vco(vco_id)

    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner, is_billing_member)
    @ns.doc(shortcut="updateVCO", description="Update VCO object")
    @ns.expect(vco_edit_parser)
    def put(self, vco_id):
        """Update VCO details

        Args:
            vco_id (str): VCO ID
        """
        update_vco(vco_id, **vco_edit_parser.parse_args())

    @authenticated
    @requires_admin
    @ns.doc(shortcut="deleteVCO", description="Delete VCO")
    def delete(self, vco_id):
        """Delete VCO

        Args:
            vco_id (str): VCO ID
        """
        delete_vco(vco_id)


@ns.route("/<vco_id>/enable")
class EnableVCO(Resource):
    """Enable VCO"""

    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="enableVCO", description="Enable VCO")
    def put(self, vco_id):
        """Enable VCO"""
        return enable_vco(vco_id)


@ns.route("/<vco_id>/disable")
class DisableVCO(Resource):
    """Disable VCO"""

    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="disableVCO", description="Disable VCO")
    def put(self, vco_id):
        """Disable VCO"""
        return disable_vco(vco_id)


@ns.route("/<vco_id>/users")
class VCOUsers(Resource):
    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="listVCOUsers", description="List members of VCO org")
    def get(self, vco_id):
        return list_organization_users(vco_id)


@ns.route("/<vco_id>/users/<username>")
class VCOUser(Resource):
    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="inviteVCOUser", description="Invite user to the VCO org")
    @ns.expect(user_type_parser)
    def post(self, vco_id, username):
        is_owner = user_type_parser.parse_args()["is_owner"]
        invite_member_to_vco_org(vco_id, username, is_owner)

    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="revokeVCOUserAccess", description="Revoke user access")
    @ns.expect(user_type_parser)
    def delete(self, vco_id, username):
        is_owner = user_type_parser.parse_args()["is_owner"]
        revoke_member_from_vco_org(vco_id, username, is_owner)


@ns.route("/<vco_id>/locations")
class VCOLocations(Resource):
    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(shortcut="listVCOLocations", description="List locations available for the VCO")
    @ns.marshal_with(vco_locations_model)
    def get(self, vco_id):
        return dict(result=list_vco_locations(vco_id))


@ns.route("/<vco_id>/statistics/license-compliance")
class VCOComplianceReport(Resource):
    @authenticated
    @requires_custom(is_admin, is_vco_g8_owner)
    @ns.doc(
        shortcut="getVCOComplianceSeries",
        description="Get the information about noncompliant virtual machines over time for this VCO",
    )
    @ns.marshal_with(vco_compliance_series_model)
    def get(self, vco_id):
        return license_compliance_reports.get_vco_compliance_series(vco_id)


@ns.route("/<vco_id>/g8owner/<g8owner_id>")
class VCOG8Owner(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="updateVCOG8Owner", description="Update VCO G8 owner")
    def put(self, vco_id, g8owner_id):
        move_vco_to_cloud_enabler(vco_id, g8owner_id)
        return SUCCESS


@ns.route("/geocode")
class Geocoder(Resource):
    @authenticated
    @ns.hide
    @ns.expect(geocode_parser)
    @ns.doc(
        shortcut="geoCodeAddress",
        description="Geocode address",
    )
    def get(self):
        args = geocode_parser.parse_args()
        address = args.get("address")
        return geocode_address(address=address)["geometry"]["location"]


@ns.route("/<vco_id>/utility-rebuild/<utility_type>")
class VCOUtilityRebuild(Resource):
    @authenticated
    @requires_admin
    @ns.doc(shortcut="triggerVCOUtilityRebuild", description="Update VCO G8 owner")
    def put(self, vco_id: str, utility_type: str):
        trigger_utility_rebuild(vco_id, utility_type)
        return True, 200


@ns.route("/translation_suggestion", doc=False)
class TranslationSuggestions(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.marshal_with(TranslationSuggestionStruct.list_model(api))
    @ns.expect(list_translations_parser)
    @ns.doc(
        shortcut="listTranslationSuggestions",
        description="List translation suggestions",
    )
    def get(self):
        args = list_translations_parser.parse_args()
        language = args.get("language")
        status = args.get("status")
        return {"result": list_suggestions(language=language, report_status=status)}

    @authenticated
    @requires_developer
    @ns.expect(delete_suggested_translation_before_timestamp)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="deleteSuggestionBeforeTimestamp",
        description="Delete translation suggestion before specific timestamp",
    )
    def delete(self):
        timestamp = delete_suggested_translation_before_timestamp.parse_args()["timestamp"]
        delete_suggestion_before_timestamp(timestamp=timestamp)
        return SUCCESS


@ns.route("/translation_suggestion/<suggestion_id>", doc=False)
class TranslationSuggestion(Resource):
    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.marshal_with(TranslationSuggestionStruct.model(api))
    @ns.doc(
        shortcut="getTranslationSuggestion",
        description="Get a translation suggestion",
    )
    def get(self, suggestion_id):
        return get_suggestion(suggestion_id=suggestion_id)

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.doc(
        shortcut="updateSuggestionStatus",
        description="Update translation suggestion status",
    )
    @ns.expect(update_translation_suggestion_status_parser)
    @ns.marshal_with(success_model)
    def put(self, suggestion_id):
        args = update_translation_suggestion_status_parser.parse_args()
        update_suggestion_status(suggestion_id=suggestion_id, status=args["status"])
        return SUCCESS

    @authenticated
    @requires_custom(is_admin, is_developer)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="deleteSuggestion",
        description="Delete translation suggestion",
    )
    def delete(self, suggestion_id):
        delete_suggestion(suggestion_id=suggestion_id)
        return SUCCESS

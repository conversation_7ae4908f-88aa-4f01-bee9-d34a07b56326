# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115, W0613

from flask_itsyouonline import authenticated
from flask_restx import Resource, reqparse

from meneja.api import api
from meneja.business.auth import requires_g8_owner, requires_specific_g8_owner
from meneja.business.vco_on_boarding import create_vco_onboarding, list_vco_customers_of_g8, validate_vco_onboarding
from meneja.lib.enumeration import EnvironmentName
from meneja.lib.fixes import FixedArgument
from meneja.structs.vco.dataclasses.vco_onboarding import G8VCOCustomers, VcoOnboardingStruct

is_not_prd = EnvironmentName.current() != EnvironmentName.PRD

ns = api.namespace("vco-hidden", description="VCO hidden apis")

vco_dns_post_parser = reqparse.RequestParser(argument_class=FixedArgument)
vco_dns_post_parser.add_argument("portal_domain", type=str, location="args", help="VCO portal domain")
vco_dns_post_parser.add_argument("iam_domain", type=str, location="args", help="IAM portal domain")
vco_dns_post_parser.add_argument("support_email_address", type=str, location="args", help="VCO support email")
vco_dns_post_parser.add_argument("iam_email_address", type=str, location="args", help="IAM email")
vco_dns_post_parser.add_argument("top_level_domain", type=str, location="args", help="Top level domain")
list_vco_customers_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_vco_customers_parser.add_argument("g8_name", type=str, location="args", help="G8 name")


@ns.route("/dns-check/<session_key>", doc=None if is_not_prd else False)
class VcoDnsCheck(Resource):
    @authenticated
    @requires_g8_owner
    @ns.marshal_with(VcoOnboardingStruct.model(api))
    @ns.response(404, "No VCO onboarding exists with this session key")
    def get(self, session_key):
        return validate_vco_onboarding(session_key)


@ns.route("/dns-check/", doc=None if is_not_prd else False)
class VcoOnboarding(Resource):
    @authenticated
    @requires_g8_owner
    @ns.marshal_with(VcoOnboardingStruct.model(api))
    @ns.expect(vco_dns_post_parser)
    def post(self):
        return create_vco_onboarding(**vco_dns_post_parser.parse_args())


@ns.route("/g8owners/<g8owner_id>/vcos/<vco_id>/customers", doc=None if is_not_prd else False)
class G8Customers(Resource):
    @authenticated
    @requires_specific_g8_owner
    @ns.expect(list_vco_customers_parser)
    @ns.doc(shortcut="listVCOCustomersOfG8", description="List VCO customers of specific location")
    @ns.marshal_with(G8VCOCustomers.model(api))
    def get(self, g8owner_id, vco_id):
        args = list_vco_customers_parser.parse_args()
        return list_vco_customers_of_g8(args["g8_name"], vco_id)

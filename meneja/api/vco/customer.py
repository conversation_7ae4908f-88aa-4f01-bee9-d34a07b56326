# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115, W0613

import logging

from dacite import from_dict
from flask import g as flask_g
from flask.helpers import make_response
from flask_itsyouonline import authenticated, get_current_user_info
from flask_restx import Resource, fields, reqparse
from flask_restx.inputs import boolean
from flask_restx.marshalling import marshal

import meneja.business.license_compliance_reports as license_compliance_reports
import meneja.business.vco.customer as customer_business
from meneja.api import api
from meneja.api.meneja import audit_model, audits_list_argparser, audits_pagination_model
from meneja.api.vco import SUCCESS, alpha_ns
from meneja.api.vco import customer_ns as ns
from meneja.api.vco import get_vco_id, success_model
from meneja.api.vco.cloudspaces import resources_domain_parser as dns_record_parser
from meneja.api.vco.management import backup_location_parser, backup_parser, list_backups_parser, sync_backup_parser
from meneja.business import backups as backup_business
from meneja.business import invoice_config_v2 as inv_cfg
from meneja.business import vdi as vdi_business
from meneja.business.auth import (
    has_access_to_customer,
    is_customer_org_admin,
    is_vco_admin,
    requires_custom,
    requires_vco_admin,
)
from meneja.business.billing_v2 import (
    add_customer_card,
    delete_customer_card,
    get_customer_card,
    get_invoice_pdf,
    list_customer_cards,
    set_customer_card_as_default,
)
from meneja.business.db_model_2_rest import generate_restfull_model_spec
from meneja.business.g8.g8_api import G8Client
from meneja.business.payment_terms import get_payment_terms, set_payment_terms
from meneja.business.vco import get_vco_resource_quotas, set_vco_quotas
from meneja.business.vco.customer import (
    approve_self_registered_customer,
    customer_account,
    delete_customer_dns_record,
    get_customer_resource_quotas,
    set_customer_quotas,
)
from meneja.business.vco.customer.roles import (
    add_support_organization,
    check_support_status,
    create_role,
    delete_role,
    delete_support_organization,
    get_role,
    get_role_grants,
    grant_revoke_role,
    list_roles,
    list_roles_on_customer,
    list_roles_on_location,
    update_role,
)
from meneja.business.vco.customer.service_accounts import (
    add_role_to_service_account,
    create_service_account,
    delete_service_account,
    get_service_account,
    get_service_account_jwt,
    get_service_accounts_by_role,
    list_service_accounts,
    remove_role_from_service_account,
    update_service_account,
)
from meneja.business.vco.dns import get_ds_key
from meneja.lib.enumeration import BillingRole, CloudResourceType
from meneja.lib.enumeration import Currencies as CurrenciesEnum
from meneja.lib.enumeration import (
    DiskTypes,
    EnvironmentName,
    InvoiceStatus,
    RaidLevel,
    RDPStatus,
    ServiceAccountResourceType,
    SupportedDNSRecordType,
)
from meneja.lib.fixes import FixedArgument
from meneja.lib.itsyouonline import ItsyouOnlineClient
from meneja.lib.meneja_g8_mapping import gen_arg_parser, generate_api_response_model
from meneja.lib.pagination import get_pagination_model, pagination_handler, pagination_handler_for_audits
from meneja.lib.utils import from_dict_with_enum, geocode_address
from meneja.model.audit import AuditLog
from meneja.model.backups import Policies, Targets
from meneja.model.billing_organization import BillingOrganization
from meneja.model.datacenters import Datacenter
from meneja.model.invoice_v2 import Invoice
from meneja.model.licensing_compliance import NonCompliantCloudSpace
from meneja.model.vco.customer import Customer as CustomerModel
from meneja.model.vco.software_licenses import VMLicense
from meneja.model.vco.vdi import VDIProfile, VDISession
from meneja.payment import saved_card_fields_mapper, saved_cards_fields_mapper
from meneja.structs.dataclasses.payment_terms import PaymentTermsStruct
from meneja.structs.dataclasses.pricing import (
    AddingCustomerLocationStruct,
    G8PricingWithCurrencyStruct,
    PricingStruct,
    PricingWithLicensesStruct,
)
from meneja.structs.meneja.dataclasses.dns import DnsDSRecord
from meneja.structs.meneja.dataclasses.logs import TransitionLogsStruct
from meneja.structs.software_licenses.v_m_software_license import VMSoftwareLicenseStruct
from meneja.structs.vco.customer.customer_location import CustomerLocationStruct
from meneja.structs.vco.customer.customer_role import CustomerRoleStruct
from meneja.structs.vco.customer.customer_role_create import CustomerRoleCreateStruct
from meneja.structs.vco.customer.customer_role_full import CustomerRoleFullStruct
from meneja.structs.vco.customer.microsoft_license_subscription import MicrosoftLicenseSubscriptionStruct
from meneja.structs.vco.dataclasses.alert_notification_contact_info import AlertNotificationContactStruct
from meneja.structs.vco.dataclasses.backup_workers import BackupWorkerGetStruct, BackupWorkerListStruct
from meneja.structs.vco.dataclasses.consumption import CustomerLocationConsumptionSeriesStruct
from meneja.structs.vco.dataclasses.customer import (
    CustomerBaseStruct,
    CustomerCreateStruct,
    CustomerSelfCreateStruct,
    CustomerUpdateStruct,
)
from meneja.structs.vco.dataclasses.customer_location_resources import CustomerLocationConsumptionResources
from meneja.structs.vco.dataclasses.customer_simple import CustomerSimpleStruct
from meneja.structs.vco.dataclasses.domain_ownership_code import DomainOwnershipCodeStruct
from meneja.structs.vco.dataclasses.invoices import InvoiceStruct
from meneja.structs.vco.dataclasses.location import CustomerLocationInfoStruct
from meneja.structs.vco.dataclasses.resource_domain import TopLevelDomainStruct
from meneja.structs.vco.dataclasses.resource_quotas_with_message import (
    LocationsQuotasWithErrorMessageStruct,
    VcoUpdateLocationsQuotasWithErrorMessageStruct,
)
from meneja.structs.vco.dataclasses.resources_dns_record import CustomerResourceDnsRecordStruct
from meneja.structs.vco.dataclasses.service_account import (
    ServiceAccountCreateStruct,
    ServiceAccountStruct,
    ServiceAccountUpdateStruct,
)
from meneja.structs.vco.dataclasses.show_prices import ShowPricesStruct
from meneja.structs.vco.dataclasses.subscription import CustomerSubscriptionWebhook
from meneja.structs.vco.dataclasses.vco import (
    BackupCreateTargetStruct,
    BackupFullStruct,
    BackupPoliciesStruct,
    BackupPolicyListStruct,
    BackupPolicyStruct,
    BackupStruct,
    BackupTargetsStruct,
    BackupTargetStruct,
    BackupWithVMStruct,
    CustomerComplianceStruct,
    IdModelStruct,
    LicenseIncompliancyForwardingStruct,
    LocationPolicyStruct,
    LocationTargetStruct,
    VCOCachedDetails,
    VmFromBackupStruct,
)
from meneja.structs.vco.dataclasses.vdi import (
    ListVDISessionsStruct,
    VDIProfileCreateStruct,
    VDIProfileStruct,
    VDIProfileUpdateStruct,
    VDISessionCreateStruct,
    VDISessionStatusStruct,
    VDISessionStruct,
    VDIStandbyTransitionLogs,
)

logger = logging.getLogger()

vm_detailed_pricing = api.model(
    "UnitPrices",
    {
        "mu": fields.Float(readOnly=True, description="Price per Memory Unit"),
        "vcu": fields.Float(readOnly=True, description="Price per VCU"),
        "su": fields.Float(readOnly=True, description="Price per Storage Unit"),
        "tu": fields.Float(readOnly=True, description="Price per Transaction Unit"),
        "wu": fields.Float(readOnly=True, description="Price per Windows License"),
        "fsu": fields.Float(readOnly=True, description="Price per Flash Storage Unit"),
    },
)
vm_price_spec = api.model(
    "VMPrice",
    {
        "currency": fields.String(readOnly=True, description="Curency"),
        "price": fields.Float(readOnly=True, description="Price per month"),
        "unit_prices": fields.Nested(vm_detailed_pricing, readOnly=True, description="Unit price per month"),
    },
)

add_customer_location_parser = reqparse.RequestParser(argument_class=FixedArgument)
add_customer_location_parser.add_argument("location", type=str, location="args", required=True, help="Location name")
user_type_parser = reqparse.RequestParser(argument_class=FixedArgument)
user_type_parser.add_argument(
    "is_owner",
    type=boolean,
    required=False,
    default=False,
    location="args",
    help="IAM org user is Owner",
)
customer_simple_model = api.model(
    "CustomerSimple",
    {
        "customer_id": fields.String(description="Customer ID", required=True),
        "name": fields.String(description="Customer company name", required=True, attribute="company_information.name"),
        "contact_name": fields.String(
            description="Customer contact name", attribute="company_information.contact.name"
        ),
        "email": fields.String(description="Customer email", attribute="company_information.contact.email"),
        "billable": fields.Boolean(description="Customer is billable"),
        "status": fields.String(description="Customer status"),
    },
)

customer_non_compliant_cloudspace_model = api.model(
    "NonCompliantCloudSpace", generate_restfull_model_spec(NonCompliantCloudSpace, api)
)
customer_compliance_report_model = api.model(
    "CustomerComplianceReport", {"result": fields.List(fields.Nested(customer_non_compliant_cloudspace_model))}
)
customer_compliance_series_model = api.model(
    "CustomerComplianceSeries",
    {
        "timeseries": fields.List(fields.Integer(description="Epoch timestamps corresponding to the count series")),
        "count": fields.List(
            fields.Integer(
                description="Count of license issues recorded over time. Corresponds to timestamps in timeseries list"
            )
        ),
    },
)

vco_compliance_report_model = api.model(
    "VCOComplianceReport", {"result": fields.List(fields.Nested(CustomerComplianceStruct.model(api)))}
)

customers_simple_model = api.model("CustomersSimple", {"result": fields.List(fields.Nested(customer_simple_model))})
simulate_vm_price_parser = reqparse.RequestParser(argument_class=FixedArgument)
simulate_vm_price_parser.add_argument(
    "memory", type=int, location="args", required=True, help="Amount of memory in MiB"
)
simulate_vm_price_parser.add_argument("vcpus", type=int, location="args", required=True, help="Number of VCPUs")
simulate_vm_price_parser.add_argument(
    "disksize", type=int, location="args", required=True, help="Total disk size in GiB"
)
simulate_vm_price_parser.add_argument(
    "disk_type",
    type=str,
    default=DiskTypes.DATA.name,
    location="args",
    help="Disk Type",
    choices=DiskTypes.get_create_disk_types(),
)
simulate_vm_price_parser.add_argument(
    "physical_storage",
    type=int,
    default=0,
    location="args",
    help="Physical storage size (To be used in rancher)",
)
simulate_vm_price_parser.add_argument(
    "raid_level",
    type=str,
    default=RaidLevel.NO_RAID.value,
    location="args",
    help="Raid level",
    choices=RaidLevel.get_raid_levels(),
)
simulate_vm_price_parser.add_argument(
    "iops",
    default=2000,
    type=int,
    location="args",
    required=True,
    help="IOPS of the bootdisk",
)
simulate_vm_price_parser.add_argument(
    "is_windows_vm",
    default=False,
    type=boolean,
    location="args",
    required=True,
    help="Indicates if VM requires Windows license",
)
datacenter_model = api.model("Datacenter", generate_restfull_model_spec(Datacenter))
location_capacity_model = generate_api_response_model("cloudapi/billing/getCapacity", api)
account_consumption_parser = gen_arg_parser("cloudapi/billing/calculate$account")
account_consumption_model = generate_api_response_model("cloudapi/billing/calculate$account", api)
account_consumption_series_parser = gen_arg_parser("cloudapi/billing/calculateTimeseriesV2$account")
account_consumption_series_model = generate_api_response_model("cloudapi/billing/calculateTimeseriesV2$account", api)

customer_locations_model = api.model(
    "CustomerLocations", {"result": fields.List(fields.Nested(CustomerLocationStruct.model()))}
)
delete_customer_parser = reqparse.RequestParser(argument_class=FixedArgument)
delete_customer_parser.add_argument(
    "reason", type=str, location="args", required=False, default=None, help="Reason for deletion (Optional)"
)
delete_customer_parser.add_argument(
    "recursive_delete",
    type=boolean,
    location="args",
    required=False,
    default=False,
    help="Delete the customer recursively",
)
delete_customer_parser.add_argument(
    "permanent",
    type=boolean,
    location="args",
    required=False,
    default=False,
    help="Delete the customer permanently",
)
wireguard_create_api_model = api.model("InterfaceID", {"interface_id": fields.String(description="Interface ID")})

invoice_model = api.model("InvoiceModel", InvoiceStruct.model(api))
invoices_model = api.model("InvoicesModel", {"result": fields.List(fields.Nested(invoice_model))})
customer_invoice_model = get_pagination_model("PaginatedInvoicesModel", invoice_model)
top_level_domain_parser = reqparse.RequestParser(argument_class=FixedArgument)
top_level_domain_parser.add_argument(
    "domain",
    type=str,
    location="args",
    required=True,
    help="Customer top level domain or customer top level domain along with vco top level domain",
)

customer_role_resource_grant = api.model(
    "CustomerRoleResourceGrant",
    {
        "grant_action": fields.Boolean(
            description="If True, grant access to the resource, if False revoke access from the resource"
        ),
        "resource_type": fields.String(description="Resource type", enum=CloudResourceType.values()),
        "resource_id": fields.String(
            description="""
        Id of the resource.
Not relevant for a customer role.
For a location the location code needs to be used.
For a virtual machine the id is formed by concatenating the vm cloudspace_id with the vm id separated
by a colon (eg kjgkhjgkjhgk:45646 )"""
        ),
    },
)

customer_role_resource = api.model(
    "CustomerRoleResource",
    {
        "resource_type": fields.String(description="Resource type", enum=CloudResourceType.values()),
        "resource_id": fields.String(
            description="""
        Id of the resource.
Not relevant for a customer role.
For a location the location code needs to be used.
For a virtual machine the id is formed by concatenating the vm cloudspace_id with the vm id separated
by a colon (eg kjgkhjgkjhgk:45646 )"""
        ),
        "resource_name": fields.String(description="Resource name"),
    },
)

customer_role_resources = api.model(
    "CustomerRoleResources",
    {
        "locations": fields.List(
            fields.Nested(
                api.model(
                    "LocationStatus",
                    {
                        "location": fields.String(description="Location name"),
                        "status": fields.String(description="Location status, OK or error"),
                    },
                )
            )
        ),
        "result": fields.List(fields.Nested(customer_role_resource)),
    },
)

geocode_parser = reqparse.RequestParser()
geocode_parser.add_argument("address", type=str, location="args", required=True, help="Address query")

domain_ownership_verification_model = DomainOwnershipCodeStruct.model(api)

delete_domain_parser = reqparse.RequestParser(argument_class=FixedArgument)
delete_domain_parser.add_argument("record_type", type=str, required=True, location="args", help="Record type")

delete_domain_parser.add_argument("domain_name", type=str, required=True, location="args", help="Domain Name")

delete_domain_parser.add_argument("value", type=str, location="args", help="Record Value")
list_customers_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_customers_parser.add_argument(
    "include_deleted", type=boolean, required=False, location="args", default=False, help="include deleted customers"
)
list_customers_parser.add_argument(
    "only_deleted",
    type=boolean,
    required=False,
    location="args",
    default=False,
    help="list only non obsolete deleted customers",
)
list_customers_parser.add_argument(
    "location",
    type=str,
    required=False,
    location="args",
    default=None,
    help="Filter customers by location",
)

list_invoices_argparser = reqparse.RequestParser(argument_class=FixedArgument)
list_invoices_argparser.add_argument("limit", type=int, default=25, location="args", help="Limit")
list_invoices_argparser.add_argument("start_after", type=int, default=0, location="args", help="Start after")

license_incompliancy_forwarding = LicenseIncompliancyForwardingStruct.model(api)

edit_license_incompliancy_parser = reqparse.RequestParser(argument_class=FixedArgument)
edit_license_incompliancy_parser.add_argument(
    "enabled", type=boolean, required=True, location="args", help="Incompliancy notifications forwarding status"
)

unsubscribe_parser = reqparse.RequestParser(argument_class=FixedArgument)
unsubscribe_parser.add_argument("location", type=str, location="args", help="Target location", required=True)
subscribe_policy_parser = reqparse.RequestParser(argument_class=FixedArgument)
subscribe_policy_parser.add_argument("target_id", type=str, location="args", help="Target id", required=True)

worker_location_parser = reqparse.RequestParser(argument_class=FixedArgument)
worker_location_parser.add_argument("location", type=str, location="args", help="Worker location", required=True)
list_sessions_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_sessions_parser.add_argument(
    "status", type=str, location="args", default=None, required=False, help="Filter by session status"
)
list_sessions_parser.add_argument(
    "username", type=str, location="args", default=None, required=False, help="Filter by user username"
)
list_sessions_parser.add_argument(
    "email", type=str, location="args", default=None, required=False, help="Filter by user email"
)
list_sessions_parser.add_argument(
    "after_created_at", type=float, location="args", default=None, required=False, help="Created at >= timestamp"
)
list_sessions_parser.add_argument(
    "before_created_at", type=float, location="args", default=None, required=False, help="Created at <= timestamp"
)
list_sessions_parser.add_argument(
    "after_connected_at", type=float, location="args", default=None, required=False, help="Connected at >= timestamp"
)
list_sessions_parser.add_argument(
    "before_connected_at", type=float, location="args", default=None, required=False, help="Connected at <= timestamp"
)
list_sessions_parser.add_argument(
    "remote_ip", type=str, location="args", default=None, required=False, help="Remote IP address"
)
list_sessions_parser.add_argument(
    "geo_coded_city", type=str, location="args", default=None, required=False, help="Geo-coded city"
)

targets_parser = reqparse.RequestParser(argument_class=FixedArgument)
targets_parser.add_argument(
    "limit", type=int, default=0, location="args", help="Flag to limit the amount of results. 0 means no limit"
)
targets_parser.add_argument(
    "start_after", type=int, location="args", default=0, help="Start returning records after index"
)
targets_parser.add_argument("sort_by", type=int, default=None, location="args", help="Sort by")
targets_parser.add_argument("sort_direction", type=int, location="args", default=1, help="Sort direction")
targets_parser.add_argument(
    "metadata", type=str, location="args", default="", help="Json string with metadata for filtering"
)
list_backup_workers_parser = reqparse.RequestParser(argument_class=FixedArgument)
list_backup_workers_parser.add_argument(
    "limit",
    type=int,
    default=25,
    location="args",
    help="limit",
)
list_backup_workers_parser.add_argument(
    "start_after", type=int, default=0, location="args", help="Index to start listing after"
)
list_backup_workers_parser.add_argument(
    "sort_direction",
    type=int,
    default=-1,
    location="args",
    help="Sort direction 1 for ascending and -1 for descending order",
    choices=[-1, 1],
)
list_backup_workers_parser.add_argument("sort_by", type=str, default=None, location="args", help="Sort by field")


grant_vdi_profile_role_parser = reqparse.RequestParser(argument_class=FixedArgument)
grant_vdi_profile_role_parser.add_argument(
    "role_id",
    type=str,
    required=True,
    help="Role ID to be granted to the VDI profile",
    location="args",
)

logs_parser = reqparse.RequestParser()
logs_parser.add_argument("job_id", type=str, required=False, help="Specific standby job id (optional)")
logs_parser.add_argument(
    "include_debug_logs", type=boolean, default=True, required=False, help="Include debug logs (optional)"
)
status_report_parser = reqparse.RequestParser(argument_class=FixedArgument)
status_report_parser.add_argument(
    "status",
    type=str,
    choices=RDPStatus.values(),
    location="args",
    help="RDP status",
)

# Parser for adding/removing single role
service_account_role_parser = reqparse.RequestParser(argument_class=FixedArgument)
service_account_role_parser.add_argument(
    "role_id", type=str, location="args", required=True, help="Role ID to add or remove"
)

# Parser for attaching service account to resource
attach_service_account_parser = reqparse.RequestParser(argument_class=FixedArgument)
attach_service_account_parser.add_argument(
    "resource_type",
    type=str,
    location="args",
    required=True,
    choices=ServiceAccountResourceType.values(),
    help="Type of resource",
)
attach_service_account_parser.add_argument(
    "resource_id", type=str, location="args", required=True, help="ID of the resource"
)
attach_service_account_parser.add_argument(
    "service_account_id", type=str, location="args", required=True, help="Service account ID to attach"
)


@ns.route("")
class Customers(Resource):
    @authenticated
    @requires_vco_admin
    @ns.expect(list_customers_parser)
    @ns.doc(shortcut="listCustomers", description="List customers")
    @ns.marshal_with(CustomerSimpleStruct.list_model(api))
    def get(self):
        return {"result": customer_business.list_customers(vco_id=get_vco_id(), **list_customers_parser.parse_args())}

    @authenticated
    @requires_vco_admin
    @ns.expect(CustomerCreateStruct.model(api), validate=True)
    @ns.doc(shortcut="createCustomer", description="Add new customer", model=fields.String(description="Customer ID"))
    def post(self):
        is_https = EnvironmentName.current() != EnvironmentName.TEST
        vco_details: VCOCachedDetails = flask_g.vco_details
        iyo_client = ItsyouOnlineClient.new(
            vco_details.iam_domain,
            vco_details.iam_root_organization,
            vco_details.iam_root_organization_api_key,
            is_https,
        )
        customer = customer_business.add(
            iyo_client.jwt,
            vco_details.iam_root_organization,
            get_vco_id(),
            CustomerCreateStruct.load(ns.payload),
        )
        return customer.customer_id, 201


customer_self_create_model = {}


@ns.route("/self-creation")
class CustomerSelfCreation(Resource):
    @authenticated
    @ns.expect(CustomerSelfCreateStruct.model(api), validate=True)
    @ns.doc(
        shortcut="selfCreateCustomer",
        description="Self-create a new customer",
        model=fields.String(description="Customer ID"),
    )
    def post(self):
        is_https = EnvironmentName.current() != EnvironmentName.TEST
        vco_details: VCOCachedDetails = flask_g.vco_details
        iyo_client = ItsyouOnlineClient.new(
            vco_details.iam_domain,
            vco_details.iam_root_organization,
            vco_details.iam_root_organization_api_key,
            is_https,
        )
        customer = customer_business.self_create(
            iyo_client.jwt,
            vco_details.iam_root_organization,
            get_vco_id(),
            CustomerSelfCreateStruct.load(ns.payload),
            get_current_user_info(),
        )
        return customer.customer_id, 201


@ns.route("/compliance")
class CustomerCompliance(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(
        shortcut="getCustomerComplianceOverview", description="Returns a list of customers that have compliance issues"
    )
    @ns.marshal_with(vco_compliance_report_model)
    def get(self):
        return dict(result=customer_business.get_customer_compliance(get_vco_id()))


@ns.route("/<customer_id>")
class Customer(Resource):
    @authenticated
    @requires_custom(is_vco_admin, is_customer_org_admin)
    @ns.marshal_with(CustomerBaseStruct.model(api))
    @ns.doc(shortcut="getCustomerInfo", description="Get customer info")
    def get(self, customer_id):
        return customer_business.get(customer_id=customer_id)

    @authenticated
    @requires_vco_admin
    @ns.expect(CustomerUpdateStruct.model(api), validate=True)
    @ns.doc(
        shortcut="updateCustomer", description="Update customer info", model=fields.String(description="Customer ID")
    )
    def put(self, customer_id):
        payload = CustomerUpdateStruct.load(ns.payload)
        jwt = get_current_user_info().jwt
        customer = customer_business.update(customer_id, payload, jwt)
        return str(customer.id), 201

    @authenticated
    @requires_custom(is_vco_admin, is_customer_org_admin)
    @ns.expect(delete_customer_parser)
    @ns.doc(shortcut="deleteCustomer", description="Delete customer")
    def delete(self, customer_id):
        jwt = get_current_user_info().jwt
        customer_business.delete(customer_id, jwt, is_vco_admin(get_vco_id()), **delete_customer_parser.parse_args())


@ns.route("/<customer_id>/audits")
class CustomerAudits(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc("listCustomerAudits", description="List customer audits logs")
    @ns.marshal_with(audits_pagination_model)
    @ns.expect(audits_list_argparser)
    def get(self, customer_id):
        args = audits_list_argparser.parse_args()
        args["vco"] = get_vco_id()
        args["customer_id"] = customer_id
        audits = AuditLog.list(**args)
        return pagination_handler_for_audits(audits, args["limit"])


@ns.route("/<customer_id>/audits/<audit_id>")
class CustomerAudit(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc("getCustomerAudit", description="Get customer audit details")
    @ns.marshal_with(audit_model)
    def get(self, customer_id, audit_id):
        return AuditLog.get_by_id(audit_id)


@ns.route("/<customer_id>/enable")
class EnableCustomer(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="enableCustomer", description="Enable Customer")
    def put(self, customer_id):
        customer_business.enable_customer(customer_id, get_current_user_info().jwt)


@ns.route("/geocode")
class Geocoder(Resource):
    @authenticated
    @ns.hide
    @ns.expect(geocode_parser)
    @ns.doc(
        shortcut="geoCodeAddress",
        description="Geocode address",
    )
    def get(self):
        args = geocode_parser.parse_args()
        address = args.get("address")
        return geocode_address(address=address)["geometry"]["location"]


@ns.route("/<customer_id>/disable")
class DisableCustomer(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="disableCustomer", description="Disable Customer")
    def put(self, customer_id):
        customer_business.disable_customer(customer_id, get_current_user_info().jwt)


@ns.route("/<customer_id>/licenses")
class CustomerSoftwareLicenses(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getCustomerSoftwareLicenses", description="Get Customer Software Licenses")
    @ns.marshal_with(VMSoftwareLicenseStruct.list_model())
    def get(self, customer_id):
        return {"result": VMLicense.list_customer_licenses(customer_id)}


@ns.route("/<customer_id>/locations")
class CustomerLocations(Resource):
    @authenticated
    @requires_custom(is_vco_admin, has_access_to_customer)
    @ns.marshal_with(PricingStruct.model(api))
    @ns.doc(
        shortcut="listCustomerLocations",
        description="List customer available locations",
    )
    def get(self, customer_id):
        return customer_business.get_customer_prices(get_vco_id(), customer_id)

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="addCustomerLocation", description="Add location for Customer")
    @ns.expect(AddingCustomerLocationStruct.model(api))
    def post(self, customer_id):
        jwt = get_current_user_info().jwt
        customer_business.add_location(
            customer_id=customer_id,
            location=AddingCustomerLocationStruct.load(ns.payload),
            jwt=jwt,
            vco_id=get_vco_id(),
        )


@ns.route("/<customer_id>/locations/<location>")
class CustomerLocation(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="removeCustomerLocation", description="remove location from Customer")
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @ns.response(200, "Success")
    @customer_business.customer_has_access_to_location
    def delete(self, customer_id, location):
        jwt = get_current_user_info().jwt
        customer_business.delete_location(customer_id, location, jwt)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.doc(shortcut="getCustomerLocation", description="Get customer location")
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_has_access_to_location
    @ns.marshal_with(CustomerLocationInfoStruct.model(api))
    def get(self, customer_id, location):
        return customer_business.get_location_info(location)


@ns.route("/<customer_id>/locations/<location>/datacenter")
class CustomerLocationDatacenter(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(datacenter_model)
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @ns.doc(shortcut="getCustomerLocationDatacenter", description="Get customer location datacenter")
    @customer_business.customer_has_access_to_location
    def get(self, customer_id, location):
        return customer_business.customer_location_datacenter(customer_id, location)


@ns.route("/<customer_id>/locations/<location>/capacity")
class CustomerLocationCapacity(Resource):
    @authenticated
    @requires_custom(is_vco_admin, is_customer_org_admin)
    @ns.marshal_with(location_capacity_model)
    @ns.doc(shortcut="getCustomerLocationCapacity", description="Get location Capacity")
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_has_access_to_location
    def get(self, customer_id, location):
        return G8Client(location, jwt=get_current_user_info().jwt).get_capacity()


@ns.route("/<customer_id>/locations/<location>/prices")
class CustomerLocationPricing(Resource):
    @authenticated
    @requires_custom(is_vco_admin, is_customer_org_admin)
    @ns.marshal_with(G8PricingWithCurrencyStruct.model(api))
    @ns.doc(
        shortcut="getCustomerLocationPricing",
        description="Get location prices for this Customer",
    )
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_has_access_to_location
    def get(self, customer_id, location):
        return customer_business.get_location_prices(get_vco_id(), customer_id, location)


@ns.route("/<customer_id>/prices")
class CustomerPricing(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(PricingWithLicensesStruct.model(api))
    @ns.doc(
        shortcut="getCustomerPricing",
        description="Get location prices for this Customer",
    )
    def get(self, customer_id):
        return customer_business.get_customer_prices(get_vco_id(), customer_id)


@ns.route("/<customer_id>/locations/<location>/vm-price-simulation")
class VMPriceSimulation(Resource):
    @authenticated
    @ns.expect(simulate_vm_price_parser)
    @ns.marshal_with(vm_price_spec)
    @ns.doc(shortcut="getVMPriceSimulation", description="get estimated VM price per month")
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_has_access_to_location
    def get(self, customer_id, location):
        kwargs = simulate_vm_price_parser.parse_args()
        kwargs["disk_type"] = DiskTypes.get_by_name(kwargs["disk_type"]).value
        return customer_business.simulate_vm_price(get_vco_id(), customer_id, location, **kwargs)


@alpha_ns.route("/customers/<customer_id>/dns/top-level-domains")
class CustomerResourceDomains(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(TopLevelDomainStruct.list_model(api))
    @ns.response(200, "List of customer top level domains")
    @ns.doc(
        shortcut="getCustomerTopLevelDomains", description="Get all top level domains domains of a Customer and his vco"
    )
    def get(self, customer_id):
        return {"result": customer_business.get_customer_top_level_domains(customer_id)}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(top_level_domain_parser)
    @ns.marshal_with(success_model)
    @ns.response(200, "success")
    @ns.doc(
        shortcut="addCustomerTopLevelDomain",
        description="Add top level domain to customer",
    )
    def post(self, customer_id):
        customer_business.add_customer_top_level_domain(customer_id, **top_level_domain_parser.parse_args())
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/dns/top-level-domain/<domain>")
class CustomerTopLevelDomain(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.doc(shortcut="getCustomerTopLevelDomain", description="get a top level domain of a customer")
    @ns.response(404, "A top level domain matching query is not found")
    @ns.marshal_with(TopLevelDomainStruct.model(api))
    def get(self, customer_id, domain):
        return customer_business.get_top_level_domain(customer_id, domain=domain)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="deleteCustomerTopLevelDomain", description="Delete a top level domain of a customer")
    @ns.marshal_with(success_model)
    def delete(self, customer_id, domain):
        customer_business.delete_top_level_domain(customer_id, domain=domain)
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/dns/top-level-domain/vco")
class CustomerVCOTopLevelDomain(Resource):
    @authenticated
    @ns.marshal_with(TopLevelDomainStruct.model(api))
    @requires_custom(has_access_to_customer)
    @ns.doc(shortcut="getCustomerVcoTopLevelDomainDomain", description="Get the top level domain of vco of a customer")
    @ns.response(200, "VCO top level domain")
    @ns.response(404, "Top level domain is not set yet")
    def get(self, customer_id):
        return customer_business.get_vco_top_level_domain(customer_id)


@alpha_ns.route("/customers/<customer_id>/dns-records")
class CustomerDnsRecords(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.doc(shortcut="getCustomerDnsRecords", description="Get customer dns records")
    @ns.marshal_with(CustomerResourceDnsRecordStruct.list_model(api))
    def get(self, customer_id):
        return {"result": customer_business.get_dns_records(customer_id)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(dns_record_parser)
    @ns.doc(shortcut="addCustomerDnsRecord", description="Add customer dns records")
    @ns.marshal_with(success_model)
    def post(self, customer_id):
        args = dns_record_parser.parse_args()
        customer_business.add_customer_dns_record(
            get_vco_id(),
            customer_id,
            args["domain"],
            args["priority"],
            args["weight"],
            args["port"],
            args["service"],
            args["protocol"],
            args["value"],
            SupportedDNSRecordType.from_string(args["type"]),
            caa_domain=args["caa_domain"],
            flag=args["flag"],
            tag=args["tag"],
        )
        return SUCCESS

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(delete_domain_parser)
    @ns.doc(shortcut="deleteCustomerDnsRecord", description="Delete customer dns records")
    @ns.marshal_with(success_model)
    def delete(self, customer_id):
        args = delete_domain_parser.parse_args()
        delete_customer_dns_record(
            vco_id=get_vco_id(),
            customer_id=customer_id,
            domain_name=args["domain_name"],
            value=args["value"],
            record_type=args["record_type"],
        )
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/dns")
class CustomerDomainOwnershipVerification(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(domain_ownership_verification_model)
    @ns.response(200, "Customer domain ownership verification")
    @ns.doc(
        shortcut="getCustomerDomainOwnershipCode",
        description="Get the code required to be added by the customer to txt record to provide ownership of a domain",
    )
    def get(self, customer_id):
        return customer_business.get_domain_ownership_code(customer_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(domain_ownership_verification_model)
    @ns.response(200, "Customer domain ownership verification")
    @ns.doc(
        shortcut="newCustomerDomainOwnershipCode",
        description="Generate and returns a new ownership of a domain code for the customer",
    )
    def put(self, customer_id):
        return customer_business.generate_new_ownership_code(customer_id)


@ns.route("/<customer_id>/locations/<location>/consumption")
class CustomerLocationConsumption(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(account_consumption_parser)
    @ns.marshal_with(account_consumption_model)
    @ns.doc(shortcut="getCustomerConsumption", description="get location resource consumption for this Customer")
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_account()
    def get(self, account_id, location):
        return customer_business.get_customer_account_consumption(
            account_id, location, get_current_user_info().jwt, **account_consumption_parser.parse_args()
        )


@ns.route("/<customer_id>/locations/<location>/consumption/series")
class CustomerLocationConsumptionSeries(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(account_consumption_series_parser)
    @ns.marshal_with(CustomerLocationConsumptionSeriesStruct.model(api))
    @ns.doc(
        shortcut="getCustomerConsumptionSeries",
        description="get location resource consumption timeseries for this Customer",
    )
    @ns.response(404, "NotFound: Customer not found")
    @ns.response(403, "Forbidden: Customer has no access to location")
    @customer_business.customer_account()
    def get(self, account_id, location):
        return customer_business.get_customer_account_consumption_ts(
            account_id, location, get_current_user_info().jwt, **account_consumption_series_parser.parse_args()
        )


@ns.route("/<customer_id>/compliance")
class CustomerComplianceReport(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(
        shortcut="getCustomerComplianceReport",
        description="get the information about noncompliant virtual machines in the last 24hr for this Customer",
    )
    @ns.marshal_with(customer_compliance_report_model)
    def get(self, customer_id):
        return {"result": license_compliance_reports.get_customer_last24h_report(customer_id)}


@ns.route("/<customer_id>/statistics/license-compliance")
class CustomerComplianceSeries(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(customer_compliance_series_model)
    @ns.doc(
        shortcut="getCustomerComplianceSeries",
        description="get the information about licensing issues over time for this Customer",
    )
    def get(self, customer_id):
        return license_compliance_reports.get_customer_compliance_series(customer_id)


@ns.route("/<customer_id>/invoices")
class CustomerInvoices(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(list_invoices_argparser)
    @ns.doc(shortcut="listCustomerInvoices", description="List customer Invoices")
    @ns.marshal_with(customer_invoice_model)
    def get(self, customer_id):
        args = list_invoices_argparser.parse_args()
        return pagination_handler(
            Invoice.list_simple,
            seller_id=BillingOrganization.get_id(BillingRole.VCO, get_vco_id()),
            buyer_id=BillingOrganization.get_id(BillingRole.CUSTOMER, customer_id),
            status=InvoiceStatus.SENT,
            only=[
                "invoice_id",
                "creation_timestamp",
                "currency",
                "number",
                "total_incl",
                "month",
                "status",
                "payment_status",
            ],
            **args,
        )


@ns.route("/<customer_id>/invoices/<invoice_id>/pdf")
class CustomerInvoice(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getCustomerInvoicePDF", description="Get customer Invoice PDF")
    @ns.produces(["application/pdf"])
    def get(self, customer_id, invoice_id):
        return make_response(
            get_invoice_pdf(invoice_id=invoice_id),
            200,
            {"Content-Type": "application/pdf"},
        )


@alpha_ns.route("/customer/<customer_id>/cards")
class CustomerCards(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="addCustomerCard", description="Add customer Card")
    def post(self, customer_id):
        return add_customer_card(get_vco_id(), customer_id, ns.payload)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listCustomerCards", description="List customer Cards")
    def get(self, customer_id):
        payment_gateway, cards = list_customer_cards(get_vco_id(), customer_id)
        if payment_gateway:
            return marshal({"result": cards}, saved_cards_fields_mapper[payment_gateway])
        else:
            return {"result": []}


@alpha_ns.route("/customer/<customer_id>/cards/<card_id>")
class CustomerPaymentCard(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getCustomerCard", description="Get customer Card")
    def get(self, customer_id, card_id):
        payment_gateway, card = get_customer_card(get_vco_id(), customer_id, card_id)
        return marshal(card, saved_card_fields_mapper[payment_gateway])

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.response(204, "Updated")
    @ns.doc(shortcut="setCustomerCardAsDefault", description="Set customer Card as Default")
    def put(self, customer_id, card_id):
        set_customer_card_as_default(get_vco_id(), customer_id, card_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.response(204, "Deleted")
    @ns.doc(shortcut="deleteCustomerCard", description="Delete customer Card")
    def delete(self, customer_id, card_id):
        delete_customer_card(get_vco_id(), customer_id, card_id)


@ns.route("/<customer_id>/roles")
class CustomerRoles(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listRoles", description="Get customer roles")
    @ns.marshal_with(CustomerRoleStruct.list_model())
    def get(self, customer_id):
        return {"result": list_roles(customer_id)}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="createRole", description="Create customer role")
    @ns.marshal_with(CustomerRoleStruct.model())
    @ns.expect(CustomerRoleCreateStruct.model())
    def post(self, customer_id):
        return create_role(get_current_user_info().jwt, customer_id, CustomerRoleCreateStruct(ns.payload))


@ns.route("/<customer_id>/roles/<role_id>")
class CustomerRole(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getRole", description="Get customer role")
    @ns.marshal_with(CustomerRoleFullStruct.model())
    def get(self, customer_id, role_id):
        return get_role(customer_id, role_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="deleteRole", description="Delete customer role")
    def delete(self, customer_id, role_id):
        delete_role(get_current_user_info().jwt, customer_id, role_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="updateRole", description="Update customer role")
    @ns.expect(CustomerRoleCreateStruct.model())
    def put(self, customer_id, role_id):
        update_role(get_current_user_info().jwt, customer_id, role_id, CustomerRoleCreateStruct(ns.payload))


@ns.route("/<customer_id>/roles/<role_id>/grants")
class CustomerRoleResourceAccess(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listRoleGrants", description="List cloud resources to which the role has access")
    @ns.marshal_with(customer_role_resources)
    def get(self, customer_id, role_id):
        location_statuses, grants = get_role_grants(get_current_user_info().jwt, customer_id, role_id)
        return {"locations": location_statuses, "result": grants}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="updateRoleGrants", description="Add/remove role access to a cloud resource")
    @ns.expect(customer_role_resource_grant)
    def put(self, customer_id, role_id):
        grant_revoke_role(get_current_user_info().jwt, customer_id, role_id, **ns.payload)


@ns.route("/<customer_id>/service-accounts")
class CustomerServiceAccounts(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listServiceAccounts", description="List customer service accounts")
    @ns.marshal_with(ServiceAccountStruct.list_model(api))
    def get(self, customer_id):
        return {"result": list_service_accounts(get_current_user_info().jwt, customer_id)}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="createServiceAccount", description="Create customer service account")
    @ns.marshal_with(ServiceAccountStruct.model(api))
    @ns.expect(ServiceAccountCreateStruct.model(api))
    def post(self, customer_id):
        payload = from_dict(ServiceAccountCreateStruct, ns.payload)
        vco_id = get_vco_id()
        return create_service_account(get_current_user_info().jwt, vco_id, customer_id, payload.name, payload.role_ids)


@ns.route("/<customer_id>/service-accounts/<service_account_id>")
class CustomerServiceAccount(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getServiceAccount", description="Get customer service account")
    @ns.marshal_with(ServiceAccountStruct.model(api))
    def get(self, customer_id, service_account_id):
        return get_service_account(get_current_user_info().jwt, customer_id, service_account_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="updateServiceAccount", description="Update customer service account")
    @ns.marshal_with(ServiceAccountStruct.model(api))
    @ns.expect(ServiceAccountUpdateStruct.model(api))
    def put(self, customer_id, service_account_id):
        payload = from_dict(ServiceAccountUpdateStruct, ns.payload)
        vco_id = get_vco_id()
        return update_service_account(
            get_current_user_info().jwt, vco_id, customer_id, service_account_id, payload.name, payload.role_ids
        )

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="deleteServiceAccount", description="Delete customer service account")
    @ns.marshal_with(success_model)
    def delete(self, customer_id, service_account_id):
        vco_id = get_vco_id()
        delete_service_account(get_current_user_info().jwt, vco_id, customer_id, service_account_id)
        return SUCCESS


@ns.route("/<customer_id>/service-accounts/<service_account_id>/jwt")
class CustomerServiceAccountJWT(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getServiceAccountJWT", description="Get service account JWT token")
    def get(self, customer_id, service_account_id):
        vco_id = get_vco_id()
        jwt_token = get_service_account_jwt(vco_id, customer_id, service_account_id)
        return {"jwt": jwt_token}


@ns.route("/<customer_id>/service-accounts/<service_account_id>/roles")
class ServiceAccountRoles(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="addRoleToServiceAccount", description="Add role to service account")
    @ns.marshal_with(ServiceAccountStruct.model(api))
    @ns.expect(service_account_role_parser)
    def post(self, customer_id, service_account_id):
        args = service_account_role_parser.parse_args()
        vco_id = get_vco_id()
        role_id = args.get("role_id")
        return add_role_to_service_account(
            get_current_user_info().jwt, vco_id, customer_id, service_account_id, role_id
        )

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="removeRoleFromServiceAccount", description="Remove role from service account")
    @ns.marshal_with(ServiceAccountStruct.model(api))
    @ns.expect(service_account_role_parser)
    def delete(self, customer_id, service_account_id):
        args = service_account_role_parser.parse_args()
        vco_id = get_vco_id()
        role_id = args.get("role_id")
        return remove_role_from_service_account(
            get_current_user_info().jwt, vco_id, customer_id, service_account_id, role_id
        )


@ns.route("/<customer_id>/roles/<role_id>/service-accounts")
class RoleServiceAccounts(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getServiceAccountsByRole", description="Get service accounts by role")
    @ns.marshal_with(ServiceAccountStruct.list_model(api))
    def get(self, customer_id, role_id):
        return {"result": get_service_accounts_by_role(customer_id, role_id)}


@ns.route("/<customer_id>/granted-roles")
class RolesOnCustomer(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listRolesOnCustomer", description="List roles with customer wide access")
    @ns.marshal_with(CustomerRoleStruct.list_model())
    def get(self, customer_id):
        roles = list_roles_on_customer(customer_id=customer_id)
        return {"result": [role.to_mongo().to_dict() for role in roles]}


@ns.route("/<customer_id>/locations/<location>/granted-roles")
class RolesOnLocation(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="listRolesOnLocation", description="List roles with access to this location")
    @ns.marshal_with(CustomerRoleStruct.list_model())
    @customer_business.customer_account(preserve_customer_id=True)
    def get(self, customer_id, account_id, location):
        roles = list_roles_on_location(
            jwt=get_current_user_info().jwt, customer_id=customer_id, account_id=account_id, location=location
        )
        return {"result": [role.to_mongo().to_dict() for role in roles]}


@ns.route("/currencies")
class Currencies(Resource):
    @authenticated
    @ns.doc(
        shortcut="getCurrencies",
        description="Get Currencies",
        model=fields.List(fields.String(description="Currency code")),
    )
    def get(self):
        return CurrenciesEnum.values()


@alpha_ns.route("/customers/<customer_id>/dns/validate-domain-ownership")
class CustomerTopLevelDomainValidationTxt(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(top_level_domain_parser)
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="validateCustomerDomainOwnership",
        description="Validate customer domain ownership",
    )
    def get(self, customer_id):
        customer_business.validate_domain_ownership(customer_id, **top_level_domain_parser.parse_args())
        return SUCCESS


@ns.route("/<customer_id>/support-access")
class SupportAccess(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="getSupportAccess", description="get support organization access")
    def get(self, customer_id):
        return check_support_status(customer_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="addSupportAccess", description="add support organization access")
    def post(self, customer_id):
        return add_support_organization(get_current_user_info().jwt, customer_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.doc(shortcut="deleteSupportAccess", description="delete support organization access")
    def delete(self, customer_id):
        return delete_support_organization(get_current_user_info().jwt, customer_id)


@ns.route("/<customer_id>/restore/")
class RestoreCustomer(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(CustomerSimpleStruct.list_model(api))
    @ns.doc(shortcut="restoreDeletedCustomer", description="Restore deleted customers")
    def post(self, customer_id):
        customer_business.restore_deleted_customer(customer_id=customer_id)


@ns.route("/<customer_id>/subscriptions")
class CustomerSubscriptions(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(MicrosoftLicenseSubscriptionStruct.model(), validate=True)
    @ns.doc(shortcut="updateLicenseSubscription", description="Update License Subscription")
    def put(self, customer_id):
        customer_business.update_licenses_notification_subscription(
            customer_id, MicrosoftLicenseSubscriptionStruct(ns.payload).to_mongoengine()
        )

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(MicrosoftLicenseSubscriptionStruct.model())
    @ns.doc(shortcut="getLicenseSubscription", description="get License Subscription")
    def get(self, customer_id):
        return CustomerModel.get_licenses_notification_subscription(customer_id=customer_id)


@ns.route("/<customer_id>/subscriptions/webhook-test")
class CustomerSubscriptionsWebhookTest(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(CustomerSubscriptionWebhook.model(api))
    @ns.doc(shortcut="webhookTest", description="send request to test webhook")
    def put(self, customer_id):
        customer_business.test_send_notification_to_webhook(customer_id, ns.payload["webhook"])


@ns.route("/quota")
class VCOStandardQuota(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateVCOStandardQuota", description="Update VCO standard quotas")
    @ns.expect(VcoUpdateLocationsQuotasWithErrorMessageStruct.model(api))
    def put(self):
        jwt = get_current_user_info().jwt
        set_vco_quotas(
            get_vco_id(), jwt, VcoUpdateLocationsQuotasWithErrorMessageStruct.load(ns.payload, remove_none=True)
        )

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(LocationsQuotasWithErrorMessageStruct.model(api))
    @ns.doc(shortcut="getVCOStandardQuota", description="Get VCO standard quotas")
    def get(self):
        return get_vco_resource_quotas(get_vco_id())


@ns.route("/<customer_id>/quota")
class CustomerResourceQuota(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="updateCustomerQuota", description="Update customer standard quotas")
    @ns.expect(LocationsQuotasWithErrorMessageStruct.model(api))
    def put(self, customer_id):
        jwt = get_current_user_info().jwt
        set_customer_quotas(customer_id, jwt, LocationsQuotasWithErrorMessageStruct.load(ns.payload, remove_none=True))

    @authenticated
    @requires_vco_admin
    @ns.marshal_with(LocationsQuotasWithErrorMessageStruct.model(api))
    @ns.doc(shortcut="getCustomerQuota", description="Get customer standard quotas")
    def get(self, customer_id):
        jwt = get_current_user_info().jwt
        return get_customer_resource_quotas(customer_id, jwt)


@ns.route("/<customer_id>/emergency-notifications")
class CustomerEmergencyNotifications(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(CustomerLocationStruct.list_model(field="locations"))
    @ns.doc(
        shortcut="updateEmergencyNotificationSubscription", description="Update emergency notification subscription"
    )
    def put(self, customer_id):
        jwt = get_current_user_info().jwt
        customer_business.update_customer_emergency_notifications_subscription(customer_id, jwt, ns.payload)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(CustomerLocationStruct.model())
    @ns.doc(shortcut="getEmergencyNotificationSubscription", description="get emergency notification subscription")
    def get(self, customer_id):
        return CustomerModel.get_by_id(customer_id, only=["locations"]).locations


@ns.route("/<customer_id>/emergency-contact-info")
class CustomerEmergencyNotificationsContact(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(AlertNotificationContactStruct.model(api))
    @ns.doc(shortcut="updateEmergencyNotificationContact", description="Update emergency notification contact info")
    def put(self, customer_id):
        jwt = get_current_user_info().jwt
        customer_business.update_customer_emergency_notifications_contact_info(customer_id, jwt, ns.payload)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(AlertNotificationContactStruct.model(api))
    @ns.doc(shortcut="getEmergencyNotificationContact", description="get emergency notification contact info")
    def get(self, customer_id):
        return CustomerModel.get_by_id(customer_id, only=["company_information"]).company_information.alert_information


@ns.route("/payment-gateway")
class UserPaymentGateway(Resource):
    @authenticated
    @ns.doc(shortcut="getPaymentGateway", description="Get payment gateway")
    def get(self):
        payment_gateway, config = inv_cfg.get_payment_config(BillingOrganization.get_id(BillingRole.VCO, get_vco_id()))
        if payment_gateway.value == "Stripe":
            return {"payment_gateway": payment_gateway.value, "public_api_key": config.public_api_key}
        return {"payment_gateway": payment_gateway.value}


@ns.route("/<customer_id>/customer-incompliancy-notifications")
class CustomerIncompliancyNotification(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="getCustomerIncompliancyForwarding", description="Gets customer license incompliancy forwarding")
    @ns.marshal_with(license_incompliancy_forwarding)
    def get(self, customer_id):
        return dict(
            enabled=CustomerModel.get_by_id(
                customer_id, only=["license_incompliancy_forwarding"]
            ).license_incompliancy_forwarding
        )

    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="editCustomerIncompliancyForwarding", description="Edit customer license incompliancy forwarding")
    @ns.expect(edit_license_incompliancy_parser)
    def put(self, customer_id):
        args = edit_license_incompliancy_parser.parse_args()
        customer = CustomerModel.get_by_id(customer_id, only=["license_incompliancy_forwarding"])
        customer.update(license_incompliancy_forwarding=args.get("enabled"))


@ns.route("/<customer_id>/locations/consumption")
class CustomerLocationsConsumption(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(CustomerLocationConsumptionResources.list_model(api))
    @ns.doc(
        shortcut="listCustomerLocationsConsumption",
        description="List customer available locations",
    )
    def get(self, customer_id):
        return {
            "result": customer_business.get_customer_location_consumptions_details(
                customer_id, get_current_user_info().jwt
            )
        }


@ns.route("/<customer_id>/show-prices")
class CustomerShowPrices(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(ShowPricesStruct.model(api))
    @ns.doc(
        shortcut="getCustomerShowPrices",
        description="Get customer show prices",
    )
    def get(self, customer_id):
        return ShowPricesStruct(
            enabled=CustomerModel.get_by_id(customer_id=customer_id, only=["show_prices"]).show_prices
        )


@ns.route("/<customer_id>/approve")
class ApproveCustomerSelfRegistration(Resource):
    @authenticated
    @requires_vco_admin
    @ns.doc(shortcut="approveSelfRegisteredCustomer", description="Approve Self Registered Customer")
    def put(self, customer_id):
        approve_self_registered_customer(customer_id)


@ns.route("/<customer_id>/payment-terms")
class VCOPaymentTerms(Resource):
    @authenticated
    @ns.doc(shortcut="getVCOPaymentTerms", description="Get customer payment terms")
    @ns.marshal_with(PaymentTermsStruct.model(api))
    @requires_vco_admin
    def get(self, customer_id):
        return get_payment_terms(
            seller_role=BillingRole.VCO,
            seller_id=get_vco_id(),
            buyer_role=BillingRole.CUSTOMER,
            buyer_id=customer_id,
        )

    @authenticated
    @ns.doc(shortcut="setVCOPaymentTerms", description="Update customer payment terms")
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.expect(PaymentTermsStruct.model(api))
    def put(self, customer_id):
        set_payment_terms(
            seller_role=BillingRole.VCO,
            seller_id=get_vco_id(),
            buyer_role=BillingRole.CUSTOMER,
            buyer_id=customer_id,
            payment_terms=PaymentTermsStruct.load(ns.payload),
        )
        return SUCCESS


@ns.route("/<customer_id>/dns/ds-records/<domain>")
class CustomerDSRecord(Resource):
    @authenticated
    @ns.doc(shortcut="getCustomerDsRecord", description="Get customer DS records")
    @ns.marshal_with(DnsDSRecord.model(api))
    @requires_custom(is_customer_org_admin)
    def get(self, customer_id, domain):
        return get_ds_key(domain)


@alpha_ns.route("/customers/<customer_id>/locations/<location>/backup-targets")
class CustomerLocationBackupTargets(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(targets_parser)
    @ns.marshal_with(get_pagination_model("ListBackupTargets", LocationTargetStruct.model(api)))
    @ns.doc(shortcut="listLocationCustomerBackupTargets", description="List backup targets on a specific location")
    def get(self, customer_id, location):
        jwt = get_current_user_info().jwt
        args = targets_parser.parse_args()
        return pagination_handler(
            backup_business.list_location_backup_targets,
            limit=args.get("limit"),
            start_after=args.get("start_after"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
            jwt=jwt,
            customer_id=customer_id,
            location=location,
        )


@alpha_ns.route("/customers/<customer_id>/locations/<location>/backup-policies")
class CustomerLocationBackupPolicies(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(targets_parser)
    @ns.marshal_with(get_pagination_model("ListBackupPolicies", LocationPolicyStruct.model(api)))
    @ns.doc(shortcut="listLocationCustomerBackupPolicies", description="List backup Policies on a specific location")
    def get(self, customer_id, location):
        jwt = get_current_user_info().jwt
        args = targets_parser.parse_args()
        return pagination_handler(
            backup_business.list_location_backup_policies,
            limit=args.get("limit"),
            start_after=args.get("start_after"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
            jwt=jwt,
            customer_id=customer_id,
            location=location,
        )


@alpha_ns.route("/customers/<customer_id>/locations/<location>/backup-targets/<target_id>/sync")
class CustomerLocationBackupSyncTarget(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="syncCustomerBackupTargetOnLocation", description="Sync backup target on location")
    def post(self, customer_id, target_id, location):
        jwt = get_current_user_info().jwt
        backup_business.sync_backup_target_on_location(jwt, location, target_id)
        return SUCCESS


@alpha_ns.route("/<customer_id>/vdi-profiles")
class VDIProfilesAPIs(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(VDIProfileStruct.list_model(api))
    @ns.doc(shortcut="listCustomerVDIProfiles", description="List VDI profiles for a customer")
    def get(self, customer_id):
        return {"result": VDIProfile.list(customer_id=customer_id)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(VDIProfileCreateStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createCustomerVDIProfile", description="Create VDI profile")
    def post(self, customer_id):
        payload = from_dict_with_enum(VDIProfileCreateStruct, ns.payload)
        vco_id = get_vco_id()
        result = vdi_business.create_vdi_profile(
            customer_id=customer_id, payload=payload, vco_id=vco_id
        )
        return IdModelStruct(result["id"])


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/standby-transitions/<transition_id>")
class VDIStandbyTransition(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)  # or a dedicated check_profile_ownership if you have it
    @ns.marshal_with(VDIStandbyTransitionLogs.model(api))
    @ns.doc(shortcut="GetVDIStandbyTransition", description="Get VDI Standby (pool build) transition by id")
    def get(self, customer_id, profile_id, transition_id):
        return VDIProfile.get_transition_by_id(customer_id, profile_id, transition_id)


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/standby-transitions/<transition_id>/logs")
class VDIStandbyTransitionLogsAPI(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.doc("GetVDIStandbyLogs", description="Get logs for VDI standby (pool build) transition")
    @ns.marshal_with(TransitionLogsStruct.list_model(api))
    @ns.expect(logs_parser)
    def get(self, customer_id, profile_id, transition_id):
        args = logs_parser.parse_args()
        include_debug_logs = args.get("include_debug_logs")
        return dict(
            result=vdi_business.get_standby_pipeline_logs(customer_id, profile_id, transition_id, include_debug_logs)
        )


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>")
class VDIProfileAPIs(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(VDIProfileStruct.model(api))
    @ns.doc(shortcut="getCustomerVDIProfile", description="Get VDI profiles of a customer")
    def get(self, customer_id, profile_id):
        return VDIProfile.get_by_id(customer_id=customer_id, profile_id=profile_id)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(VDIProfileUpdateStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateCustomerVDIProfile", description="Update VDI profiles for a customer")
    def put(self, customer_id, profile_id):
        payload = from_dict(VDIProfileUpdateStruct, ns.payload)
        return vdi_business.update_vdi_profile(customer_id=customer_id, profile_id=profile_id, payload=payload)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteCustomerVDIProfile", description="Delete VDI profiles for a customer")
    def delete(self, customer_id, profile_id):
        return vdi_business.delete_vdi_profile(customer_id=customer_id, profile_id=profile_id)


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/service-account/<service_account_id>")
class VDIProfileServiceAccountAPIs(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateVDIProfileServiceAccount", description="Update service account for VDI profile")
    def put(self, customer_id, profile_id, service_account_id):
        vdi_business.update_vdi_profile_service_account(
            customer_id=customer_id, profile_id=profile_id, new_service_account_id=service_account_id
        )
        return SUCCESS


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/roles")
class VDIProfileRolesAPIs(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(CustomerRoleStruct.list_model())
    @ns.doc(shortcut="listCustomerVDIProfileRoles", description="List VDI profile roles")
    def get(self, customer_id, profile_id):
        return {"result": vdi_business.list_profile_roles(customer_id=customer_id, profile_id=profile_id)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.expect(grant_vdi_profile_role_parser)
    @ns.doc(shortcut="createCustomerVDIProfileRole", description="Create a new VDI profile role")
    def post(self, customer_id, profile_id):
        role_id = grant_vdi_profile_role_parser.parse_args().get("role_id")
        vdi_business.grant_vdi_profile_role(customer_id=customer_id, profile_id=profile_id, role_id=role_id)
        return SUCCESS


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/roles/<role_id>")
class VDIProfileRoleAPIs(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(CustomerRoleStruct.model())
    @ns.doc(shortcut="getCustomerVDIProfileRole", description="Get VDI profile role")
    def get(self, customer_id, profile_id, role_id):
        return vdi_business.get_profile_role(customer_id=customer_id, profile_id=profile_id, role_id=role_id)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteCustomerVDIProfileRole", description="Delete VDI profile role")
    def delete(self, customer_id, profile_id, role_id):
        vdi_business.revoke_vdi_profile_role(customer_id=customer_id, profile_id=profile_id, role_id=role_id)
        return SUCCESS


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/sessions")
class VDIProfileSessionsAPIs(Resource):
    @authenticated
    @ns.expect(list_sessions_parser)
    @ns.marshal_with(ListVDISessionsStruct.list_model(api))
    @ns.doc(shortcut="listCustomerVDIProfileSessions", description="List VDI profile sessions")
    def get(self, customer_id, profile_id):
        filters = list_sessions_parser.parse_args()
        return {"result": VDISession.list(customer_id, profile_id, filters)}

    @authenticated
    @ns.expect(VDISessionCreateStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createCustomerVDIProfileSession", description="Create VDI profile session")
    def post(self, customer_id, profile_id):
        payload = from_dict(VDISessionCreateStruct, ns.payload)
        user_info = get_current_user_info()
        vco_id = get_vco_id()
        return {
            "id": vdi_business.create_vdi_profile_session(
                customer_id=customer_id,
                profile_id=profile_id,
                user_info=user_info,
                public_key=payload.public_key,
                vco_id=vco_id,
            )
        }


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/sessions/<session_id>")
class VDIProfileSessionAPIs(Resource):
    @authenticated
    @ns.marshal_with(VDISessionStruct.model(api))
    @ns.doc(shortcut="getVDIProfileSession", description="Get VDI profile session")
    def get(self, customer_id, profile_id, session_id):
        return VDISession.get_by_id(customer_id, profile_id, session_id)

    @authenticated
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteVDIProfileSession", description="Delete VDI profile session")
    def delete(self, customer_id, profile_id, session_id):
        doc = VDISession.get_by_id(customer_id, profile_id, session_id)
        if doc:
            doc.delete()
        return SUCCESS


@alpha_ns.route("/<customer_id>/vdi-profiles/<profile_id>/sessions/<session_id>/status")
class VDIProfileSessionStatus(Resource):
    @authenticated
    @ns.marshal_with(VDISessionStatusStruct.model(api))
    @ns.doc(shortcut="getVDIProfileSessionStatus", description="Get VDI profile session status")
    def get(self, customer_id, profile_id, session_id):
        return {"status": VDISession.get_session_status(customer_id, profile_id, session_id).status}


@alpha_ns.route("/customers/<customer_id>/backup-targets")
class CustomerBackupTargets(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(BackupTargetsStruct.list_model(api))
    @ns.doc(shortcut="listCustomerBackupTargets", description="List backup targets")
    def get(self, customer_id):
        return {"result": Targets.list(vco_id=get_vco_id(), by_customer=True, customer_id=customer_id)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createCustomerBackupTarget", description="Create a new backup target")
    def post(self, customer_id):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        return {
            "id": backup_business.create_target(
                vco_id=get_vco_id(), data=target_data, by_customer=True, customer_id=customer_id
            )
        }


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>")
class CustomerBackupTarget(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(BackupTargetStruct.model(api))
    @ns.doc(shortcut="getCustomerBackupTarget", description="Get a backup target")
    def get(self, customer_id, target_id):
        return Targets.get_by_id(_id=target_id, customer_id=customer_id)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(BackupCreateTargetStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateCustomerBackupTarget", description="Update a backup target")
    def put(self, customer_id, target_id):
        target_data = from_dict(BackupCreateTargetStruct, ns.payload)
        backup_business.update_target(
            target_id=target_id, target_data=target_data, by_customer=True, vco_id=get_vco_id(), customer_id=customer_id
        )
        return SUCCESS

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteCustomerBackupTarget", description="Delete a backup target")
    def delete(self, customer_id, target_id):
        return backup_business.delete_target(target_id, customer_id)


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/subscribe")
class BackupSubscribeTarget(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(backup_location_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="subscribeCustomerBackupTarget", description="Subscribe backup target")
    def post(self, target_id, customer_id):
        args = backup_location_parser.parse_args()
        location = args.get("location")
        cloudspace_id = args.get("cloudspace_id")
        jwt = get_current_user_info().jwt
        backup_business.subscribe_backup_target(jwt, target_id, location, cloudspace_id, customer_id, get_vco_id())
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/unsubscribe")
class BackupUnsubscribeTarget(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(unsubscribe_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="unsubscribeCustomerBackupTarget", description="Unsubscribe backup target")
    def delete(self, target_id, customer_id):
        args = unsubscribe_parser.parse_args()
        location = args.get("location")
        jwt = get_current_user_info().jwt
        backup_business.unsubscribe_backup_target(jwt, target_id, location, customer_id)
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/sync")
class CustomerBackupSyncTarget(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(sync_backup_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="syncCustomerBackupTarget", description="Sync backup target")
    def post(self, customer_id, target_id):
        args = sync_backup_parser.parse_args()
        jwt = get_current_user_info().jwt
        location = args.get("location")
        backup_business.sync_backup_target(jwt, location, target_id, customer_id)
        return SUCCESS


@alpha_ns.route("/customers/<customer_id>/backup-policies")
class CustomerBackupPolicies(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(BackupPolicyListStruct.list_model(api))
    @ns.doc(shortcut="listCustomerBackupPolicies", description="List backup policies")
    def get(self, customer_id):
        return {"result": Policies.list(by_customer=True, customer_id=customer_id)}

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(BackupPolicyStruct.model(api))
    @ns.marshal_with(IdModelStruct.model(api))
    @ns.doc(shortcut="createCustomerBackupPolicy", description="Create a new backup Policy")
    def post(self, customer_id):
        policy_data = from_dict(BackupPolicyStruct, ns.payload)
        jwt = get_current_user_info().jwt
        return {
            "id": backup_business.create_policy(
                vco_id=get_vco_id(), policy_data=policy_data, by_customer=True, customer_id=customer_id, jwt=jwt
            )
        }


@alpha_ns.route("/customers/<customer_id>/backup-policies/<policy_id>")
class CustomerBackupPolicy(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(BackupPoliciesStruct.model(api))
    @ns.doc(shortcut="getCustomerBackupPolicy", description="Get a backup policy")
    def get(self, customer_id, policy_id):
        return Policies.get_by_id(_id=policy_id, customer_id=customer_id)

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(BackupPolicyStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="updateCustomerBackupPolicy", description="Update a backup policy")
    def put(self, customer_id, policy_id):
        policy_data = from_dict(BackupPolicyStruct, ns.payload)
        jwt = get_current_user_info().jwt
        backup_business.update_policy(
            jwt, get_vco_id(), policy_id, policy_data, by_customer=True, customer_id=customer_id
        )
        return SUCCESS

    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteCustomerBackupPolicy", description="Delete a backup policy")
    def delete(self, customer_id, policy_id):
        return backup_business.delete_policy(policy_id=policy_id, customer_id=customer_id)


@alpha_ns.route("/customers/<customer_id>/backups")
class CustomerBackups(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(list_backups_parser)
    @ns.marshal_with(get_pagination_model("ListBackups", BackupStruct.model(api)))
    @ns.doc(shortcut="listCustomerBackups", description="List backups")
    def get(self, customer_id):
        args = list_backups_parser.parse_args()
        location = args.get("location")
        vm_id = args.get("vm_id")
        policy = args.get("policy")
        cloudspace_id = args.get("cloudspace_id")
        target_id = args.get("target_id")
        status = args.get("status")
        g8_target_id = args.get("g8_target_id")
        jwt = get_current_user_info().jwt
        return pagination_handler(
            backup_business.list_backups,
            limit=args.get("limit"),
            start_after=args.get("start_after"),
            jwt=jwt,
            location=location,
            vm_id=vm_id,
            policy=policy,
            cloudspace_id=cloudspace_id,
            target_id=target_id,
            status=status,
            exclude_expired=args.get("exclude_expired"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
            g8_target_id=g8_target_id,
        )


@alpha_ns.route("/customers/<customer_id>/backups/<backup_id>")
class CustomerBackup(Resource):
    @authenticated
    @requires_custom(has_access_to_customer)
    @ns.expect(backup_parser)
    @ns.marshal_with(BackupWithVMStruct.model(api))
    @ns.doc(shortcut="getCustomerBackup", description="Get a backup")
    def get(self, customer_id, backup_id):
        jwt = get_current_user_info().jwt
        args = backup_parser.parse_args()
        location = args.get("location")
        return backup_business.get_backup(jwt, location, backup_id)


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/repository-overview")
class RepositoryOverview(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(VmFromBackupStruct.list_model(api))
    @ns.doc(shortcut="customerRepositoryOverview", description="Repository overview")
    def get(self, customer_id, target_id):
        jwt = get_current_user_info().jwt
        return backup_business.list_repository_vms(jwt, target_id, customer_id)


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/vms/<vm_id>")
class TargetVMs(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="customerDeleteVMBackups", description="Target vm details")
    def delete(self, customer_id, target_id, vm_id):
        jwt = get_current_user_info().jwt
        return backup_business.delete_vm_backups(jwt, target_id, vm_id=vm_id, customer_id=customer_id)


@alpha_ns.route("/customers/<customer_id>/backup-targets/<target_id>/vms/<vm_id>/backups")
class TargetVmBackups(Resource):
    @authenticated
    @requires_vco_admin
    @ns.marshal_with(BackupFullStruct.list_model(api))
    @ns.doc(shortcut="customerListTargetVMBackups", description="List VM backups")
    def get(self, customer_id, target_id, vm_id):
        jwt = get_current_user_info().jwt
        return backup_business.list_vm_backups(jwt, target_id, vm_id, customer_id)


@alpha_ns.route("/customers/<customer_id>/backups/workers")
class BackupWorkers(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(list_backup_workers_parser)
    @ns.marshal_with(get_pagination_model("BackupWorkerListStruct", BackupWorkerListStruct.model(api)))
    @ns.doc(shortcut="listBackupWorkers", description="List backup workers")
    def get(self, customer_id):
        jwt = get_current_user_info().jwt
        args = list_backup_workers_parser.parse_args()
        return pagination_handler(
            backup_business.list_backup_workers,
            limit=args.get("limit"),
            start_after=args.get("start_after"),
            sort_direction=args.get("sort_direction"),
            sort_by=args.get("sort_by"),
            customer_id=customer_id,
            jwt=jwt,
        )

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(BackupWorkerGetStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="createBackupWorker", description="Create backup worker")
    def post(self, customer_id):
        jwt = get_current_user_info().jwt
        worker_data = from_dict(BackupWorkerGetStruct, ns.payload)
        return backup_business.create_backup_worker(data=worker_data, customer_id=customer_id, jwt=jwt)


@alpha_ns.route("/customers/<customer_id>/backups/workers/<worker_id>")
class SingleBackupWorker(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(worker_location_parser)
    @ns.marshal_with(BackupWorkerListStruct.model(api))
    @ns.doc(shortcut="getBackupWorker", description="Get backup worker details")
    def get(self, customer_id, worker_id):
        args = worker_location_parser.parse_args()
        jwt = get_current_user_info().jwt
        return backup_business.get_backup_worker(
            customer_id=customer_id, worker_id=worker_id, jwt=jwt, location=args["location"]
        )

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(worker_location_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="deleteBackupWorker", description="Delete backup worker")
    def delete(self, customer_id, worker_id):
        jwt = get_current_user_info().jwt
        args = worker_location_parser.parse_args()
        return backup_business.delete_backup_worker(
            jwt=jwt, customer_id=customer_id, worker_id=worker_id, location=args["location"]
        )


@alpha_ns.route("/customers/<customer_id>/vdi-profiles/<profile_id>/vms/<vm_id>/status-report")
class VDIProfileStatusReport(Resource):
    @authenticated
    @ns.expect(status_report_parser)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="rdpStatusReport", description="List VDI profile sessions")
    def put(self, customer_id, profile_id, vm_id):
        status = status_report_parser.parse_args().get("status")
        vdi_business.update_vm_rdp_status(customer_id, profile_id, vm_id, RDPStatus.from_string(status))
        return SUCCESS

    @authenticated
    @ns.doc(shortcut="rdpStatusReportTest", description="List VDI profile sessions")
    def post(self, customer_id, profile_id, vm_id):
        vdi_business.deploy_status_reporter_config(customer_id, vm_id, profile_id)
        return SUCCESS


@alpha_ns.route("/customers/job-simulator")
class TestJob(Resource):
    @authenticated
    @ns.doc(shortcut="jobSimulator", description="Job simulator")
    def post(self):
        return vdi_business.recycle_disconnected_vdi_sessions()
